/*! For license information please see 06e16e094c4f1a7c.vendor.js.LICENSE.txt */
(self.webpackChunk_canva_web=self.webpackChunk_canva_web||[]).push([[98821],{1408:(e,t,r)=>{"use strict";r.d(t,{X:()=>n});var n=function(e){return e&&"number"==typeof e.length&&"function"!=typeof e}},7879:(e,t,r)=>{"use strict";function n(e,t,r){const n=r.getRegistry(),o=n.addSource(e,t);return[o,()=>n.removeSource(o)]}r.d(t,{A:()=>n})},10360:(e,t,r)=>{"use strict";r.d(t,{w:()=>i});var n=r(998083),o=r(335679);function i(){return function(e){return e.lift(new s)}}var s=function(){function e(){}return e.prototype.call=function(e,t){return t.subscribe(new a(e))},e}(),a=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n.C6(t,e),t.prototype._next=function(e){},t}(o.v)},11585:(e,t,r)=>{"use strict";function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}r.d(t,{A:()=>n})},13961:(e,t,r)=>{"use strict";r.d(t,{c:()=>s,h:()=>i});var n=r(230083),o=r(491577);function i(e){return new o.x(n.l[e])}function s(e){if(!e)return null;const t=Array.prototype.slice.call(e.types||[]);return Object.keys(n.l).filter((e=>{const{matchesTypes:r}=n.l[e];return r.some((e=>t.indexOf(e)>-1))}))[0]||null}},28971:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MINOR_VERSION=t.MAJOR_VERSION=void 0;t.MAJOR_VERSION=1;t.MINOR_VERSION=0},29732:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;class r{static of(e){return new r((t=>{t.onSubscribe(),t.onComplete(e)}))}static error(e){return new r((t=>{t.onSubscribe(),t.onError(e)}))}static never(){return new r((e=>{e.onSubscribe()}))}constructor(e){this._source=e}subscribe(e){const t=new n(e);try{this._source(t)}catch(r){t.onError(r)}}flatMap(e){return new r((t=>{let r;const n=()=>{r&&r(),r=null};this._source({onComplete:n=>{e(n).subscribe({onComplete:e=>{t.onComplete(e)},onError:e=>t.onError(e),onSubscribe:e=>{r=e}})},onError:e=>t.onError(e),onSubscribe:e=>{r=e,t.onSubscribe(n)}})}))}map(e){return new r((t=>this._source({onComplete:r=>t.onComplete(e(r)),onError:e=>t.onError(e),onSubscribe:e=>t.onSubscribe(e)})))}then(e,t){this.subscribe({onComplete:e||(()=>{}),onError:t||(()=>{})})}}t.default=r;class n{constructor(e){this._active=!1,this._started=!1,this._subscriber=e||{}}onComplete(e){if(this._active){this._active=!1,this._started=!0;try{this._subscriber.onComplete&&this._subscriber.onComplete(e)}catch(t){this._subscriber.onError&&this._subscriber.onError(t)}}else console.warn("Single: Invalid call to onComplete(): %s.",this._started?"onComplete/onError was already called":"onSubscribe has not been called")}onError(e){!this._started||this._active?(this._active=!1,this._started=!0,this._subscriber.onError&&this._subscriber.onError(e)):console.warn("Single: Invalid call to onError(): %s.",this._active?"onComplete/onError was already called":"onSubscribe has not been called")}onSubscribe(e){if(this._started)console.warn("Single: Invalid call to onSubscribe(): already called.");else{this._active=!0,this._started=!0;try{this._subscriber.onSubscribe&&this._subscriber.onSubscribe((()=>{this._active&&(this._active=!1,e&&e())}))}catch(t){this.onError(t)}}}}},36160:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var n=r(845212);const o=r(146462);function i(e,t,r){const[i,s]=(0,n.useState)((()=>t(e))),a=(0,n.useCallback)((()=>{const n=t(e);o(i,n)||(s(n),r&&r())}),[i,e,r]);return[i,a]}},37186:(e,t,r)=>{"use strict";r.d(t,{F:()=>o});var n=r(599884),o=function(e){return function(t){return e.then((function(e){t.closed||(t.next(e),t.complete())}),(function(e){return t.error(e)})).then(null,n.T),t}}},38173:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(390088);function o(e){return function(){if(e.getMonitor().isDragging())return{type:n.BS}}}},39716:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(799769),o=r(352754),i=r(511894);const s={itemType:null,item:null,sourceId:null,targetIds:[],dropResult:null,didDrop:!1,isSourcePublic:null};function a(e=s,t){const{payload:r}=t;switch(t.type){case n.Vw:return Object.assign({},e,{itemType:r.itemType,item:r.item,sourceId:r.sourceId,isSourcePublic:r.isSourcePublic,dropResult:null,didDrop:!1});case n.BS:return Object.assign({},e,{isSourcePublic:!0});case n.l6:return Object.assign({},e,{targetIds:r.targetIds});case o.v4:return-1===e.targetIds.indexOf(r.targetId)?e:Object.assign({},e,{targetIds:(0,i.FF)(e.targetIds,r.targetId)});case n.q2:return Object.assign({},e,{dropResult:r.dropResult,didDrop:!0,targetIds:[]});case n.dU:return Object.assign({},e,{itemType:null,item:null,sourceId:null,dropResult:null,didDrop:!1,isSourcePublic:null,targetIds:[]});default:return e}}},47911:(e,t,r)=>{"use strict";function n(e){return null!==e&&"object"==typeof e}r.d(t,{G:()=>n})},59329:(e,t,r)=>{"use strict";r.d(t,{r:()=>o});var n=r(998083),o=function(e){function t(t,r){return e.call(this)||this}return n.C6(t,e),t.prototype.schedule=function(e,t){return void 0===t&&(t=0),this},t}(r(997752).y)},68688:(e,t,r)=>{"use strict";r.d(t,{Hd:()=>o.H,V7:()=>i.V,i3:()=>n.i});var n=r(604057),o=r(641186),i=r(410576)},69858:(e,t,r)=>{"use strict";r.d(t,{Mv:()=>o,kV:()=>i,v2:()=>s});const n=r(408406);function o(e){n("function"==typeof e.canDrag,"Expected canDrag to be a function."),n("function"==typeof e.beginDrag,"Expected beginDrag to be a function."),n("function"==typeof e.endDrag,"Expected endDrag to be a function.")}function i(e){n("function"==typeof e.canDrop,"Expected canDrop to be a function."),n("function"==typeof e.hover,"Expected hover to be a function."),n("function"==typeof e.drop,"Expected beginDrag to be a function.")}function s(e,t){t&&Array.isArray(e)?e.forEach((e=>s(e,!1))):n("string"==typeof e||"symbol"==typeof e,t?"Type can only be a string, a symbol, or an array of either.":"Type can only be a string or a symbol.")}},72231:(e,t,r)=>{"use strict";function n(e){return!!e&&"function"!=typeof e.subscribe&&"function"==typeof e.then}r.d(t,{y:()=>n})},72715:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var n=r(366854),o=r(351702),i=r(546255),s=r(899474),a=r(162743),u=r(448151),c=r(231711),l=r(807148);const f=r(408406);function h(e,t,r,h={}){(0,n.A)("DropTarget","type, spec, collect[, options]",e,t,r,h);let d=e;"function"!=typeof e&&(f((0,a.A)(e,!0),'Expected "type" provided as the first argument to DropTarget to be a string, an array of strings, or a function that returns either given the current props. Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target',e),d=()=>e),f((0,l.Qd)(t),'Expected "spec" provided as the second argument to DropTarget to be a plain object. Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target',t);const p=(0,s.A)(t);return f("function"==typeof r,'Expected "collect" provided as the third argument to DropTarget to be a function that returns a plain object of props to inject. Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target',r),f((0,l.Qd)(h),'Expected "options" provided as the fourth argument to DropTarget to be a plain object when specified. Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target',r),function(e){return(0,o.A)({containerDisplayName:"DropTarget",createHandler:p,registerHandler:i.A,createMonitor:e=>new u.A(e),createConnector:e=>new c.A(e),DecoratedComponent:e,getType:d,collect:r,options:h})}}},73531:(e,t,r)=>{"use strict";r.d(t,{t:()=>o});var n=r(660264);function o(e,t,r){var o;return o=e&&"object"==typeof e?e:{bufferSize:e,windowTime:t,refCount:!1,scheduler:r},function(e){return e.lift(function(e){var t,r,o=e.bufferSize,i=void 0===o?Number.POSITIVE_INFINITY:o,s=e.windowTime,a=void 0===s?Number.POSITIVE_INFINITY:s,u=e.refCount,c=e.scheduler,l=0,f=!1,h=!1;return function(e){var o;l++,!t||f?(f=!1,t=new n.m(i,a,c),o=t.subscribe(this),r=e.subscribe({next:function(e){t.next(e)},error:function(e){f=!0,t.error(e)},complete:function(){h=!0,r=void 0,t.complete()}}),h&&(r=void 0)):o=t.subscribe(this),this.add((function(){l--,o.unsubscribe(),o=void 0,r&&!h&&u&&0===l&&(r.unsubscribe(),r=void 0,t=void 0)}))}}(o))}}},77808:(e,t,r)=>{"use strict";r.d(t,{g:()=>o});var n=r(554634),o=new(r(741199).d)(n.U)},78583:(e,t,r)=>{"use strict";r.d(t,{t:()=>s});var n=r(998083),o=r(450117),i=r(878796),s=function(e){function t(t){var r=e.call(this)||this;return r._value=t,r}return n.C6(t,e),Object.defineProperty(t.prototype,"value",{get:function(){return this.getValue()},enumerable:!0,configurable:!0}),t.prototype._subscribe=function(t){var r=e.prototype._subscribe.call(this,t);return r&&!r.closed&&t.next(this._value),r},t.prototype.getValue=function(){if(this.hasError)throw this.thrownError;if(this.closed)throw new i.P;return this._value},t.prototype.next=function(t){e.prototype.next.call(this,this._value=t)},t}(o.B7)},82537:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ResponderLeaseHandler=t.RequesterLeaseHandler=t.Leases=t.Lease=void 0;var n,o=(n=r(308552))&&n.__esModule?n:{default:n},i=r(914422),s=r(550558);function a(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class u{constructor(e,t,r){(0,o.default)(e>0,"Lease time-to-live must be positive"),(0,o.default)(t>0,"Lease allowed requests must be positive"),this.timeToLiveMillis=e,this.allowedRequests=t,this.startingAllowedRequests=t,this.expiry=Date.now()+e,this.metadata=r}expired(){return Date.now()>this.expiry}valid(){return this.allowedRequests>0&&!this.expired()}_use(){if(this.expired())return!1;const e=this.allowedRequests,t=e>0;return t&&(this.allowedRequests=e-1),t}}t.Lease=u;t.Leases=class{constructor(){a(this,"_sender",(()=>i.Flowable.never())),a(this,"_receiver",(e=>{}))}sender(e){return this._sender=e,this}receiver(e){return this._receiver=e,this}stats(e){return this._stats=e,this}};t.RequesterLeaseHandler=class{constructor(e){a(this,"_requestN",-1),e(new i.Flowable((e=>{this._subscriber?e.onError(new Error("only 1 subscriber is allowed")):this.isDisposed()?e.onComplete():(this._subscriber=e,e.onSubscribe({cancel:()=>{this.dispose()},request:t=>{if(t<=0&&e.onError(new Error(`request demand must be positive: ${t}`)),!this.isDisposed()){const e=this._requestN;this._onRequestN(e),this._requestN=Math.min(Number.MAX_SAFE_INTEGER,Math.max(0,e)+t)}}}))})))}use(){const e=this._lease;return!!e&&e._use()}errorMessage(){return c(this._lease)}receive(e){if(!this.isDisposed()){const t=e.ttl,r=e.requestCount,n=e.metadata;this._onLease(new u(t,r,n))}}availability(){const e=this._lease;return e&&e.valid()?e.allowedRequests/e.startingAllowedRequests:0}dispose(){if(!this._isDisposed){this._isDisposed=!0;const e=this._subscriber;e&&e.onComplete()}}isDisposed(){return this._isDisposed}_onRequestN(e){const t=this._lease,r=this._subscriber;e<0&&t&&r&&r.onNext(t)}_onLease(e){const t=this._subscriber,r=this._requestN-1;r>=0&&t&&t.onNext(e),this._requestN=Math.max(-1,r),this._lease=e}};function c(e){if(!e)return"Lease was not received yet";if(e.valid())return"Missing leases";{const t=e.expired(),r=e.allowedRequests;return`Missing leases. Expired: ${t.toString()}, allowedRequests: ${r}`}}t.ResponderLeaseHandler=class{constructor(e,t,r){this._leaseSender=e,this._stats=t,this._errorConsumer=r}use(){const e=this._lease,t=!!e&&e._use();return this._onStatsEvent(t),t}errorMessage(){return c(this._lease)}send(e){let t,r;return this._leaseSender(this._stats).subscribe({onComplete:()=>this._onStatsEvent(),onError:e=>{this._onStatsEvent();const t=this._errorConsumer;t&&t(e)},onNext:t=>{this._lease=t,e(t)},onSubscribe:e=>{r?e.cancel():(e.request(s.MAX_REQUEST_N),t=e)}}),{dispose(){r||(r=!0,this._onStatsEvent(),t&&t.cancel())},isDisposed:()=>r}}_onStatsEvent(e){const t=this._stats;if(t){const r=void 0===e?"Terminate":e?"Accept":"Reject";t.onEvent(r)}}}},84287:(e,t,r)=>{"use strict";r.d(t,{j:()=>o});var n=r(807148);class o{constructor(e){this.isDisposed=!1,this.action=(0,n.Tn)(e)?e:n.lQ}static isDisposable(e){return e&&(0,n.Tn)(e.dispose)}static _fixup(e){return o.isDisposable(e)?e:o.empty}static create(e){return new o(e)}dispose(){this.isDisposed||(this.action(),this.isDisposed=!0)}}o.empty={dispose:n.lQ}},84516:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=class{constructor(e,t){this._fn=t,this._subscriber=e,this._subscription=null}onComplete(){this._subscriber.onComplete()}onError(e){this._subscriber.onError(e)}onNext(e){try{this._subscriber.onNext(this._fn(e))}catch(t){if(!this._subscription)throw new Error("subscription is null");this._subscription.cancel(),this._subscriber.onError(t)}}onSubscribe(e){this._subscription=e,this._subscriber.onSubscribe(e)}}},88085:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Snapshot=t.Variable=void 0;class r{constructor(e){this.name=(null==e?void 0:e.name)||"",this.defaultValue=null==e?void 0:e.defaultValue}get(){return this.defaultValue}run(e,t,...r){return t.call(null,...r)}}t.Variable=r,r.isNoop=!0;class n{run(e,...t){return e.call(null,...t)}static wrap(e){return e}}t.Snapshot=n,n.isNoop=!0},88173:(e,t,r)=>{"use strict";r.d(t,{u:()=>s});var n=r(845212),o=r(523173);const i=r(408406);function s(){const{dragDropManager:e}=(0,n.useContext)(o._O);return i(null!=e,"Expected drag drop context"),e}},88647:(e,t,r)=>{"use strict";function n(e=0){return e+1}r.d(t,{A:()=>n})},110676:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=s(r(84516)),o=s(r(189739)),i=s(r(521390));function s(e){return e&&e.__esModule?e:{default:e}}function a(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class u{static just(...e){return new u((t=>{let r=!1,n=0;t.onSubscribe({cancel:()=>{r=!0},request:o=>{for(;!r&&o>0&&n<e.length;)t.onNext(e[n++]),o--;r||n!=e.length||t.onComplete()}})}))}static error(e){return new u((t=>{t.onSubscribe({cancel:()=>{},request:()=>{t.onError(e)}})}))}static never(){return new u((e=>{e.onSubscribe({cancel:()=>{},request:()=>{}})}))}constructor(e,t=Number.MAX_SAFE_INTEGER){this._max=t,this._source=e}subscribe(e){let t;t="function"==typeof e?this._wrapCallback(e):e;const r=new c(t,this._max);this._source(r)}lift(e){return new u((t=>this._source(e(t))))}map(e){return this.lift((t=>new n.default(t,e)))}take(e){return this.lift((t=>new o.default(t,e)))}_wrapCallback(e){const t=this._max;return{onNext:e,onSubscribe(e){e.request(t)}}}}t.default=u;class c{constructor(e,t){a(this,"_cancel",(()=>{this._active&&(this._active=!1,this._subscription&&this._subscription.cancel())})),a(this,"_request",(e=>{(0,i.default)(Number.isInteger(e)&&e>=1&&e<=this._max,"Flowable: Expected request value to be an integer with a value greater than 0 and less than or equal to %s, got `%s`.",this._max,e),this._active&&(e===this._max?this._pending=this._max:(this._pending+=e,this._pending>=this._max&&(this._pending=this._max)),this._subscription&&this._subscription.request(e))})),this._active=!1,this._max=t,this._pending=0,this._started=!1,this._subscriber=e||{},this._subscription=null}onComplete(){if(this._active){this._active=!1,this._started=!0;try{this._subscriber.onComplete&&this._subscriber.onComplete()}catch(e){this._subscriber.onError&&this._subscriber.onError(e)}}else console.warn("Flowable: Invalid call to onComplete(): %s.",this._started?"onComplete/onError was already called":"onSubscribe has not been called")}onError(e){!this._started||this._active?(this._active=!1,this._started=!0,this._subscriber.onError&&this._subscriber.onError(e)):console.warn("Flowable: Invalid call to onError(): %s.",this._active?"onComplete/onError was already called":"onSubscribe has not been called")}onNext(e){if(this._active)if(0!==this._pending){this._pending!==this._max&&this._pending--;try{this._subscriber.onNext&&this._subscriber.onNext(e)}catch(t){this._subscription&&this._subscription.cancel(),this.onError(t)}}else console.warn("Flowable: Invalid call to onNext(), all request()ed values have been published.");else console.warn("Flowable: Invalid call to onNext(): %s.",this._active?"onComplete/onError was already called":"onSubscribe has not been called")}onSubscribe(e){if(this._started)console.warn("Flowable: Invalid call to onSubscribe(): already called.");else{this._active=!0,this._started=!0,this._subscription=e;try{this._subscriber.onSubscribe&&this._subscriber.onSubscribe({cancel:this._cancel,request:this._request})}catch(t){this.onError(t)}}}}},120399:(e,t,r)=>{"use strict";r.d(t,{Hd:()=>a.Hd,I4:()=>i.A,JY:()=>n.JY,Mf:()=>o.A,Tl:()=>s.A,V7:()=>a.V7,i3:()=>a.i3});var n=r(523173),o=r(897e3),i=r(385560),s=r(72715),a=(r(799716),r(68688))},124105:(e,t,r)=>{"use strict";r.d(t,{x:()=>s});var n=r(226260),o=r(997752),i=r(977305);function s(e,t){return new n.c((function(r){var n=new o.y;return n.add(t.schedule((function(){var o=e[i.s]();n.add(o.subscribe({next:function(e){n.add(t.schedule((function(){return r.next(e)})))},error:function(e){n.add(t.schedule((function(){return r.error(e)})))},complete:function(){n.add(t.schedule((function(){return r.complete()})))}}))}))),n}))}},125e3:(e,t,r)=>{"use strict";r.d(t,{P:()=>c,k:()=>l});var n=r(845212),o=r(7879),i=r(88173),s=r(446246),a=r(221963);const u=r(408406);function c(){const e=(0,i.u)();return[(0,n.useMemo)((()=>new s.A(e)),[e]),(0,n.useMemo)((()=>new a.A(e.getBackend())),[e])]}function l(e,t,r){const s=(0,i.u)(),a=(0,n.useMemo)((()=>({beginDrag(){const{begin:r,item:n}=e.current;if(r){const e=r(t);return u(null==e||"object"==typeof e,"dragSpec.begin() must either return an object, undefined, or null"),e||n||{}}return n||{}},canDrag:()=>"boolean"==typeof e.current.canDrag?e.current.canDrag:"function"!=typeof e.current.canDrag||e.current.canDrag(t),isDragging(r,n){const{isDragging:o}=e.current;return o?o(t):n===r.getSourceId()},endDrag(){const{end:n}=e.current;n&&n(t.getItem(),t),r.reconnect()}})),[]);(0,n.useEffect)((function(){const[n,i]=(0,o.A)(e.current.item.type,a,s);return t.receiveHandlerId(n),r.receiveHandlerId(n),i}),[])}},139520:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var n=r(845212),o=r(36160);function i(e,t,r){const[i,s]=(0,o.F)(e,t,r);return(0,n.useEffect)((function(){const t=e.getHandlerId();if(null!=t)return e.subscribeToStateChange(s,{handlerIds:[t]})}),[e,s]),i}},142771:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(352754);function o(e=0,t){switch(t.type){case n.Yd:case n.SO:return e+1;case n.n_:case n.v4:return e-1;default:return e}}},146462:e=>{e.exports=function(e,t,r,n){var o=r?r.call(n,e,t):void 0;if(void 0!==o)return!!o;if(e===t)return!0;if("object"!=typeof e||!e||"object"!=typeof t||!t)return!1;var i=Object.keys(e),s=Object.keys(t);if(i.length!==s.length)return!1;for(var a=Object.prototype.hasOwnProperty.bind(t),u=0;u<i.length;u++){var c=i[u];if(!a(c))return!1;var l=e[c],f=t[c];if(!1===(o=r?r.call(n,l,f,c):void 0)||void 0===o&&l!==f)return!1}return!0}},148676:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.enableAsyncContextRuntime=void 0;t.enableAsyncContextRuntime=null!=globalThis.flags&&!0===globalThis.flags.a845bec5||(()=>{var e,t,r;if(!(null===(e=null===globalThis||void 0===globalThis?void 0:globalThis.flags)||void 0===e?void 0:e.fe634b10))return!1;const n=null===(r=null===(t=null===globalThis||void 0===globalThis?void 0:globalThis.location)||void 0===t?void 0:t.search)||void 0===r?void 0:r.substring(1),o="enableAsyncContextRuntime",i=null==n?void 0:n.split("&"),s=null==i?void 0:i.find((e=>{const[t]=e.split("=");if(t===o)return!0;const r=t.match(/^flags?\.(.+)$/);return(null==r?void 0:r[1])===o}));if(void 0===s)return!1;return"false"!==s.split("=")[1]})()},153181:(e,t,r)=>{"use strict";r.d(t,{j:()=>s});var n=r(998083),o=r(335679),i=r(997752);function s(e){return function(t){return t.lift(new a(e))}}var a=function(){function e(e){this.callback=e}return e.prototype.call=function(e,t){return t.subscribe(new u(e,this.callback))},e}(),u=function(e){function t(t,r){var n=e.call(this,t)||this;return n.add(new i.y(r)),n}return n.C6(t,e),t}(o.v)},162743:(e,t,r)=>{"use strict";function n(e,t){return"string"==typeof e||"symbol"==typeof e||!!t&&Array.isArray(e)&&e.every((e=>n(e,!1)))}r.d(t,{A:()=>n})},162773:(e,t,r)=>{"use strict";r.d(t,{n:()=>a});var n=r(998083),o=r(535921),i=r(212943),s=r(914941);function a(e,t){return"function"==typeof t?function(r){return r.pipe(a((function(r,n){return(0,i.H)(e(r,n)).pipe((0,o.T)((function(e,o){return t(r,e,n,o)})))})))}:function(t){return t.lift(new u(e))}}var u=function(){function e(e){this.project=e}return e.prototype.call=function(e,t){return t.subscribe(new c(e,this.project))},e}(),c=function(e){function t(t,r){var n=e.call(this,t)||this;return n.project=r,n.index=0,n}return n.C6(t,e),t.prototype._next=function(e){var t,r=this.index++;try{t=this.project(e,r)}catch(n){return void this.destination.error(n)}this._innerSub(t)},t.prototype._innerSub=function(e){var t=this.innerSubscription;t&&t.unsubscribe();var r=new s.zA(this),n=this.destination;n.add(r),this.innerSubscription=(0,s.tS)(e,r),this.innerSubscription!==r&&n.add(this.innerSubscription)},t.prototype._complete=function(){var t=this.innerSubscription;t&&!t.closed||e.prototype._complete.call(this),this.unsubscribe()},t.prototype._unsubscribe=function(){this.innerSubscription=void 0},t.prototype.notifyComplete=function(){this.innerSubscription=void 0,this.isStopped&&e.prototype._complete.call(this)},t.prototype.notifyNext=function(e){this.destination.next(e)},t}(s.gn)},168287:(e,t,r)=>{"use strict";r.d(t,{P:()=>i});var n=r(226260),o=r(997752);function i(e,t){return new n.c((function(r){var n=new o.y;return n.add(t.schedule((function(){return e.then((function(e){n.add(t.schedule((function(){r.next(e),n.add(t.schedule((function(){return r.complete()})))})))}),(function(e){n.add(t.schedule((function(){return r.error(e)})))}))}))),n}))}},178665:(e,t,r)=>{"use strict";var n;r.d(t,{z:()=>n}),function(e){e.SOURCE="SOURCE",e.TARGET="TARGET"}(n||(n={}))},179210:(e,t,r)=>{"use strict";r.d(t,{H:()=>o});var n=r(335679);function o(e){for(;e;){var t=e,r=t.closed,o=t.destination,i=t.isStopped;if(r||i)return!1;e=o&&o instanceof n.v?o:null}return!0}},189739:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=class{constructor(e,t){this._subscriber=e,this._subscription=null,this._toTake=t}onComplete(){this._subscriber.onComplete()}onError(e){this._subscriber.onError(e)}onNext(e){try{this._subscriber.onNext(e),0==--this._toTake&&this._cancelAndComplete()}catch(t){if(!this._subscription)throw new Error("subscription is null");this._subscription.cancel(),this._subscriber.onError(t)}}onSubscribe(e){this._subscription=e,this._subscriber.onSubscribe(e),this._toTake<=0&&this._cancelAndComplete()}_cancelAndComplete(){if(!this._subscription)throw new Error("subscription is null");this._subscription.cancel(),this._subscriber.onComplete()}}},189859:(e,t,r)=>{"use strict";r.d(t,{V:()=>c,y:()=>u});var n=r(845212),o=r(546255),i=r(88173),s=r(231711),a=r(448151);function u(){const e=(0,i.u)();return[(0,n.useMemo)((()=>new a.A(e)),[e]),(0,n.useMemo)((()=>new s.A(e.getBackend())),[e])]}function c(e,t,r){const s=(0,i.u)(),a=(0,n.useMemo)((()=>({canDrop(){const{canDrop:r}=e.current;return!r||r(t.getItem(),t)},hover(){const{hover:r}=e.current;r&&r(t.getItem(),t)},drop(){const{drop:r}=e.current;if(r)return r(t.getItem(),t)}})),[t]);(0,n.useEffect)((function(){const[n,i]=(0,o.A)(e.current.accept,a,s);return t.receiveHandlerId(n),r.receiveHandlerId(n),i}),[t,r])}},190418:(e,t,r)=>{"use strict";r.d(t,{X:()=>o});var n=r(977305),o=function(e){return function(t){var r=e[n.s]();if("function"!=typeof r.subscribe)throw new TypeError("Provided object does not correctly implement Symbol.observable");return r.subscribe(t)}}},192897:(e,t,r)=>{var n;!function(){function o(e,t,r){return e.call.apply(e.bind,arguments)}function i(e,t,r){if(!e)throw Error();if(2<arguments.length){var n=Array.prototype.slice.call(arguments,2);return function(){var r=Array.prototype.slice.call(arguments);return Array.prototype.unshift.apply(r,n),e.apply(t,r)}}return function(){return e.apply(t,arguments)}}function s(e,t,r){return(s=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?o:i).apply(null,arguments)}var a=Date.now||function(){return+new Date};function u(e,t){this.a=e,this.o=t||e,this.c=this.o.document}var c=!!window.FontFace;function l(e,t,r,n){if(t=e.c.createElement(t),r)for(var o in r)r.hasOwnProperty(o)&&("style"==o?t.style.cssText=r[o]:t.setAttribute(o,r[o]));return n&&t.appendChild(e.c.createTextNode(n)),t}function f(e,t,r){(e=e.c.getElementsByTagName(t)[0])||(e=document.documentElement),e.insertBefore(r,e.lastChild)}function h(e){e.parentNode&&e.parentNode.removeChild(e)}function d(e,t,r){t=t||[],r=r||[];for(var n=e.className.split(/\s+/),o=0;o<t.length;o+=1){for(var i=!1,s=0;s<n.length;s+=1)if(t[o]===n[s]){i=!0;break}i||n.push(t[o])}for(t=[],o=0;o<n.length;o+=1){for(i=!1,s=0;s<r.length;s+=1)if(n[o]===r[s]){i=!0;break}i||t.push(n[o])}e.className=t.join(" ").replace(/\s+/g," ").replace(/^\s+|\s+$/,"")}function p(e,t){for(var r=e.className.split(/\s+/),n=0,o=r.length;n<o;n++)if(r[n]==t)return!0;return!1}function y(e,t,r){function n(){a&&o&&i&&(a(s),a=null)}t=l(e,"link",{rel:"stylesheet",href:t,media:"all"});var o=!1,i=!0,s=null,a=r||null;c?(t.onload=function(){o=!0,n()},t.onerror=function(){o=!0,s=Error("Stylesheet failed to load"),n()}):setTimeout((function(){o=!0,n()}),0),f(e,"head",t)}function b(e,t,r,n){var o=e.c.getElementsByTagName("head")[0];if(o){var i=l(e,"script",{src:t}),s=!1;return i.onload=i.onreadystatechange=function(){s||this.readyState&&"loaded"!=this.readyState&&"complete"!=this.readyState||(s=!0,r&&r(null),i.onload=i.onreadystatechange=null,"HEAD"==i.parentNode.tagName&&o.removeChild(i))},o.appendChild(i),setTimeout((function(){s||(s=!0,r&&r(Error("Script load timeout")))}),n||5e3),i}return null}function g(){this.a=0,this.c=null}function v(e){return e.a++,function(){e.a--,E(e)}}function m(e,t){e.c=t,E(e)}function E(e){0==e.a&&e.c&&(e.c(),e.c=null)}function _(e){this.a=e||"-"}function O(e,t){this.c=e,this.f=4,this.a="n";var r=(t||"n4").match(/^([nio])([1-9])$/i);r&&(this.a=r[1],this.f=parseInt(r[2],10))}function S(e){var t=[];e=e.split(/,\s*/);for(var r=0;r<e.length;r++){var n=e[r].replace(/['"]/g,"");-1!=n.indexOf(" ")||/^\d/.test(n)?t.push("'"+n+"'"):t.push(n)}return t.join(",")}function w(e){return e.a+e.f}function T(e){var t="normal";return"o"===e.a?t="oblique":"i"===e.a&&(t="italic"),t}function A(e){var t=4,r="n",n=null;return e&&((n=e.match(/(normal|oblique|italic)/i))&&n[1]&&(r=n[1].substr(0,1).toLowerCase()),(n=e.match(/([1-9]00|normal|bold)/i))&&n[1]&&(/bold/i.test(n[1])?t=7:/[1-9]00/.test(n[1])&&(t=parseInt(n[1].substr(0,1),10)))),r+t}function N(e,t){this.c=e,this.f=e.o.document.documentElement,this.h=t,this.a=new _("-"),this.j=!1!==t.events,this.g=!1!==t.classes}function P(e){if(e.g){var t=p(e.f,e.a.c("wf","active")),r=[],n=[e.a.c("wf","loading")];t||r.push(e.a.c("wf","inactive")),d(e.f,r,n)}I(e,"inactive")}function I(e,t,r){e.j&&e.h[t]&&(r?e.h[t](r.c,w(r)):e.h[t]())}function R(){this.c={}}function C(e,t){this.c=e,this.f=t,this.a=l(this.c,"span",{"aria-hidden":"true"},this.f)}function j(e){f(e.c,"body",e.a)}function M(e){return"display:block;position:absolute;top:-9999px;left:-9999px;font-size:300px;width:auto;height:auto;line-height:normal;margin:0;padding:0;font-variant:normal;white-space:nowrap;font-family:"+S(e.c)+";font-style:"+T(e)+";font-weight:"+e.f+"00;"}function x(e,t,r,n,o,i){this.g=e,this.j=t,this.a=n,this.c=r,this.f=o||3e3,this.h=i||void 0}function D(e,t,r,n,o,i,s){this.v=e,this.B=t,this.c=r,this.a=n,this.s=s||"BESbswy",this.f={},this.w=o||3e3,this.u=i||null,this.m=this.j=this.h=this.g=null,this.g=new C(this.c,this.s),this.h=new C(this.c,this.s),this.j=new C(this.c,this.s),this.m=new C(this.c,this.s),e=M(e=new O(this.a.c+",serif",w(this.a))),this.g.a.style.cssText=e,e=M(e=new O(this.a.c+",sans-serif",w(this.a))),this.h.a.style.cssText=e,e=M(e=new O("serif",w(this.a))),this.j.a.style.cssText=e,e=M(e=new O("sans-serif",w(this.a))),this.m.a.style.cssText=e,j(this.g),j(this.h),j(this.j),j(this.m)}_.prototype.c=function(e){for(var t=[],r=0;r<arguments.length;r++)t.push(arguments[r].replace(/[\W_]+/g,"").toLowerCase());return t.join(this.a)},x.prototype.start=function(){var e=this.c.o.document,t=this,r=a(),n=new Promise((function(n,o){!function i(){a()-r>=t.f?o():e.fonts.load(function(e){return T(e)+" "+e.f+"00 300px "+S(e.c)}(t.a),t.h).then((function(e){1<=e.length?n():setTimeout(i,25)}),(function(){o()}))}()})),o=null,i=new Promise((function(e,r){o=setTimeout(r,t.f)}));Promise.race([i,n]).then((function(){o&&(clearTimeout(o),o=null),t.g(t.a)}),(function(){t.j(t.a)}))};var L={D:"serif",C:"sans-serif"},k=null;function U(){if(null===k){var e=/AppleWebKit\/([0-9]+)(?:\.([0-9]+))/.exec(window.navigator.userAgent);k=!!e&&(536>parseInt(e[1],10)||536===parseInt(e[1],10)&&11>=parseInt(e[2],10))}return k}function F(e,t,r){for(var n in L)if(L.hasOwnProperty(n)&&t===e.f[L[n]]&&r===e.f[L[n]])return!0;return!1}function B(e){var t,r=e.g.a.offsetWidth,n=e.h.a.offsetWidth;(t=r===e.f.serif&&n===e.f["sans-serif"])||(t=U()&&F(e,r,n)),t?a()-e.A>=e.w?U()&&F(e,r,n)&&(null===e.u||e.u.hasOwnProperty(e.a.c))?q(e,e.v):q(e,e.B):function(e){setTimeout(s((function(){B(this)}),e),50)}(e):q(e,e.v)}function q(e,t){setTimeout(s((function(){h(this.g.a),h(this.h.a),h(this.j.a),h(this.m.a),t(this.a)}),e),0)}function V(e,t,r){this.c=e,this.a=t,this.f=0,this.m=this.j=!1,this.s=r}D.prototype.start=function(){this.f.serif=this.j.a.offsetWidth,this.f["sans-serif"]=this.m.a.offsetWidth,this.A=a(),B(this)};var Y=null;function G(e){0==--e.f&&e.j&&(e.m?((e=e.a).g&&d(e.f,[e.a.c("wf","active")],[e.a.c("wf","loading"),e.a.c("wf","inactive")]),I(e,"active")):P(e.a))}function H(e){this.j=e,this.a=new R,this.h=0,this.f=this.g=!0}function z(e,t,r,n,o){var i=0==--e.h;(e.f||e.g)&&setTimeout((function(){var e=o||null,a=n||{};if(0===r.length&&i)P(t.a);else{t.f+=r.length,i&&(t.j=i);var u,c=[];for(u=0;u<r.length;u++){var l=r[u],f=a[l.c],h=t.a,p=l;if(h.g&&d(h.f,[h.a.c("wf",p.c,w(p).toString(),"loading")]),I(h,"fontloading",p),h=null,null===Y)if(window.FontFace){p=/Gecko.*Firefox\/(\d+)/.exec(window.navigator.userAgent);var y=/OS X.*Version\/10\..*Safari/.exec(window.navigator.userAgent)&&/Apple/.exec(window.navigator.vendor);Y=p?42<parseInt(p[1],10):!y}else Y=!1;h=Y?new x(s(t.g,t),s(t.h,t),t.c,l,t.s,f):new D(s(t.g,t),s(t.h,t),t.c,l,t.s,e,f),c.push(h)}for(u=0;u<c.length;u++)c[u].start()}}),0)}function K(e,t){this.c=e,this.a=t}function X(e,t){this.c=e,this.a=t}function $(e,t){this.c=e||W,this.a=[],this.f=[],this.g=t||""}V.prototype.g=function(e){var t=this.a;t.g&&d(t.f,[t.a.c("wf",e.c,w(e).toString(),"active")],[t.a.c("wf",e.c,w(e).toString(),"loading"),t.a.c("wf",e.c,w(e).toString(),"inactive")]),I(t,"fontactive",e),this.m=!0,G(this)},V.prototype.h=function(e){var t=this.a;if(t.g){var r=p(t.f,t.a.c("wf",e.c,w(e).toString(),"active")),n=[],o=[t.a.c("wf",e.c,w(e).toString(),"loading")];r||n.push(t.a.c("wf",e.c,w(e).toString(),"inactive")),d(t.f,n,o)}I(t,"fontinactive",e),G(this)},H.prototype.load=function(e){this.c=new u(this.j,e.context||this.j),this.g=!1!==e.events,this.f=!1!==e.classes,function(e,t,r){var n=[],o=r.timeout;!function(e){e.g&&d(e.f,[e.a.c("wf","loading")]),I(e,"loading")}(t);n=function(e,t,r){var n,o=[];for(n in t)if(t.hasOwnProperty(n)){var i=e.c[n];i&&o.push(i(t[n],r))}return o}(e.a,r,e.c);var i=new V(e.c,t,o);for(e.h=n.length,t=0,r=n.length;t<r;t++)n[t].load((function(t,r,n){z(e,i,t,r,n)}))}(this,new N(this.c,e),e)},K.prototype.load=function(e){function t(){if(i["__mti_fntLst"+n]){var r,o=i["__mti_fntLst"+n](),s=[];if(o)for(var a=0;a<o.length;a++){var u=o[a].fontfamily;null!=o[a].fontStyle&&null!=o[a].fontWeight?(r=o[a].fontStyle+o[a].fontWeight,s.push(new O(u,r))):s.push(new O(u))}e(s)}else setTimeout((function(){t()}),50)}var r=this,n=r.a.projectId,o=r.a.version;if(n){var i=r.c.o;b(this.c,(r.a.api||"https://fast.fonts.net/jsapi")+"/"+n+".js"+(o?"?v="+o:""),(function(o){o?e([]):(i["__MonotypeConfiguration__"+n]=function(){return r.a},t())})).id="__MonotypeAPIScript__"+n}else e([])},X.prototype.load=function(e){var t,r,n=this.a.urls||[],o=this.a.families||[],i=this.a.testStrings||{},s=new g;for(t=0,r=n.length;t<r;t++)y(this.c,n[t],v(s));var a=[];for(t=0,r=o.length;t<r;t++)if((n=o[t].split(":"))[1])for(var u=n[1].split(","),c=0;c<u.length;c+=1)a.push(new O(n[0],u[c]));else a.push(new O(n[0]));m(s,(function(){e(a,i)}))};var W="https://fonts.googleapis.com/css";function Q(e){this.f=e,this.a=[],this.c={}}var J={latin:"BESbswy","latin-ext":"çöüğş",cyrillic:"йяЖ",greek:"αβΣ",khmer:"កខគ",Hanuman:"កខគ"},Z={thin:"1",extralight:"2","extra-light":"2",ultralight:"2","ultra-light":"2",light:"3",regular:"4",book:"4",medium:"5","semi-bold":"6",semibold:"6","demi-bold":"6",demibold:"6",bold:"7","extra-bold":"8",extrabold:"8","ultra-bold":"8",ultrabold:"8",black:"9",heavy:"9",l:"3",r:"4",b:"7"},ee={i:"i",italic:"i",n:"n",normal:"n"},te=/^(thin|(?:(?:extra|ultra)-?)?light|regular|book|medium|(?:(?:semi|demi|extra|ultra)-?)?bold|black|heavy|l|r|b|[1-9]00)?(n|i|normal|italic)?$/;function re(e,t){this.c=e,this.a=t}var ne={Arimo:!0,Cousine:!0,Tinos:!0};function oe(e,t){this.c=e,this.a=t}function ie(e,t){this.c=e,this.f=t,this.a=[]}re.prototype.load=function(e){var t=new g,r=this.c,n=new $(this.a.api,this.a.text),o=this.a.families;!function(e,t){for(var r=t.length,n=0;n<r;n++){var o=t[n].split(":");3==o.length&&e.f.push(o.pop());var i="";2==o.length&&""!=o[1]&&(i=":"),e.a.push(o.join(i))}}(n,o);var i=new Q(o);!function(e){for(var t=e.f.length,r=0;r<t;r++){var n=e.f[r].split(":"),o=n[0].replace(/\+/g," "),i=["n4"];if(2<=n.length){var s;if(s=[],a=n[1])for(var a,u=(a=a.split(",")).length,c=0;c<u;c++){var l;if((l=a[c]).match(/^[\w-]+$/))if(null==(f=te.exec(l.toLowerCase())))l="";else{if(l=null==(l=f[2])||""==l?"n":ee[l],null==(f=f[1])||""==f)f="4";else var f=Z[f]||(isNaN(f)?"4":f.substr(0,1));l=[l,f].join("")}else l="";l&&s.push(l)}0<s.length&&(i=s),3==n.length&&(s=[],0<(n=(n=n[2])?n.split(","):s).length&&(n=J[n[0]])&&(e.c[o]=n))}for(e.c[o]||(n=J[o])&&(e.c[o]=n),n=0;n<i.length;n+=1)e.a.push(new O(o,i[n]))}}(i),y(r,function(e){if(0==e.a.length)throw Error("No fonts to load!");if(-1!=e.c.indexOf("kit="))return e.c;for(var t=e.a.length,r=[],n=0;n<t;n++)r.push(e.a[n].replace(/ /g,"+"));return t=e.c+"?family="+r.join("%7C"),0<e.f.length&&(t+="&subset="+e.f.join(",")),0<e.g.length&&(t+="&text="+encodeURIComponent(e.g)),t}(n),v(t)),m(t,(function(){e(i.a,i.c,ne)}))},oe.prototype.load=function(e){var t=this.a.id,r=this.c.o;t?b(this.c,(this.a.api||"https://use.typekit.net")+"/"+t+".js",(function(t){if(t)e([]);else if(r.Typekit&&r.Typekit.config&&r.Typekit.config.fn){t=r.Typekit.config.fn;for(var n=[],o=0;o<t.length;o+=2)for(var i=t[o],s=t[o+1],a=0;a<s.length;a++)n.push(new O(i,s[a]));try{r.Typekit.load({events:!1,classes:!1,async:!0})}catch(u){}e(n)}}),2e3):e([])},ie.prototype.load=function(e){var t=this.f.id,r=this.c.o,n=this;t?(r.__webfontfontdeckmodule__||(r.__webfontfontdeckmodule__={}),r.__webfontfontdeckmodule__[t]=function(t,r){for(var o=0,i=r.fonts.length;o<i;++o){var s=r.fonts[o];n.a.push(new O(s.name,A("font-weight:"+s.weight+";font-style:"+s.style)))}e(n.a)},b(this.c,(this.f.api||"https://f.fontdeck.com/s/css/js/")+function(e){return e.o.location.hostname||e.a.location.hostname}(this.c)+"/"+t+".js",(function(t){t&&e([])}))):e([])};var se=new H(window);se.a.c.custom=function(e,t){return new X(t,e)},se.a.c.fontdeck=function(e,t){return new ie(t,e)},se.a.c.monotype=function(e,t){return new K(t,e)},se.a.c.typekit=function(e,t){return new oe(t,e)},se.a.c.google=function(e,t){return new re(t,e)};var ae={load:s(se.load,se)};void 0===(n=function(){return ae}.call(t,r,t,e))||(e.exports=n)}()},198333:(e,t,r)=>{"use strict";function n(e){const t=e.current;return null==t?null:t.decoratedRef?t.decoratedRef.current:t}r.d(t,{P:()=>n})},206040:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let n=0;function o(){return n++}},206928:(e,t,r)=>{"use strict";r.d(t,{Aj:()=>R,K:()=>d,Q_:()=>p,Sx:()=>h,Vj:()=>l,YD:()=>c,Z8:()=>T,_y:()=>g,eG:()=>u,iB:()=>B,jn:()=>w,n7:()=>m,pY:()=>M,tB:()=>N,tE:()=>o,uP:()=>P});var n=r(400770),o=function(){};function i(e,t){void 0===t&&(t="Illegal state"),e||function(e){throw new Error("[mobx-utils] "+e)}(t)}var s=function(e){return e&&e!==Object.prototype&&Object.getOwnPropertyNames(e).concat(s(Object.getPrototypeOf(e))||[])},a=function(e){return function(e){var t=s(e);return t.filter((function(e,r){return t.indexOf(e)===r}))}(e).filter((function(e){return"constructor"!==e&&!~e.indexOf("__")}))},u="pending",c="fulfilled",l="rejected";function f(e){switch(this.state){case u:return e.pending&&e.pending(this.value);case l:return e.rejected&&e.rejected(this.value);case c:return e.fulfilled?e.fulfilled(this.value):this.value}}function h(e,t){if(i(arguments.length<=2,"fromPromise expects up to two arguments"),i("function"==typeof e||"object"==typeof e&&e&&"function"==typeof e.then,"Please pass a promise or function to fromPromise"),!0===e.isPromiseBasedObservable)return e;"function"==typeof e&&(e=new Promise(e));var r=e;e.then((0,n.action)("observableFromPromise-resolve",(function(e){r.value=e,r.state=c})),(0,n.action)("observableFromPromise-reject",(function(e){r.value=e,r.state=l}))),r.isPromiseBasedObservable=!0,r.case=f;var o=!t||t.state!==c&&t.state!==u?void 0:t.value;return(0,n.extendObservable)(r,{value:o,state:u},{},{deep:!1}),r}function d(e){return e&&!0===e.isPromiseBasedObservable}!function(e){e.reject=(0,n.action)("fromPromise.reject",(function(t){var r=e(Promise.reject(t));return r.state=l,r.value=t,r})),e.resolve=(0,n.action)("fromPromise.resolve",(function(t){void 0===t&&(t=void 0);var r=e(Promise.resolve(t));return r.state=c,r.value=t,r}))}(h||(h={}));function p(e,t){void 0===t&&(t=void 0);var r=!1,o=n.observable.box(t,{deep:!1}),i=n.observable.box(!1),s=function(){return r||(r=!0,(0,n._allowStateChanges)(!0,(function(){i.set(!0)})),e((function(e){(0,n._allowStateChanges)(!0,(function(){o.set(e),i.set(!1)}))}))),o.get()},a=(0,n.action)("lazyObservable-reset",(function(){return r=!1,o.set(t),o.get()}));return{current:s,refresh:function(){return r?(r=!1,s()):o.get()},reset:function(){return a()},get pending(){return i.get()}}}function y(e,t,r){void 0===t&&(t=o),void 0===r&&(r=void 0);var s=!1,a=!1,u=r,c=function(){s&&(s=!1,t())},l=(0,n.createAtom)("ResourceBasedObservable",(function(){i(!s&&!a),s=!0,e((function(e){(0,n._allowStateChanges)(!0,(function(){u=e,l.reportChanged()}))}))}),c);return{current:function(){return i(!a,"subscribingObservable has already been disposed"),l.reportObserved()||s||console.warn("Called `get` of a subscribingObservable outside a reaction. Current value will be returned but no new subscription has started"),u},dispose:function(){a=!0,c()},isAlive:function(){return s}}}var b=function(e,t,r,n){var o,i=arguments.length,s=i<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,r,n);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(s=(i<3?o(s):i>3?o(t,r,s):o(t,r))||s);return i>3&&s&&Object.defineProperty(t,r,s),s};function g(e,t){var r;void 0===t&&(t=!1);var o=(0,n.computed)(e);return(r={subscribe:function(e){return"function"==typeof e?{unsubscribe:(0,n.observe)(o,(function(t){var r=t.newValue;return e(r)}),t)}:e&&"object"==typeof e&&e.next?{unsubscribe:(0,n.observe)(o,(function(t){var r=t.newValue;return e.next(r)}),t)}:{unsubscribe:function(){}}}})["function"==typeof Symbol&&Symbol.observable||"@@observable"]=function(){return this},r}var v=function(){function e(e,t){var r=this;Object.defineProperty(this,"current",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"subscription",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),(0,n.makeObservable)(this),(0,n.runInAction)((function(){r.current=t,r.subscription=e.subscribe(r)}))}return Object.defineProperty(e.prototype,"dispose",{enumerable:!1,configurable:!0,writable:!0,value:function(){this.subscription&&this.subscription.unsubscribe()}}),Object.defineProperty(e.prototype,"next",{enumerable:!1,configurable:!0,writable:!0,value:function(e){this.current=e}}),Object.defineProperty(e.prototype,"complete",{enumerable:!1,configurable:!0,writable:!0,value:function(){this.dispose()}}),Object.defineProperty(e.prototype,"error",{enumerable:!1,configurable:!0,writable:!0,value:function(e){this.current=e,this.dispose()}}),b([n.observable.ref],e.prototype,"current",void 0),b([n.action.bound],e.prototype,"next",null),b([n.action.bound],e.prototype,"complete",null),b([n.action.bound],e.prototype,"error",null),e}();function m(e,t){return void 0===t&&(t=void 0),new v(e,t)}var E=function(){return E=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},E.apply(this,arguments)},_=function(e,t,r,n){var o,i=arguments.length,s=i<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,r,n);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(s=(i<3?o(s):i>3?o(t,r,s):o(t,r))||s);return i>3&&s&&Object.defineProperty(t,r,s),s},O=["model","reset","submit","isDirty","isPropertyDirty","resetProperty"],S=function(){function e(e){var t=this;Object.defineProperty(this,"model",{enumerable:!0,configurable:!0,writable:!0,value:e}),Object.defineProperty(this,"localValues",{enumerable:!0,configurable:!0,writable:!0,value:n.observable.map({})}),Object.defineProperty(this,"localComputedValues",{enumerable:!0,configurable:!0,writable:!0,value:n.observable.map({})}),Object.defineProperty(this,"isPropertyDirty",{enumerable:!0,configurable:!0,writable:!0,value:function(e){return t.localValues.has(e)}}),(0,n.makeObservable)(this),i((0,n.isObservableObject)(e),"createViewModel expects an observable object");var r=a(this);a(e).forEach((function(o){var s;if(!r.includes(o)&&o!==n.$mobx&&"__mobxDidRunLazyInitializers"!==o){if(i(-1===O.indexOf(o),"The propertyname "+o+" is reserved and cannot be used with viewModels"),(0,n.isComputedProp)(e,o)){var a=(0,n._getAdministration)(e,o),u=a.derivation.bind(t),c=null===(s=a.setter_)||void 0===s?void 0:s.bind(t);t.localComputedValues.set(o,(0,n.computed)(u,{set:c}))}var l=Object.getOwnPropertyDescriptor(e,o),f=l?{enumerable:l.enumerable}:{};Object.defineProperty(t,o,E(E({},f),{configurable:!0,get:function(){return(0,n.isComputedProp)(e,o)?t.localComputedValues.get(o).get():t.isPropertyDirty(o)?t.localValues.get(o):t.model[o]},set:(0,n.action)((function(r){(0,n.isComputedProp)(e,o)?t.localComputedValues.get(o).set(r):r!==t.model[o]?t.localValues.set(o,r):t.localValues.delete(o)}))}))}}))}return Object.defineProperty(e.prototype,"isDirty",{get:function(){return this.localValues.size>0},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"changedValues",{get:function(){return new Map(this.localValues)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"submit",{enumerable:!1,configurable:!0,writable:!0,value:function(){var e=this;(0,n.keys)(this.localValues).forEach((function(t){var r=e.localValues.get(t),o=e.model[t];(0,n.isObservableArray)(o)?o.replace(r):(0,n.isObservableMap)(o)?(o.clear(),o.merge(r)):(0,n.isComputed)(r)||(e.model[t]=r)})),this.localValues.clear()}}),Object.defineProperty(e.prototype,"reset",{enumerable:!1,configurable:!0,writable:!0,value:function(){this.localValues.clear()}}),Object.defineProperty(e.prototype,"resetProperty",{enumerable:!1,configurable:!0,writable:!0,value:function(e){this.localValues.delete(e)}}),_([n.computed],e.prototype,"isDirty",null),_([n.computed],e.prototype,"changedValues",null),_([n.action.bound],e.prototype,"submit",null),_([n.action.bound],e.prototype,"reset",null),_([n.action.bound],e.prototype,"resetProperty",null),e}();function w(e){return new S(e)}function T(e,t){var r=(0,n.getAtom)(e,t);if(!r)throw new Error("No computed provided, please provide an object created with `computed(() => expr)` or an object + property name");return(0,n.observe)(r,(function(){}))}var A={};function N(e){return void 0===e&&(e=1e3),(0,n._isComputingDerivation)()?(A[e]||(A[e]="number"==typeof e?function(e){var t;return y((function(r){r(Date.now()),t=setInterval((function(){return r(Date.now())}),e)}),(function(){clearInterval(t)}),Date.now())}(e):t=y((function(e){function r(){window.requestAnimationFrame((function(){e(Date.now()),t.isAlive()&&r()}))}e(Date.now()),r()}),(function(){}),Date.now())),A[e].current()):Date.now();var t}function P(e){return(0,n._isComputingDerivation)()||console.warn("'expr' should only be used inside other reactive functions."),(0,n.computed)(e).get()}var I=function(){return I=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},I.apply(this,arguments)};function R(e,t){i("function"==typeof e&&e.length<2,"createTransformer expects a function that accepts one argument");var r=new Map,o=function(e){return"object"==typeof e?e:"function"==typeof e?{onCleanup:e}:{}}(t),s=o.debugNameGenerator,a=o.keepAlive,u=o.onCleanup;var c=!1;return function(t){var i;!function(e){var t=typeof e;if(null===e||"object"!==t&&"function"!==t&&"string"!==t&&"number"!==t)throw new Error("[mobx-utils] transform expected an object, function, string or number, got: "+String(e))}(t);var l=r.get(t);if(l)return l.get();if(!a&&!(0,n._isComputingDerivation)()){!c&&(null!==(i=o.requiresReaction)&&void 0!==i?i:(0,n._getGlobalState)().computedRequiresReaction)&&(console.warn("Invoking a transformer from outside a reactive context won't be memoized and is cleaned up immediately, unless keepAlive is set."),c=!0);var f=e(t);return u&&u(f,t),f}return l=function(t){var i,c=typeof t,l=s?s(t):"Transformer-"+e.name+"-"+("string"===c||"number"===c?t:"object"),f=(0,n.computed)((function(){return i=e(t)}),I(I({},o),{name:l}));if(!a)var h=(0,n.onBecomeUnobserved)(f,(function(){r.delete(t),h(),u&&u(i,t)}));return f}(t),r.set(t,l),l.get()}}function C(e){if(!e)return"ROOT";for(var t=[];e.parent;)t.push(e.path),e=e.parent;return t.reverse().join("/")}function j(e){return(0,n.isObservableObject)(e)||(0,n.isObservableArray)(e)||(0,n.isObservableMap)(e)}function M(e,t){var r=new WeakMap;function o(n){var o=r.get(n.object);!function(e,t){switch(e.type){case"add":i(e.newValue,t,e.name);break;case"update":s(e.oldValue),i(e.newValue,t,e.name||""+e.index);break;case"remove":case"delete":s(e.oldValue);break;case"splice":e.removed.map(s),e.added.forEach((function(r,n){return i(r,t,""+(e.index+n))}));for(var n=e.index+e.addedCount;n<e.object.length;n++)if(j(e.object[n])){var o=r.get(e.object[n]);o&&(o.path=""+n)}}}(n,o),t(n,C(o),e)}function i(e,t,s){if(j(e)){var a=r.get(e);if(a){if(a.parent!==t||a.path!==s)throw new Error("The same observable object cannot appear twice in the same tree, trying to assign it to '"+C(t)+"/"+s+"', but it already exists at '"+C(a.parent)+"/"+a.path+"'")}else{var u={parent:t,path:s,dispose:(0,n.observe)(e,o)};r.set(e,u),(0,n.entries)(e).forEach((function(e){var t=e[0];return i(e[1],u,""+t)}))}}}function s(e){if(j(e)){var t=r.get(e);if(!t)return;r.delete(e),t.dispose(),(0,n.values)(e).forEach(s)}}return i(e,void 0,""),function(){s(e)}}var x,D=(x=function(e,t){return x=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},x(e,t)},function(e,t){function r(){this.constructor=e}x(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),L=(function(e){function t(t,r,o){var i=void 0===o?{}:o,s=i.name,a=void 0===s?"ogm"+(1e3*Math.random()|0):s,u=i.keyToName,c=void 0===u?function(e){return""+e}:u,l=e.call(this)||this;Object.defineProperty(l,"_base",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(l,"_ogmInfoKey",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(l,"_groupBy",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(l,"_keyToName",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(l,"_disposeBaseObserver",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),l._keyToName=c,l._groupBy=r,l._ogmInfoKey=Symbol("ogmInfo"+a),l._base=t;for(var f=0;f<t.length;f++)l._addItem(t[f]);return l._disposeBaseObserver=(0,n.observe)(l._base,(function(e){if("splice"===e.type)(0,n.transaction)((function(){for(var t=0,r=e.removed;t<r.length;t++){var n=r[t];l._removeItem(n)}for(var o=0,i=e.added;o<i.length;o++){var s=i[o];l._addItem(s)}}));else{if("update"!==e.type)throw new Error("illegal state");(0,n.transaction)((function(){l._removeItem(e.oldValue),l._addItem(e.newValue)}))}})),l}D(t,e),Object.defineProperty(t.prototype,"clear",{enumerable:!1,configurable:!0,writable:!0,value:function(){throw new Error("not supported")}}),Object.defineProperty(t.prototype,"delete",{enumerable:!1,configurable:!0,writable:!0,value:function(e){throw new Error("not supported")}}),Object.defineProperty(t.prototype,"set",{enumerable:!1,configurable:!0,writable:!0,value:function(e,t){throw new Error("not supported")}}),Object.defineProperty(t.prototype,"dispose",{enumerable:!1,configurable:!0,writable:!0,value:function(){this._disposeBaseObserver();for(var e=0;e<this._base.length;e++){var t=this._base[e];t[this._ogmInfoKey].reaction(),delete t[this._ogmInfoKey]}}}),Object.defineProperty(t.prototype,"_getGroupArr",{enumerable:!1,configurable:!0,writable:!0,value:function(t){var r=e.prototype.get.call(this,t);return void 0===r&&(r=(0,n.observable)([],{name:"GroupArray["+this._keyToName(t)+"]",deep:!1}),e.prototype.set.call(this,t,r)),r}}),Object.defineProperty(t.prototype,"_removeFromGroupArr",{enumerable:!1,configurable:!0,writable:!0,value:function(t,r){var n=e.prototype.get.call(this,t);1===n.length?e.prototype.delete.call(this,t):(r===n.length-1||(n[r]=n[n.length-1],n[r][this._ogmInfoKey].groupArrIndex=r),n.length--)}}),Object.defineProperty(t.prototype,"_addItem",{enumerable:!1,configurable:!0,writable:!0,value:function(e){var t=this,r=this._groupBy(e),o=this._getGroupArr(r),i={groupByValue:r,groupArrIndex:o.length,reaction:(0,n.reaction)((function(){return t._groupBy(e)}),(function(r,n){var o=e[t._ogmInfoKey];t._removeFromGroupArr(o.groupByValue,o.groupArrIndex);var i=t._getGroupArr(r),s=i.length;i.push(e),o.groupByValue=r,o.groupArrIndex=s}))};Object.defineProperty(e,this._ogmInfoKey,{configurable:!0,enumerable:!1,value:i}),o.push(e)}}),Object.defineProperty(t.prototype,"_removeItem",{enumerable:!1,configurable:!0,writable:!0,value:function(e){var t=e[this._ogmInfoKey];this._removeFromGroupArr(t.groupByValue,t.groupArrIndex),t.reaction(),delete e[this._ogmInfoKey]}})}(n.ObservableMap),function(){function e(e,t,r,n){Object.defineProperty(this,"base",{enumerable:!0,configurable:!0,writable:!0,value:e}),Object.defineProperty(this,"args",{enumerable:!0,configurable:!0,writable:!0,value:t}),Object.defineProperty(this,"version",{enumerable:!0,configurable:!0,writable:!0,value:r}),Object.defineProperty(this,"versionChecker",{enumerable:!0,configurable:!0,writable:!0,value:n}),Object.defineProperty(this,"root",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"closest",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"closestIdx",{enumerable:!0,configurable:!0,writable:!0,value:0});for(var o=this.closest=this.root=e,i=0;i<this.args.length-1&&(o=o.get(t[i]));i++)this.closest=o;this.closestIdx=i}return Object.defineProperty(e.prototype,"exists",{enumerable:!1,configurable:!0,writable:!0,value:function(){this.assertCurrentVersion();var e=this.args.length;return this.closestIdx>=e-1&&this.closest.has(this.args[e-1])}}),Object.defineProperty(e.prototype,"get",{enumerable:!1,configurable:!0,writable:!0,value:function(){if(this.assertCurrentVersion(),!this.exists())throw new Error("Entry doesn't exist");return this.closest.get(this.args[this.args.length-1])}}),Object.defineProperty(e.prototype,"set",{enumerable:!1,configurable:!0,writable:!0,value:function(e){this.assertCurrentVersion();for(var t=this.args.length,r=this.closest,n=this.closestIdx;n<t-1;n++){var o=new Map;r.set(this.args[n],o),r=o}this.closestIdx=t-1,this.closest=r,r.set(this.args[t-1],e)}}),Object.defineProperty(e.prototype,"delete",{enumerable:!1,configurable:!0,writable:!0,value:function(){if(this.assertCurrentVersion(),!this.exists())throw new Error("Entry doesn't exist");var e=this.args.length;this.closest.delete(this.args[e-1]);for(var t=this.root,r=[t],n=0;n<e-1;n++)t=t.get(this.args[n]),r.push(t);for(n=r.length-1;n>0;n--)0===r[n].size&&r[n-1].delete(this.args[n-1])}}),Object.defineProperty(e.prototype,"assertCurrentVersion",{enumerable:!1,configurable:!0,writable:!0,value:function(){if(!this.versionChecker(this.version))throw new Error("Concurrent modification exception")}}),e}()),k=function(){function e(){var e=this;Object.defineProperty(this,"store",{enumerable:!0,configurable:!0,writable:!0,value:new Map}),Object.defineProperty(this,"argsLength",{enumerable:!0,configurable:!0,writable:!0,value:-1}),Object.defineProperty(this,"currentVersion",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,"checkVersion",{enumerable:!0,configurable:!0,writable:!0,value:function(t){return e.currentVersion===t}})}return Object.defineProperty(e.prototype,"entry",{enumerable:!1,configurable:!0,writable:!0,value:function(e){if(-1===this.argsLength)this.argsLength=e.length;else if(this.argsLength!==e.length)throw new Error("DeepMap should be used with functions with a consistent length, expected: "+this.argsLength+", got: "+e.length);return this.currentVersion>=Number.MAX_SAFE_INTEGER&&(this.currentVersion=0),this.currentVersion++,new L(this.store,e,this.currentVersion,this.checkVersion)}}),e}(),U=function(){return U=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},U.apply(this,arguments)},F=function(){for(var e=0,t=0,r=arguments.length;t<r;t++)e+=arguments[t].length;var n=Array(e),o=0;for(t=0;t<r;t++)for(var i=arguments[t],s=0,a=i.length;s<a;s++,o++)n[o]=i[s];return n};function B(e,t){if(void 0===t&&(t=!1),(0,n.isAction)(e))throw new Error("computedFn shouldn't be used on actions");var r=!1,o=0,i="boolean"==typeof t?{keepAlive:t}:t,s=new k;return function(){for(var t,a=this,u=[],c=0;c<arguments.length;c++)u[c]=arguments[c];var l,f=s.entry(u);if(f.exists())return f.get().get();if(!i.keepAlive&&!(0,n._isComputingDerivation)()){!r&&(null!==(t=i.requiresReaction)&&void 0!==t?t:(0,n._getGlobalState)().computedRequiresReaction)&&(console.warn("Invoking a computedFn from outside a reactive context won't be memoized and is cleaned up immediately, unless keepAlive is set."),r=!0);var h=e.apply(this,u);return i.onCleanup&&i.onCleanup.apply(i,F([h],u)),h}var d=(0,n.computed)((function(){return l=e.apply(a,u)}),U(U({},i),{name:"computedFn("+(i.name||e.name)+"#"+ ++o+")"}));return f.set(d),i.keepAlive||(0,n.onBecomeUnobserved)(d,(function(){s.entry(u).delete(),i.onCleanup&&i.onCleanup.apply(i,F([l],u)),l=void 0})),d.get()}}},211794:(e,t,r)=>{"use strict";function n(){return"function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator"}r.d(t,{lJ:()=>o});var o=n()},212943:(e,t,r)=>{"use strict";r.d(t,{H:()=>s});var n=r(226260),o=r(664191),i=r(502678);function s(e,t){return t?(0,i.c)(e,t):e instanceof n.c?e:new n.c((0,o.i)(e))}},213427:(e,t,r)=>{"use strict";r.d(t,{h:()=>a});var n=r(226260),o=r(453535),i=r(290676),s=r(550742);function a(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=Number.POSITIVE_INFINITY,a=null,u=e[e.length-1];return(0,o.m)(u)?(a=e.pop(),e.length>1&&"number"==typeof e[e.length-1]&&(r=e.pop())):"number"==typeof u&&(r=e.pop()),null===a&&1===e.length&&e[0]instanceof n.c?e[0]:(0,i.U)(r)((0,s.c)(e,a))}},221963:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(222990),o=r(403732);const i=r(146462);class s{constructor(e){this.backend=e,this.hooks=(0,n.A)({dragSource:(e,t)=>{this.dragSourceOptions=t||null,(0,o.i)(e)?this.dragSourceRef=e:this.dragSourceNode=e,this.reconnectDragSource()},dragPreview:(e,t)=>{this.dragPreviewOptions=t||null,(0,o.i)(e)?this.dragPreviewRef=e:this.dragPreviewNode=e,this.reconnectDragPreview()}}),this.handlerId=null,this.dragSourceRef=null,this.dragSourceOptionsInternal=null,this.dragPreviewRef=null,this.dragPreviewOptionsInternal=null,this.lastConnectedHandlerId=null,this.lastConnectedDragSource=null,this.lastConnectedDragSourceOptions=null,this.lastConnectedDragPreview=null,this.lastConnectedDragPreviewOptions=null}receiveHandlerId(e){this.handlerId!==e&&(this.handlerId=e,this.reconnect())}get connectTarget(){return this.dragSource}get dragSourceOptions(){return this.dragSourceOptionsInternal}set dragSourceOptions(e){this.dragSourceOptionsInternal=e}get dragPreviewOptions(){return this.dragPreviewOptionsInternal}set dragPreviewOptions(e){this.dragPreviewOptionsInternal=e}reconnect(){this.reconnectDragSource(),this.reconnectDragPreview()}reconnectDragSource(){const e=this.didHandlerIdChange()||this.didConnectedDragSourceChange()||this.didDragSourceOptionsChange();e&&this.disconnectDragSource();const t=this.dragSource;this.handlerId&&(t?e&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragSource=t,this.lastConnectedDragSourceOptions=this.dragSourceOptions,this.dragSourceUnsubscribe=this.backend.connectDragSource(this.handlerId,t,this.dragSourceOptions)):this.lastConnectedDragSource=t)}reconnectDragPreview(){const e=this.didHandlerIdChange()||this.didConnectedDragPreviewChange()||this.didDragPreviewOptionsChange();e&&this.disconnectDragPreview();const t=this.dragPreview;this.handlerId&&t&&e&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragPreview=t,this.lastConnectedDragPreviewOptions=this.dragPreviewOptions,this.dragPreviewUnsubscribe=this.backend.connectDragPreview(this.handlerId,t,this.dragPreviewOptions))}didHandlerIdChange(){return this.lastConnectedHandlerId!==this.handlerId}didConnectedDragSourceChange(){return this.lastConnectedDragSource!==this.dragSource}didConnectedDragPreviewChange(){return this.lastConnectedDragPreview!==this.dragPreview}didDragSourceOptionsChange(){return!i(this.lastConnectedDragSourceOptions,this.dragSourceOptions)}didDragPreviewOptionsChange(){return!i(this.lastConnectedDragPreviewOptions,this.dragPreviewOptions)}disconnectDragSource(){this.dragSourceUnsubscribe&&(this.dragSourceUnsubscribe(),this.dragSourceUnsubscribe=void 0,this.dragPreviewNode=null,this.dragPreviewRef=null)}disconnectDragPreview(){this.dragPreviewUnsubscribe&&(this.dragPreviewUnsubscribe(),this.dragPreviewUnsubscribe=void 0,this.dragPreviewNode=null,this.dragPreviewRef=null)}get dragSource(){return this.dragSourceNode||this.dragSourceRef&&this.dragSourceRef.current}get dragPreview(){return this.dragPreviewNode||this.dragPreviewRef&&this.dragPreviewRef.current}}},222990:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(845212),o=r(937815);r(757130);function i(e){return(t=null,r=null)=>{if(!(0,n.isValidElement)(t)){const n=t;return e(n,r),n}const i=t;!function(e){if("string"==typeof e.type)return;const t=e.type.displayName||e.type.name||"the component";throw new Error(`Only native element nodes can now be passed to React DnD connectors.You can either wrap ${t} into a <div>, or turn it into a drag source or a drop target itself.`)}(i);const s=r?t=>e(t,r):e;return(0,o.A)(i,s)}}function s(e){const t={};return Object.keys(e).forEach((r=>{const n=e[r];if(r.endsWith("Ref"))t[r]=e[r];else{const e=i(n);t[r]=()=>e}})),t}},223331:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.every=function(e){return new o.default((t=>{let r=null,n=0;t.onSubscribe({cancel:()=>{null!=r&&(clearInterval(r),r=null)},request:o=>{o<Number.MAX_SAFE_INTEGER?n+=o:n=Number.MAX_SAFE_INTEGER,null==r&&(r=setInterval((()=>{n>0&&(n!==Number.MAX_SAFE_INTEGER&&n--,t.onNext(Date.now()))}),e))}})}))};var n,o=(n=r(110676))&&n.__esModule?n:{default:n}},224892:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(656772);class o{constructor(e){this.entered=[],this.isNodeInDocument=e}enter(e){const t=this.entered.length;return this.entered=(0,n.KC)(this.entered.filter((t=>this.isNodeInDocument(t)&&(!t.contains||t.contains(e)))),[e]),0===t&&this.entered.length>0}leave(e){const t=this.entered.length;return this.entered=(0,n.FF)(this.entered.filter(this.isNodeInDocument),e),t>0&&0===this.entered.length}reset(){this.entered=[]}}},226260:(e,t,r)=>{"use strict";r.d(t,{c:()=>u});var n=r(179210),o=r(248203),i=r(977305),s=r(228120),a=r(558279),u=function(){function e(e){this._isScalar=!1,e&&(this._subscribe=e)}return e.prototype.lift=function(t){var r=new e;return r.source=this,r.operator=t,r},e.prototype.subscribe=function(e,t,r){var n=this.operator,i=(0,o.u)(e,t,r);if(n?i.add(n.call(i,this.source)):i.add(this.source||a.$.useDeprecatedSynchronousErrorHandling&&!i.syncErrorThrowable?this._subscribe(i):this._trySubscribe(i)),a.$.useDeprecatedSynchronousErrorHandling&&i.syncErrorThrowable&&(i.syncErrorThrowable=!1,i.syncErrorThrown))throw i.syncErrorValue;return i},e.prototype._trySubscribe=function(e){try{return this._subscribe(e)}catch(t){a.$.useDeprecatedSynchronousErrorHandling&&(e.syncErrorThrown=!0,e.syncErrorValue=t),(0,n.H)(e)?e.error(t):console.warn(t)}},e.prototype.forEach=function(e,t){var r=this;return new(t=c(t))((function(t,n){var o;o=r.subscribe((function(t){try{e(t)}catch(r){n(r),o&&o.unsubscribe()}}),n,t)}))},e.prototype._subscribe=function(e){var t=this.source;return t&&t.subscribe(e)},e.prototype[i.s]=function(){return this},e.prototype.pipe=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return 0===e.length?this:(0,s.m)(e)(this)},e.prototype.toPromise=function(e){var t=this;return new(e=c(e))((function(e,r){var n;t.subscribe((function(e){return n=e}),(function(e){return r(e)}),(function(){return e(n)}))}))},e.create=function(t){return new e(t)},e}();function c(e){if(e||(e=a.$.Promise||Promise),!e)throw new Error("no Promise impl found");return e}},228120:(e,t,r)=>{"use strict";r.d(t,{F:()=>o,m:()=>i});var n=r(897210);function o(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return i(e)}function i(e){return 0===e.length?n.D:1===e.length?e[0]:function(t){return e.reduce((function(e,t){return t(e)}),t)}}},229664:(e,t,r)=>{"use strict";r.d(t,{E:()=>o});var n=r(998083),o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n.C6(t,e),t.prototype.notifyNext=function(e,t,r,n,o){this.destination.next(t)},t.prototype.notifyError=function(e,t){this.destination.error(e)},t.prototype.notifyComplete=function(e){this.destination.complete()},t}(r(335679).v)},230083:(e,t,r)=>{"use strict";r.d(t,{l:()=>i});var n=r(983923),o=r(759098);const i={[n.FILE]:{exposeProperties:{files:e=>Array.prototype.slice.call(e.files),items:e=>e.items},matchesTypes:["Files"]},[n.URL]:{exposeProperties:{urls:(e,t)=>(0,o.W)(e,t,"").split("\n")},matchesTypes:["Url","text/uri-list"]},[n.TEXT]:{exposeProperties:{text:(e,t)=>(0,o.W)(e,t,"")},matchesTypes:["Text","text/plain"]}}},231711:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(222990),o=r(403732);const i=r(146462);class s{constructor(e){this.backend=e,this.hooks=(0,n.A)({dropTarget:(e,t)=>{this.dropTargetOptions=t,(0,o.i)(e)?this.dropTargetRef=e:this.dropTargetNode=e,this.reconnect()}}),this.handlerId=null,this.dropTargetRef=null,this.dropTargetOptionsInternal=null,this.lastConnectedHandlerId=null,this.lastConnectedDropTarget=null,this.lastConnectedDropTargetOptions=null}get connectTarget(){return this.dropTarget}reconnect(){const e=this.didHandlerIdChange()||this.didDropTargetChange()||this.didOptionsChange();e&&this.disconnectDropTarget();const t=this.dropTarget;this.handlerId&&(t?e&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDropTarget=t,this.lastConnectedDropTargetOptions=this.dropTargetOptions,this.unsubscribeDropTarget=this.backend.connectDropTarget(this.handlerId,t,this.dropTargetOptions)):this.lastConnectedDropTarget=t)}receiveHandlerId(e){e!==this.handlerId&&(this.handlerId=e,this.reconnect())}get dropTargetOptions(){return this.dropTargetOptionsInternal}set dropTargetOptions(e){this.dropTargetOptionsInternal=e}didHandlerIdChange(){return this.lastConnectedHandlerId!==this.handlerId}didDropTargetChange(){return this.lastConnectedDropTarget!==this.dropTarget}didOptionsChange(){return!i(this.lastConnectedDropTargetOptions,this.dropTargetOptions)}disconnectDropTarget(){this.unsubscribeDropTarget&&(this.unsubscribeDropTarget(),this.unsubscribeDropTarget=void 0)}get dropTarget(){return this.dropTargetNode||this.dropTargetRef&&this.dropTargetRef.current}}},237157:(e,t,r)=>{"use strict";r.d(t,{R:()=>o});var n=r(998083),o=function(e){function t(t,r){var n=e.call(this,t,r)||this;return n.scheduler=t,n.work=r,n.pending=!1,n}return n.C6(t,e),t.prototype.schedule=function(e,t){if(void 0===t&&(t=0),this.closed)return this;this.state=e;var r=this.id,n=this.scheduler;return null!=r&&(this.id=this.recycleAsyncId(n,r,t)),this.pending=!0,this.delay=t,this.id=this.id||this.requestAsyncId(n,this.id,t),this},t.prototype.requestAsyncId=function(e,t,r){return void 0===r&&(r=0),setInterval(e.flush.bind(e,this),r)},t.prototype.recycleAsyncId=function(e,t,r){if(void 0===r&&(r=0),null!==r&&this.delay===r&&!1===this.pending)return t;clearInterval(t)},t.prototype.execute=function(e,t){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;var r=this._execute(e,t);if(r)return r;!1===this.pending&&null!=this.id&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},t.prototype._execute=function(e,t){var r=!1,n=void 0;try{this.work(e)}catch(o){r=!0,n=!!o&&o||new Error(o)}if(r)return this.unsubscribe(),n},t.prototype._unsubscribe=function(){var e=this.id,t=this.scheduler,r=t.actions,n=r.indexOf(this);this.work=null,this.state=null,this.pending=!1,this.scheduler=null,-1!==n&&r.splice(n,1),null!=e&&(this.id=this.recycleAsyncId(t,e,null)),this.delay=null},t}(r(59329).r)},245307:(e,t,r)=>{"use strict";t.Fm=void 0;const n=r(88085),o=r(472537),i=r(148676);i.enableAsyncContextRuntime?o.Variable:n.Variable;const s=i.enableAsyncContextRuntime?o.Snapshot:n.Snapshot;t.Fm=s},248203:(e,t,r)=>{"use strict";r.d(t,{u:()=>s});var n=r(335679),o=r(623276),i=r(537727);function s(e,t,r){if(e){if(e instanceof n.v)return e;if(e[o.D])return e[o.D]()}return e||t||r?new n.v(e,t,r):new n.v(i.I)}},254136:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(515807);Object.keys(n).forEach((function(e){"default"!==e&&"__esModule"!==e&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))}));var o=r(343696);Object.keys(o).forEach((function(e){"default"!==e&&"__esModule"!==e&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))}))},261297:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(680639),o=r(716339),i=r(799769),s=r(875828),a=r(990352);class u{constructor(e,t={},r=!1){this.context=t,this.isSetUp=!1,this.handleRefCountChange=()=>{const e=this.store.getState().refCount>0;e&&!this.isSetUp?(this.backend.setup(),this.isSetUp=!0):!e&&this.isSetUp&&(this.backend.teardown(),this.isSetUp=!1)};const i=function(e){const t="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__;return(0,n.y$)(o.A,e&&t&&t({name:"dnd-core",instanceId:"dnd-core"}))}(r);this.store=i,this.monitor=new s.A(i,new a.A(i)),this.backend=e(this),i.subscribe(this.handleRefCountChange)}getContext(){return this.context}getMonitor(){return this.monitor}getBackend(){return this.backend}getRegistry(){return this.monitor.registry}getActions(){const e=this,{dispatch:t}=this.store;const r=(0,i.Ay)(this);return Object.keys(r).reduce(((n,o)=>{const i=r[o];var s;return n[o]=(s=i,(...r)=>{const n=s.apply(e,r);void 0!==n&&t(n)}),n}),{})}dispatch(e){this.store.dispatch(e)}}},262772:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(407010);function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach((function(t){(0,n.A)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}},267921:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(799769),o=r(352754),i=r(753939),s=r(792768),a=r(511894);function u(e=s.x3,t){switch(t.type){case n.l6:break;case o.Yd:case o.SO:case o.v4:case o.n_:return s.x3;case n.Vw:case n.BS:case n.dU:case n.q2:default:return s.y2}const{targetIds:r=[],prevTargetIds:u=[]}=t.payload,c=(0,a.I8)(r,u);if(!(c.length>0||!(0,i.BI)(r,u)))return s.x3;const l=u[u.length-1],f=r[r.length-1];return l!==f&&(l&&c.push(l),f&&c.push(f)),c}},268245:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var n=r(998083),o=r(335679);function i(e,t){return function(r){return r.lift(new s(e,t))}}var s=function(){function e(e,t){this.predicate=e,this.thisArg=t}return e.prototype.call=function(e,t){return t.subscribe(new a(e,this.predicate,this.thisArg))},e}(),a=function(e){function t(t,r,n){var o=e.call(this,t)||this;return o.predicate=r,o.thisArg=n,o.count=0,o}return n.C6(t,e),t.prototype._next=function(e){var t;try{t=this.predicate.call(this.thisArg,e,this.count++)}catch(r){return void this.destination.error(r)}t&&this.destination.next(e)},t}(o.v)},279590:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.encodeCompositeMetadata=function(e){let t=(0,o.createBuffer)(0);for(const[r,n]of e){const e="function"==typeof n?n():n;t=r instanceof i.default||"number"==typeof r||"WellKnownMimeType"===r.constructor.name?c(t,r,e):u(t,r,e)}return t},t.encodeAndAddCustomMetadata=u,t.encodeAndAddWellKnownMetadata=c,t.decodeMimeAndContentBuffersSlices=l,t.decodeMimeTypeFromMimeBuffer=f,t.encodeCustomMetadataHeader=h,t.encodeWellKnownMetadataHeader=d,t.decodeCompositeMetadata=p,t.WellKnownMimeTypeEntry=t.ReservedMimeTypeEntry=t.ExplicitMimeTimeEntry=t.CompositeMetadata=void 0;var n=r(514984),o=r(678596),i=function(e){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=s();if(t&&t.has(e))return t.get(e);var r={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var i=n?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(r,o,i):r[o]=e[o]}r.default=e,t&&t.set(e,r);return r}(r(798603));function s(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return s=function(){return e},e}class a{constructor(e){this._buffer=e}iterator(){return p(this._buffer)}[Symbol.iterator](){return p(this._buffer)}}function u(e,t,r){return n.LiteBuffer.concat([e,h(t,r.byteLength),r])}function c(e,t,r){let o;return o=Number.isInteger(t)?t:t.identifier,n.LiteBuffer.concat([e,d(o,r.byteLength),r])}function l(e,t){const r=e.readInt8(t);let n,i=t;if((r&E)===E)n=e.slice(i,i+1),i+=1;else{const t=1+(255&r);if(!(e.byteLength>i+t))throw new Error("Metadata is malformed. Inappropriately formed Mime Length");n=e.slice(i,i+t+1),i+=t+1}if(e.byteLength>=i+3){const t=(0,o.readUInt24BE)(e,i);if(i+=3,e.byteLength>=t+i){return[n,e.slice(i,i+t)]}throw new Error("Metadata is malformed. Inappropriately formed Metadata Length or malformed content")}throw new Error("Metadata is malformed. Metadata Length is absent or malformed")}function f(e){if(e.length<2)throw new Error("Unable to decode explicit MIME type");return e.toString("ascii",1)}function h(e,t){const r=(0,o.createBuffer)(4+e.length),n=r.write(e,1);if(!function(e,t){let r=!0;for(let n=t,o=e.length;n<o;n++)if(e[n]>127){r=!1;break}return r}(r,1))throw new Error("Custom mime type must be US_ASCII characters only");if(n<1||n>128)throw new Error("Custom mime type must have a strictly positive length that fits on 7 unsigned bits, ie 1-128");return r.writeUInt8(n-1),(0,o.writeUInt24BE)(r,t,n+1),r}function d(e,t){const r=n.LiteBuffer.alloc(4);return r.writeUInt8(e|E),(0,o.writeUInt24BE)(r,t,1),r}function*p(e){const t=e.byteLength;let r=0;for(;r<t;){const t=l(e,r),o=t[0],s=t[1];if(n=s,r=r+o.byteLength+3+n.byteLength,!m(o)){const e=f(o);if(!e)throw new Error("MIME type cannot be null");yield new y(s,e);continue}const a=v(o),u=i.default.fromIdentifier(a);i.UNKNOWN_RESERVED_MIME_TYPE!==u?yield new g(s,u):yield new b(s,a)}var n}t.CompositeMetadata=a;class y{constructor(e,t){this._content=e,this._type=t}get content(){return this._content}get mimeType(){return this._type}}t.ExplicitMimeTimeEntry=y;class b{constructor(e,t){this._content=e,this._type=t}get content(){return this._content}get mimeType(){}get type(){return this._type}}t.ReservedMimeTypeEntry=b;class g{constructor(e,t){this._content=e,this._type=t}get content(){return this._content}get mimeType(){return this._type.string}get type(){return this._type}}function v(e){return m(e)?e.readInt8()&_:i.UNPARSEABLE_MIME_TYPE.identifier}function m(e){return 1===e.byteLength}t.WellKnownMimeTypeEntry=g;const E=128,_=127},290676:(e,t,r)=>{"use strict";r.d(t,{U:()=>i});var n=r(895693),o=r(897210);function i(e){return void 0===e&&(e=Number.POSITIVE_INFINITY),(0,n.ZZ)(o.D,e)}},291650:(e,t,r)=>{"use strict";r.d(t,{I:()=>i,w:()=>o});var n=r(226260),o=new n.c((function(e){return e.complete()}));function i(e){return e?function(e){return new n.c((function(t){return e.schedule((function(){return t.complete()}))}))}(e):o}},296423:(e,t,r)=>{"use strict";r.d(t,{x:()=>i});var n=r(322546),o=r(506800);function i(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return(0,o.K)()(n.of.apply(void 0,e))}},302287:function(e,t,r){"use strict";var n,o=this&&this.__assign||function(){return o=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},o.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0});var i=r(408406);function s(e){return function(e){return!!e.targetTouches}(e)?function(e){if(1===e.targetTouches.length)return s(e.targetTouches[0])}(e):{x:e.clientX,y:e.clientY}}var a=1,u=0;function c(e){return void 0===e.button||e.button===u}var l=("undefined"!=typeof document&&document.elementsFromPoint||function(e,t){if(document.msElementsFromPoint){var r=document.msElementsFromPoint(e,t);return r&&Array.prototype.slice.call(r,0)}for(var n,o,i,s=[],a=[];(n=document.elementFromPoint(e,t))&&-1===s.indexOf(n)&&null!==n;)s.push(n),a.push({value:n.style.getPropertyValue("pointer-events"),priority:n.style.getPropertyPriority("pointer-events")}),n.style.setProperty("pointer-events","none","important");for(o=a.length;i=a[--o];)s[o].style.setProperty("pointer-events",i.value?i.value:"",i.priority);return s}).bind("undefined"!=typeof document?document:null),f=function(){var e=!1;try{addEventListener("test",null,Object.defineProperty({},"passive",{get:function(){e=!0}}))}catch(t){}return e}();var h=((n={}).mouse={start:"mousedown",move:"mousemove",end:"mouseup",contextmenu:"contextmenu"},n.touch={start:"touchstart",move:"touchmove",end:"touchend"},n.keyboard={keydown:"keydown"},n),d=function(){function e(e,t){var r=this;void 0===t&&(t={}),this.getSourceClientOffset=function(e){return function(e){var t=1===e.nodeType?e:e.parentElement;if(!t)return null;var r=t.getBoundingClientRect(),n=r.top;return{x:r.left,y:n}}(r.sourceNodes[e])},this.handleTopMoveStartCapture=function(e){c(e)&&(r.moveStartSourceIds=[])},this.handleMoveStart=function(e){Array.isArray(r.moveStartSourceIds)&&r.moveStartSourceIds.unshift(e)},this.handleTopMoveStart=function(e){if(c(e)){var t=s(e);t&&(r._mouseClientOffset=t),r.waitingForDelay=!1}},this.handleTopMoveStartDelay=function(e){if(c(e)){var t=e.type===h.touch.start?r.delayTouchStart:r.delayMouseStart;r.timeout=setTimeout(r.handleTopMoveStart.bind(r,e),t),r.waitingForDelay=!0}},this.handleTopMoveCapture=function(){r.dragOverTargetIds=[]},this.handleMove=function(e,t){r.dragOverTargetIds.unshift(t)},this.handleTopMove=function(e){if(clearTimeout(r.timeout),!r.waitingForDelay){var t,n,o,i,a=r,u=a.moveStartSourceIds,c=a.dragOverTargetIds,f=a.enableHoverOutsideTarget,h=s(e);if(h)if(r._isScrolling||!r.monitor.isDragging()&&function(e,t,r,n,o){if(!o)return!1;for(var i=180*Math.atan2(n-t,r-e)/Math.PI+180,s=0;s<o.length;++s)if((null==o[s].start||i>=o[s].start)&&(null==o[s].end||i<=o[s].end))return!0;return!1}(r._mouseClientOffset.x,r._mouseClientOffset.y,h.x,h.y,r.scrollAngleRanges))r._isScrolling=!0;else if(!r.monitor.isDragging()&&r._mouseClientOffset.hasOwnProperty("x")&&u&&(t=r._mouseClientOffset.x,n=r._mouseClientOffset.y,o=h.x,i=h.y,Math.sqrt(Math.pow(Math.abs(o-t),2)+Math.pow(Math.abs(i-n),2))>(r.touchSlop?r.touchSlop:0))&&(r.moveStartSourceIds=null,r.actions.beginDrag(u,{clientOffset:r._mouseClientOffset,getSourceClientOffset:r.getSourceClientOffset,publishSource:!1})),r.monitor.isDragging()){var d=r.sourceNodes[r.monitor.getSourceId()];r.installSourceNodeRemovalObserver(d),r.actions.publishDragSource(),e.preventDefault();var p=c.map((function(e){return r.targetNodes[e]})),y=r.getDropTargetElementsAtPoint?r.getDropTargetElementsAtPoint(h.x,h.y,p):l(h.x,h.y),b=[];for(var g in y)if(y.hasOwnProperty(g)){var v=y[g];for(b.push(v);v;)v=v.parentElement,-1===b.indexOf(v)&&b.push(v)}var m=b.filter((function(e){return p.indexOf(e)>-1})).map((function(e){for(var t in r.targetNodes)if(e===r.targetNodes[t])return t;return null})).filter((function(e){return!!e})).filter((function(e,t,r){return r.indexOf(e)===t}));if(f)for(var E in r.targetNodes)if(r.targetNodes[E]&&r.targetNodes[E].contains(d)&&-1===m.indexOf(E)){m.unshift(E);break}m.reverse(),r.actions.hover(m,{clientOffset:h})}}},this.handleTopMoveEndCapture=function(e){r._isScrolling=!1,function(e){return void 0===e.buttons||!(e.buttons&a)}(e)&&(r.monitor.isDragging()&&!r.monitor.didDrop()?(e.preventDefault(),r._mouseClientOffset={},r.uninstallSourceNodeRemovalObserver(),r.actions.drop(),r.actions.endDrag()):r.moveStartSourceIds=null)},this.handleCancelOnEscape=function(e){"Escape"===e.key&&r.monitor.isDragging()&&(r._mouseClientOffset={},r.uninstallSourceNodeRemovalObserver(),r.actions.endDrag())},t.delayTouchStart=t.delayTouchStart||t.delay,t=o({enableTouchEvents:!0,enableMouseEvents:!1,enableKeyboardEvents:!1,ignoreContextMenu:!1,enableHoverOutsideTarget:!1,delayTouchStart:0,delayMouseStart:0,touchSlop:0,scrollAngleRanges:void 0},t),this.actions=e.getActions(),this.monitor=e.getMonitor(),this.registry=e.getRegistry(),this.enableKeyboardEvents=t.enableKeyboardEvents,this.enableMouseEvents=t.enableMouseEvents,this.delayTouchStart=t.delayTouchStart,this.delayMouseStart=t.delayMouseStart,this.ignoreContextMenu=t.ignoreContextMenu,this.touchSlop=t.touchSlop,this.scrollAngleRanges=t.scrollAngleRanges,this.enableHoverOutsideTarget=t.enableHoverOutsideTarget,this.sourceNodes={},this.sourceNodeOptions={},this.sourcePreviewNodes={},this.sourcePreviewNodeOptions={},this.targetNodes={},this.targetNodeOptions={},this.listenerTypes=[],this._mouseClientOffset={},this._isScrolling=!1,t.enableMouseEvents&&this.listenerTypes.push("mouse"),t.enableTouchEvents&&this.listenerTypes.push("touch"),t.enableKeyboardEvents&&this.listenerTypes.push("keyboard"),t.getDropTargetElementsAtPoint&&(this.getDropTargetElementsAtPoint=t.getDropTargetElementsAtPoint)}return e.prototype.setup=function(){"undefined"!=typeof window&&(i(!e.isSetUp,"Cannot have two Touch backends at the same time."),e.isSetUp=!0,this.addEventListener(window,"start",this.getTopMoveStartHandler()),this.addEventListener(window,"start",this.handleTopMoveStartCapture,!0),this.addEventListener(window,"move",this.handleTopMove),this.addEventListener(window,"move",this.handleTopMoveCapture,!0),this.addEventListener(window,"end",this.handleTopMoveEndCapture,!0),this.enableMouseEvents&&!this.ignoreContextMenu&&this.addEventListener(window,"contextmenu",this.handleTopMoveEndCapture),this.enableKeyboardEvents&&this.addEventListener(window,"keydown",this.handleCancelOnEscape,!0))},e.prototype.teardown=function(){"undefined"!=typeof window&&(e.isSetUp=!1,this._mouseClientOffset={},this.removeEventListener(window,"start",this.handleTopMoveStartCapture,!0),this.removeEventListener(window,"start",this.handleTopMoveStart),this.removeEventListener(window,"move",this.handleTopMoveCapture,!0),this.removeEventListener(window,"move",this.handleTopMove),this.removeEventListener(window,"end",this.handleTopMoveEndCapture,!0),this.enableMouseEvents&&!this.ignoreContextMenu&&this.removeEventListener(window,"contextmenu",this.handleTopMoveEndCapture),this.enableKeyboardEvents&&this.removeEventListener(window,"keydown",this.handleCancelOnEscape,!0),this.uninstallSourceNodeRemovalObserver())},e.prototype.addEventListener=function(e,t,r,n){var o=f?{capture:n,passive:!1}:n;this.listenerTypes.forEach((function(n){var i=h[n][t];i&&e.addEventListener(i,r,o)}))},e.prototype.removeEventListener=function(e,t,r,n){var o=f?{capture:n,passive:!1}:n;this.listenerTypes.forEach((function(n){var i=h[n][t];i&&e.removeEventListener(i,r,o)}))},e.prototype.connectDragSource=function(e,t){var r=this,n=this.handleMoveStart.bind(this,e);return this.sourceNodes[e]=t,this.addEventListener(t,"start",n),function(){delete r.sourceNodes[e],r.removeEventListener(t,"start",n)}},e.prototype.connectDragPreview=function(e,t,r){var n=this;return this.sourcePreviewNodeOptions[e]=r,this.sourcePreviewNodes[e]=t,function(){delete n.sourcePreviewNodes[e],delete n.sourcePreviewNodeOptions[e]}},e.prototype.connectDropTarget=function(e,t){var r=this,n=function(n){var o;if(r.monitor.isDragging()){switch(n.type){case h.mouse.move:o={x:n.clientX,y:n.clientY};break;case h.touch.move:o={x:n.touches[0].clientX,y:n.touches[0].clientY}}var i=document.elementFromPoint(o.x,o.y),s=t.contains(i);return i===t||s?r.handleMove(n,e):void 0}};return this.addEventListener(document.body,"move",n),this.targetNodes[e]=t,function(){delete r.targetNodes[e],r.removeEventListener(document.body,"move",n)}},e.prototype.getTopMoveStartHandler=function(){return this.delayTouchStart||this.delayMouseStart?this.handleTopMoveStartDelay:this.handleTopMoveStart},e.prototype.installSourceNodeRemovalObserver=function(e){var t=this;this.uninstallSourceNodeRemovalObserver(),this.draggedSourceNode=e,this.draggedSourceNodeRemovalObserver=new MutationObserver((function(){e.parentElement||(t.resurrectSourceNode(),t.uninstallSourceNodeRemovalObserver())})),e&&e.parentElement&&this.draggedSourceNodeRemovalObserver.observe(e.parentElement,{childList:!0})},e.prototype.resurrectSourceNode=function(){this.draggedSourceNode.style.display="none",this.draggedSourceNode.removeAttribute("data-reactid"),document.body.appendChild(this.draggedSourceNode)},e.prototype.uninstallSourceNodeRemovalObserver=function(){this.draggedSourceNodeRemovalObserver&&this.draggedSourceNodeRemovalObserver.disconnect(),this.draggedSourceNodeRemovalObserver=null,this.draggedSourceNode=null},e}();t.TouchBackend=d,t.default=function(e){var t=function(t){return new d(t,e)};return function(e){return!!e.getMonitor}(e)?t(e):t}},308552:e=>{"use strict";e.exports=function(e,t,...r){if(!e){let e;if(void 0===t)e=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{let n=0;e=new Error(t.replace(/%s/g,(()=>String(r[n++])))),e.name="Invariant Violation"}throw e.framesToPop=1,e}}},311644:(e,t)=>{"use strict";var r="function"==typeof Symbol&&Symbol.for,n=r?Symbol.for("react.element"):60103,o=r?Symbol.for("react.portal"):60106,i=r?Symbol.for("react.fragment"):60107,s=r?Symbol.for("react.strict_mode"):60108,a=r?Symbol.for("react.profiler"):60114,u=r?Symbol.for("react.provider"):60109,c=r?Symbol.for("react.context"):60110,l=r?Symbol.for("react.async_mode"):60111,f=r?Symbol.for("react.concurrent_mode"):60111,h=r?Symbol.for("react.forward_ref"):60112,d=r?Symbol.for("react.suspense"):60113,p=r?Symbol.for("react.suspense_list"):60120,y=r?Symbol.for("react.memo"):60115,b=r?Symbol.for("react.lazy"):60116,g=r?Symbol.for("react.block"):60121,v=r?Symbol.for("react.fundamental"):60117,m=r?Symbol.for("react.responder"):60118,E=r?Symbol.for("react.scope"):60119;function _(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case l:case f:case i:case a:case s:case d:return e;default:switch(e=e&&e.$$typeof){case c:case h:case b:case y:case u:return e;default:return t}}case o:return t}}}function O(e){return _(e)===f}t.AsyncMode=l,t.ConcurrentMode=f,t.ContextConsumer=c,t.ContextProvider=u,t.Element=n,t.ForwardRef=h,t.Fragment=i,t.Lazy=b,t.Memo=y,t.Portal=o,t.Profiler=a,t.StrictMode=s,t.Suspense=d,t.isAsyncMode=function(e){return O(e)||_(e)===l},t.isConcurrentMode=O,t.isContextConsumer=function(e){return _(e)===c},t.isContextProvider=function(e){return _(e)===u},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===n},t.isForwardRef=function(e){return _(e)===h},t.isFragment=function(e){return _(e)===i},t.isLazy=function(e){return _(e)===b},t.isMemo=function(e){return _(e)===y},t.isPortal=function(e){return _(e)===o},t.isProfiler=function(e){return _(e)===a},t.isStrictMode=function(e){return _(e)===s},t.isSuspense=function(e){return _(e)===d},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===i||e===f||e===a||e===s||e===d||e===p||"object"==typeof e&&null!==e&&(e.$$typeof===b||e.$$typeof===y||e.$$typeof===u||e.$$typeof===c||e.$$typeof===h||e.$$typeof===v||e.$$typeof===m||e.$$typeof===E||e.$$typeof===g)},t.typeOf=_},311773:(e,t,r)=>{"use strict";function n(e){i.length||(o(),!0),i[i.length]=e}e.exports=n;var o,i=[],s=0;function a(){for(;s<i.length;){var e=s;if(s+=1,i[e].call(),s>1024){for(var t=0,r=i.length-s;t<r;t++)i[t]=i[t+s];i.length-=s,s=0}}i.length=0,s=0,!1}var u,c,l,f=void 0!==r.g?r.g:self,h=f.MutationObserver||f.WebKitMutationObserver;function d(e){return function(){var t=setTimeout(n,0),r=setInterval(n,50);function n(){clearTimeout(t),clearInterval(r),e()}}}"function"==typeof h?(u=1,c=new h(a),l=document.createTextNode(""),c.observe(l,{characterData:!0}),o=function(){u=-u,l.data=u}):o=d(a),n.requestFlush=o,n.makeRequestCallFromTimer=d},312098:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=class{constructor(e,t){this._source=e,this._transformer=t,this._done=!1,this._mappers=[]}onSubscribe(e){this._subscription=e}onNext(e){if(!this._sink)return void console.warn("premature onNext for processor, dropping value");let t=e;this._transformer&&(t=this._transformer(e));const r=this._mappers.reduce(((e,t)=>t(e)),t);this._sink.onNext(r)}onError(e){this._error=e,this._sink?this._sink.onError(e):console.warn("premature onError for processor, marking complete/errored")}onComplete(){this._done=!0,this._sink?this._sink.onComplete():console.warn("premature onError for processor, marking complete")}subscribe(e){this._source.subscribe&&this._source.subscribe(this),this._sink=e,this._sink.onSubscribe(this),this._error?this._sink.onError(this._error):this._done&&this._sink.onComplete()}map(e){return this._mappers.push(e),this}request(e){this._subscription&&this._subscription.request(e)}cancel(){this._subscription&&this._subscription.cancel()}}},316440:(e,t,r)=>{"use strict";r.d(t,{s:()=>a});var n=r(998083),o=r(335679),i=r(936791),s=r(291650);function a(e){return function(t){return 0===e?(0,s.I)():t.lift(new u(e))}}var u=function(){function e(e){if(this.total=e,this.total<0)throw new i.k}return e.prototype.call=function(e,t){return t.subscribe(new c(e,this.total))},e}(),c=function(e){function t(t,r){var n=e.call(this,t)||this;return n.total=r,n.count=0,n}return n.C6(t,e),t.prototype._next=function(e){var t=this.total,r=++this.count;r<=t&&(this.destination.next(e),r===t&&(this.destination.complete(),this.unsubscribe()))},t}(o.v)},317261:(e,t,r)=>{"use strict";r.d(t,{G:()=>n});var n=function(){function e(){return Error.call(this),this.message="no elements in sequence",this.name="EmptyError",this}return e.prototype=Object.create(Error.prototype),e}()},321175:(e,t,r)=>{"use strict";r.d(t,{G:()=>a});var n=1,o=function(){return Promise.resolve()}(),i={};function s(e){return e in i&&(delete i[e],!0)}var a={setImmediate:function(e){var t=n++;return i[t]=!0,o.then((function(){return s(t)&&e()})),t},clearImmediate:function(e){s(e)}}},322546:(e,t,r)=>{"use strict";r.d(t,{of:()=>s});var n=r(453535),o=r(550742),i=r(363295);function s(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=e[e.length-1];return(0,n.m)(r)?(e.pop(),(0,i.V)(e,r)):(0,o.c)(e)}},330238:(e,t,r)=>{"use strict";function n(e,t){return null===t?null===e:Array.isArray(e)?e.some((e=>e===t)):e===t}r.d(t,{A:()=>n})},335679:(e,t,r)=>{"use strict";r.d(t,{v:()=>l});var n=r(998083),o=r(387904),i=r(537727),s=r(997752),a=r(623276),u=r(558279),c=r(599884),l=function(e){function t(r,n,o){var s=e.call(this)||this;switch(s.syncErrorValue=null,s.syncErrorThrown=!1,s.syncErrorThrowable=!1,s.isStopped=!1,arguments.length){case 0:s.destination=i.I;break;case 1:if(!r){s.destination=i.I;break}if("object"==typeof r){r instanceof t?(s.syncErrorThrowable=r.syncErrorThrowable,s.destination=r,r.add(s)):(s.syncErrorThrowable=!0,s.destination=new f(s,r));break}default:s.syncErrorThrowable=!0,s.destination=new f(s,r,n,o)}return s}return n.C6(t,e),t.prototype[a.D]=function(){return this},t.create=function(e,r,n){var o=new t(e,r,n);return o.syncErrorThrowable=!1,o},t.prototype.next=function(e){this.isStopped||this._next(e)},t.prototype.error=function(e){this.isStopped||(this.isStopped=!0,this._error(e))},t.prototype.complete=function(){this.isStopped||(this.isStopped=!0,this._complete())},t.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,e.prototype.unsubscribe.call(this))},t.prototype._next=function(e){this.destination.next(e)},t.prototype._error=function(e){this.destination.error(e),this.unsubscribe()},t.prototype._complete=function(){this.destination.complete(),this.unsubscribe()},t.prototype._unsubscribeAndRecycle=function(){var e=this._parentOrParents;return this._parentOrParents=null,this.unsubscribe(),this.closed=!1,this.isStopped=!1,this._parentOrParents=e,this},t}(s.y),f=function(e){function t(t,r,n,s){var a,u=e.call(this)||this;u._parentSubscriber=t;var c=u;return(0,o.T)(r)?a=r:r&&(a=r.next,n=r.error,s=r.complete,r!==i.I&&(c=Object.create(r),(0,o.T)(c.unsubscribe)&&u.add(c.unsubscribe.bind(c)),c.unsubscribe=u.unsubscribe.bind(u))),u._context=c,u._next=a,u._error=n,u._complete=s,u}return n.C6(t,e),t.prototype.next=function(e){if(!this.isStopped&&this._next){var t=this._parentSubscriber;u.$.useDeprecatedSynchronousErrorHandling&&t.syncErrorThrowable?this.__tryOrSetError(t,this._next,e)&&this.unsubscribe():this.__tryOrUnsub(this._next,e)}},t.prototype.error=function(e){if(!this.isStopped){var t=this._parentSubscriber,r=u.$.useDeprecatedSynchronousErrorHandling;if(this._error)r&&t.syncErrorThrowable?(this.__tryOrSetError(t,this._error,e),this.unsubscribe()):(this.__tryOrUnsub(this._error,e),this.unsubscribe());else if(t.syncErrorThrowable)r?(t.syncErrorValue=e,t.syncErrorThrown=!0):(0,c.T)(e),this.unsubscribe();else{if(this.unsubscribe(),r)throw e;(0,c.T)(e)}}},t.prototype.complete=function(){var e=this;if(!this.isStopped){var t=this._parentSubscriber;if(this._complete){var r=function(){return e._complete.call(e._context)};u.$.useDeprecatedSynchronousErrorHandling&&t.syncErrorThrowable?(this.__tryOrSetError(t,r),this.unsubscribe()):(this.__tryOrUnsub(r),this.unsubscribe())}else this.unsubscribe()}},t.prototype.__tryOrUnsub=function(e,t){try{e.call(this._context,t)}catch(r){if(this.unsubscribe(),u.$.useDeprecatedSynchronousErrorHandling)throw r;(0,c.T)(r)}},t.prototype.__tryOrSetError=function(e,t,r){if(!u.$.useDeprecatedSynchronousErrorHandling)throw new Error("bad call");try{t.call(this._context,r)}catch(n){return u.$.useDeprecatedSynchronousErrorHandling?(e.syncErrorValue=n,e.syncErrorThrown=!0,!0):((0,c.T)(n),!0)}return!1},t.prototype._unsubscribe=function(){var e=this._parentSubscriber;this._context=null,this._parentSubscriber=null,e.unsubscribe()},t}(l)},339261:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TYPES_BY_AUTH_STRING=t.TYPES_BY_AUTH_ID=t.BEARER=t.SIMPLE=t.UNKNOWN_RESERVED_AUTH_TYPE=t.UNPARSEABLE_AUTH_TYPE=t.default=void 0;class r{constructor(e,t){this._string=e,this._identifier=t}static fromIdentifier(e){return e<0||e>127?n:a[e]}static fromString(e){if(!e)throw new Error("type must be non-null");return e===o.string?n:u.get(e)||n}get identifier(){return this._identifier}get string(){return this._string}toString(){return this._string}}t.default=r;const n=new r("UNPARSEABLE_AUTH_TYPE_DO_NOT_USE",-2);t.UNPARSEABLE_AUTH_TYPE=n;const o=new r("UNKNOWN_YET_RESERVED_DO_NOT_USE",-1);t.UNKNOWN_RESERVED_AUTH_TYPE=o;const i=new r("simple",0);t.SIMPLE=i;const s=new r("bearer",1);t.BEARER=s;const a=new Array(128);t.TYPES_BY_AUTH_ID=a;const u=new Map;t.TYPES_BY_AUTH_STRING=u;const c=[n,o,i,s];a.fill(o);for(const l of c)l.identifier>=0&&(a[l.identifier]=l,u.set(l.string,l));Object.seal&&Object.seal(a)},343696:()=>{},351702:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(845212),o=r(523173),i=r(662661),s=r(584600);const a=r(408406),u=r(409838),c=r(146462);function l({DecoratedComponent:e,createHandler:t,createMonitor:r,createConnector:l,registerHandler:f,containerDisplayName:h,getType:d,collect:p,options:y}){const{arePropsEqual:b=c}=y,g=e,v=e.displayName||e.name||"Component";class m extends n.Component{constructor(e){super(e),this.decoratedRef=n.createRef(),this.handleChange=()=>{const e=this.getCurrentState();c(e,this.state)||this.setState(e)},this.disposable=new i.DS,this.receiveProps(e),this.dispose()}getHandlerId(){return this.handlerId}getDecoratedComponentInstance(){return a(this.decoratedRef.current,"In order to access an instance of the decorated component, it must either be a class component or use React.forwardRef()"),this.decoratedRef.current}shouldComponentUpdate(e,t){return!b(e,this.props)||!c(t,this.state)}componentDidMount(){this.disposable=new i.DS,this.currentType=void 0,this.receiveProps(this.props),this.handleChange()}componentDidUpdate(e){b(this.props,e)||(this.receiveProps(this.props),this.handleChange())}componentWillUnmount(){this.dispose()}receiveProps(e){this.handler&&(this.handler.receiveProps(e),this.receiveType(d(e)))}receiveType(e){if(!this.handlerMonitor||!this.manager||!this.handlerConnector)return;if(e===this.currentType)return;this.currentType=e;const[t,r]=f(e,this.handler,this.manager);this.handlerId=t,this.handlerMonitor.receiveHandlerId(t),this.handlerConnector.receiveHandlerId(t);const n=this.manager.getMonitor().subscribeToStateChange(this.handleChange,{handlerIds:[t]});this.disposable.setDisposable(new i.ke(new i.jG(n),new i.jG(r)))}dispose(){this.disposable.dispose(),this.handlerConnector&&this.handlerConnector.receiveHandlerId(null)}getCurrentState(){if(!this.handlerConnector)return{};return p(this.handlerConnector.hooks,this.handlerMonitor,this.props)}render(){return n.createElement(o.ZC,null,(({dragDropManager:e})=>(this.receiveDragDropManager(e),"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame((()=>this.handlerConnector.reconnect())),n.createElement(g,Object.assign({},this.props,this.getCurrentState(),{ref:(0,s.Y)(g)?this.decoratedRef:null})))))}receiveDragDropManager(e){void 0===this.manager&&(a(void 0!==e,"Could not find the drag and drop manager in the context of %s. Make sure to wrap the top-level component of your app with DragDropContext. Read more: http://react-dnd.github.io/react-dnd/docs/troubleshooting#could-not-find-the-drag-and-drop-manager-in-the-context",v,v),void 0!==e&&(this.manager=e,this.handlerMonitor=r(e),this.handlerConnector=l(e.getBackend()),this.handler=t(this.handlerMonitor,this.decoratedRef)))}}return m.DecoratedComponent=e,m.displayName=`${h}(${v})`,u(m,e)}},352754:(e,t,r)=>{"use strict";r.d(t,{SO:()=>o,Yd:()=>n,iS:()=>l,n_:()=>i,pM:()=>a,sn:()=>c,v4:()=>s,z9:()=>u});const n="dnd-core/ADD_SOURCE",o="dnd-core/ADD_TARGET",i="dnd-core/REMOVE_SOURCE",s="dnd-core/REMOVE_TARGET";function a(e){return{type:n,payload:{sourceId:e}}}function u(e){return{type:o,payload:{targetId:e}}}function c(e){return{type:i,payload:{sourceId:e}}}function l(e){return{type:s,payload:{targetId:e}}}},354414:(e,t,r)=>{"use strict";r.d(t,{E:()=>a});var n,o=r(291650),i=r(322546),s=r(444153);n||(n={});var a=function(){function e(e,t,r){this.kind=e,this.value=t,this.error=r,this.hasValue="N"===e}return e.prototype.observe=function(e){switch(this.kind){case"N":return e.next&&e.next(this.value);case"E":return e.error&&e.error(this.error);case"C":return e.complete&&e.complete()}},e.prototype.do=function(e,t,r){switch(this.kind){case"N":return e&&e(this.value);case"E":return t&&t(this.error);case"C":return r&&r()}},e.prototype.accept=function(e,t,r){return e&&"function"==typeof e.next?this.observe(e):this.do(e,t,r)},e.prototype.toObservable=function(){switch(this.kind){case"N":return(0,i.of)(this.value);case"E":return(0,s.$)(this.error);case"C":return(0,o.I)()}throw new Error("unexpected notification kind value")},e.createNext=function(t){return void 0!==t?new e("N",t):e.undefinedValueNotification},e.createError=function(t){return new e("E",void 0,t)},e.createComplete=function(){return e.completeNotification},e.completeNotification=new e("C"),e.undefinedValueNotification=new e("N",void 0),e}()},362776:(e,t,r)=>{"use strict";var n;t.A=void 0;var o=((n=r(567427))&&n.__esModule?n:{default:n}).default;t.A=o},363295:(e,t,r)=>{"use strict";r.d(t,{V:()=>i});var n=r(226260),o=r(997752);function i(e,t){return new n.c((function(r){var n=new o.y,i=0;return n.add(t.schedule((function(){i!==e.length?(r.next(e[i++]),r.closed||n.add(this.schedule())):r.complete()}))),n}))}},366854:(e,t,r)=>{"use strict";function n(e,t,...r){0}r.d(t,{A:()=>n})},372222:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(451329),o=r(511894),i=r(390088);const s=r(408406),a={type:i.KR,payload:{clientOffset:null,sourceClientOffset:null}};function u(e){return function(t=[],r={publishSource:!0}){const{publishSource:u=!0,clientOffset:c,getSourceClientOffset:l}=r,f=e.getMonitor(),h=e.getRegistry();e.dispatch((0,n.P)(c)),function(e,t,r){s(!t.isDragging(),"Cannot call beginDrag while dragging.");for(const n of e)s(r.getSource(n),"Expected sourceIds to be registered.")}(t,f,h);const d=function(e,t){let r=null;for(let n=e.length-1;n>=0;n--)if(t.canDragSource(e[n])){r=e[n];break}return r}(t,f);if(null===d)return void e.dispatch(a);let p=null;c&&(!function(e){s("function"==typeof e,"When clientOffset is provided, getSourceClientOffset must be a function.")}(l),p=l(d)),e.dispatch((0,n.P)(c,p));const y=h.getSource(d).beginDrag(f,d);!function(e){s((0,o.Gv)(e),"Item must be an object.")}(y),h.pinSource(d);const b=h.getSourceType(d);return{type:i.Vw,payload:{itemType:b,item:y,sourceId:d,clientOffset:c||null,sourceClientOffset:p||null,isSourcePublic:!!u}}}}},372656:()=>{},374750:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(799769),o=r(753939);const i={initialSourceClientOffset:null,initialClientOffset:null,clientOffset:null};function s(e=i,t){const{payload:r}=t;switch(t.type){case n.KR:case n.Vw:return{initialSourceClientOffset:r.sourceClientOffset,initialClientOffset:r.clientOffset,clientOffset:r.clientOffset};case n.l6:return(0,o.Xf)(e.clientOffset,r.clientOffset)?e:Object.assign({},e,{clientOffset:r.clientOffset});case n.dU:case n.q2:return i;default:return e}}},374899:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.IdentitySerializers=t.IdentitySerializer=t.JsonSerializers=t.JsonSerializer=void 0;var n,o=r(514984),i=(n=r(308552))&&n.__esModule?n:{default:n};const s={deserialize:e=>{let t;if(null==e)return null;if("string"==typeof e)t=e;else if(o.LiteBuffer.isBuffer(e)){t=e.toString("utf8")}else{t=o.LiteBuffer.from(e).toString("utf8")}return JSON.parse(t)},serialize:JSON.stringify};t.JsonSerializer=s;const a={data:s,metadata:s};t.JsonSerializers=a;const u={deserialize:e=>((0,i.default)(null==e||"string"==typeof e||o.LiteBuffer.isBuffer(e)||e instanceof Uint8Array,"RSocketSerialization: Expected data to be a string, Buffer, or Uint8Array. Got `%s`.",e),e),serialize:e=>e};t.IdentitySerializer=u;const c={data:u,metadata:u};t.IdentitySerializers=c},375388:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n,o=r(914422),i=(n=r(308552))&&n.__esModule?n:{default:n},s=r(550558),a=r(28971),u=r(455190),c=r(82537),l=r(374899),f=r(739001);t.default=class{constructor(e){this._checkConfig(e),this._cancel=null,this._config=e,this._connection=null,this._socket=null}close(){this._config.transport.close()}connect(){return(0,i.default)(!this._connection,"RSocketClient: Unexpected call to connect(), already connected."),this._connection=new o.Single((e=>{const t=this._config.transport;let r;t.connectionStatus().subscribe({onNext:n=>{"CONNECTED"===n.kind?(r&&r.cancel(),e.onComplete(new h(this._config,new f.ReassemblyDuplexConnection(t)))):"ERROR"===n.kind?(r&&r.cancel(),e.onError(n.error)):"CLOSED"===n.kind&&(r&&r.cancel(),e.onError(new Error("RSocketClient: Connection closed.")))},onSubscribe:n=>{r=n,e.onSubscribe((()=>{n.cancel(),t.close()})),r.request(Number.MAX_SAFE_INTEGER)}}),t.connect()})),this._connection}_checkConfig(e){const t=e.setup,r=t&&t.keepAlive;try{const e=window&&window.navigator;r>3e4&&e&&e.userAgent&&(e.userAgent.includes("Trident")||e.userAgent.includes("Edg"))&&console.warn("rsocket-js: Due to a browser bug, Internet Explorer and Edge users may experience WebSocket instability with keepAlive values longer than 30 seconds.")}catch(n){}}};class h{constructor(e,t){let r,n;const i=e.leases;if(i){const e=i();r=new c.RequesterLeaseHandler(e._receiver),n=new c.ResponderLeaseHandler(e._sender,e._stats)}const{keepAlive:a,lifetime:l}=e.setup;this._machine=(0,u.createClientMachine)(t,(e=>t.receive().subscribe(e)),l,e.serializers,e.responder,e.errorHandler,r,n),t.sendOne(this._buildSetupFrame(e));const f=(0,o.every)(a).map((()=>({data:null,flags:s.FLAGS.RESPOND,lastReceivedPosition:0,streamId:s.CONNECTION_STREAM_ID,type:s.FRAME_TYPES.KEEPALIVE})));t.send(f)}fireAndForget(e){this._machine.fireAndForget(e)}requestResponse(e){return this._machine.requestResponse(e)}requestStream(e){return this._machine.requestStream(e)}requestChannel(e){return this._machine.requestChannel(e)}metadataPush(e){return this._machine.metadataPush(e)}close(){this._machine.close()}connectionStatus(){return this._machine.connectionStatus()}availability(){return this._machine.availability()}_buildSetupFrame(e){const{dataMimeType:t,keepAlive:r,lifetime:n,metadataMimeType:o,payload:i}=e.setup,u=e.serializers||l.IdentitySerializers,c=i?u.data.serialize(i.data):void 0,f=i?u.metadata.serialize(i.metadata):void 0;let h=0;return void 0!==f&&(h|=s.FLAGS.METADATA),{data:c,dataMimeType:t,flags:h|(e.leases?s.FLAGS.LEASE:0),keepAlive:r,lifetime:n,majorVersion:a.MAJOR_VERSION,metadata:f,metadataMimeType:o,minorVersion:a.MINOR_VERSION,resumeToken:null,streamId:s.CONNECTION_STREAM_ID,type:s.FRAME_TYPES.SETUP}}}},375527:(e,t,r)=>{"use strict";r.d(t,{c:()=>n});var n=function(){return Array.isArray||function(e){return e&&"number"==typeof e.length}}()},380349:(e,t,r)=>{"use strict";r.d(t,{b:()=>o});var n=r(998083),o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n.C6(t,e),t.prototype.flush=function(e){this.active=!0,this.scheduled=void 0;var t,r=this.actions,n=-1,o=r.length;e=e||r.shift();do{if(t=e.execute(e.state,e.delay))break}while(++n<o&&(e=r.shift()));if(this.active=!1,t){for(;++n<o&&(e=r.shift());)e.unsubscribe();throw t}},t}(r(799214).q)},385560:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var n=r(366854),o=r(351702),i=r(7879),s=r(915838),a=r(446246),u=r(221963),c=r(162743),l=r(807148);const f=r(408406);function h(e,t,r,h={}){(0,n.A)("DragSource","type, spec, collect[, options]",e,t,r,h);let d=e;"function"!=typeof e&&(f((0,c.A)(e),'Expected "type" provided as the first argument to DragSource to be a string, or a function that returns a string given the current props. Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source',e),d=()=>e),f((0,l.Qd)(t),'Expected "spec" provided as the second argument to DragSource to be a plain object. Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source',t);const p=(0,s.A)(t);return f("function"==typeof r,'Expected "collect" provided as the third argument to DragSource to be a function that returns a plain object of props to inject. Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source',r),f((0,l.Qd)(h),'Expected "options" provided as the fourth argument to DragSource to be a plain object when specified. Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source',r),function(e){return(0,o.A)({containerDisplayName:"DragSource",createHandler:p,registerHandler:i.A,createConnector:e=>new u.A(e),createMonitor:e=>new a.A(e),DecoratedComponent:e,getType:d,collect:r,options:h})}}},387904:(e,t,r)=>{"use strict";function n(e){return"function"==typeof e}r.d(t,{T:()=>n})},388909:(e,t,r)=>{"use strict";r.d(t,{t:()=>i});var n=r(226260),o=r(506104),i=new n.c(o.l)},390088:(e,t,r)=>{"use strict";r.d(t,{BS:()=>i,KR:()=>n,Vw:()=>o,dU:()=>u,l6:()=>s,q2:()=>a});const n="dnd-core/INIT_COORDS",o="dnd-core/BEGIN_DRAG",i="dnd-core/PUBLISH_DRAG_SOURCE",s="dnd-core/HOVER",a="dnd-core/DROP",u="dnd-core/END_DRAG"},391430:(e,t,r)=>{"use strict";r.d(t,{$:()=>o});var n=r(486228),o=new(r(380349).b)(n.M)},403732:(e,t,r)=>{"use strict";function n(e){return null!==e&&"object"==typeof e&&e.hasOwnProperty("current")}r.d(t,{i:()=>n})},407010:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(933573);function o(e,t,r){return(t=(0,n.A)(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}},408406:e=>{"use strict";e.exports=function(e,t,r,n,o,i,s,a){if(!e){var u;if(void 0===t)u=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var c=[r,n,o,i,s,a],l=0;(u=new Error(t.replace(/%s/g,(function(){return c[l++]})))).name="Invariant Violation"}throw u.framesToPop=1,u}}},409838:(e,t,r)=>{"use strict";var n=r(496192),o={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},i={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},s={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},a={};function u(e){return n.isMemo(e)?s:a[e.$$typeof]||o}a[n.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},a[n.Memo]=s;var c=Object.defineProperty,l=Object.getOwnPropertyNames,f=Object.getOwnPropertySymbols,h=Object.getOwnPropertyDescriptor,d=Object.getPrototypeOf,p=Object.prototype;e.exports=function e(t,r,n){if("string"!=typeof r){if(p){var o=d(r);o&&o!==p&&e(t,o,n)}var s=l(r);f&&(s=s.concat(f(r)));for(var a=u(t),y=u(r),b=0;b<s.length;++b){var g=s[b];if(!(i[g]||n&&n[g]||y&&y[g]||a&&a[g])){var v=h(r,g);try{c(t,g,v)}catch(m){}}}}return t}},410576:(e,t,r)=>{"use strict";r.d(t,{V:()=>s});var n=r(845212),o=r(88173),i=r(36160);function s(e){const t=(0,o.u)().getMonitor(),[r,s]=(0,i.F)(t,e);return(0,n.useEffect)((()=>t.subscribeToOffsetChange(s))),(0,n.useEffect)((()=>t.subscribeToStateChange(s))),r}},410826:(e,t,r)=>{"use strict";r.d(t,{x:()=>o});var n=r(211794);function o(e){return e&&"function"==typeof e[n.lJ]}},421346:e=>{"use strict";e.exports=function(e){return!(!e||e.length<8)&&(137===e[0]&&80===e[1]&&78===e[2]&&71===e[3]&&13===e[4]&&10===e[5]&&26===e[6]&&10===e[7])}},426242:(e,t,r)=>{"use strict";r.d(t,{S:()=>s});var n=r(226260),o=r(997752),i=r(211794);function s(e,t){if(!e)throw new Error("Iterable cannot be null");return new n.c((function(r){var n,s=new o.y;return s.add((function(){n&&"function"==typeof n.return&&n.return()})),s.add(t.schedule((function(){n=e[i.lJ](),s.add(t.schedule((function(){if(!r.closed){var e,t;try{var o=n.next();e=o.value,t=o.done}catch(i){return void r.error(i)}t?r.complete():(r.next(e),this.schedule())}})))}))),s}))}},431880:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;r(914422);var n,o=(n=r(308552))&&n.__esModule?n:{default:n},i=r(550558),s=r(374899),a=r(455190),u=r(82537),c=r(739001);function l(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}t.default=class{constructor(e){l(this,"_handleTransportComplete",(()=>{this._handleTransportError(new Error("RSocketServer: Connection closed unexpectedly."))})),l(this,"_handleTransportError",(e=>{this._connections.forEach((e=>{e.close()}))})),l(this,"_handleTransportConnection",(e=>{const t=new f;let r;(e=new c.ReassemblyDuplexConnection(e)).receive().subscribe(t.swap({onError:e=>console.error(e),onNext:r=>{switch(r.type){case i.FRAME_TYPES.RESUME:e.sendOne({code:i.ERROR_CODES.REJECTED_RESUME,flags:0,message:"RSocketServer: RESUME not supported.",streamId:i.CONNECTION_STREAM_ID,type:i.FRAME_TYPES.ERROR}),e.close();break;case i.FRAME_TYPES.SETUP:if(this._setupLeaseError(r)){e.sendOne({code:i.ERROR_CODES.INVALID_SETUP,flags:0,message:"RSocketServer: LEASE not supported.",streamId:i.CONNECTION_STREAM_ID,type:i.FRAME_TYPES.ERROR}),e.close();break}const s=this._getSerializers();let c,l;const f=this._config.leases;if(f){const e=f();c=new u.RequesterLeaseHandler(e._receiver),l=new u.ResponderLeaseHandler(e._sender,e._stats)}const h=(0,a.createServerMachine)(e,(e=>{t.swap(e)}),r.lifetime,s,this._config.errorHandler,c,l);try{const t=this._config.getRequestHandler(h,function(e,t){return{data:e.data.deserialize(t.data),metadata:e.metadata.deserialize(t.metadata)}}(s,r));h.setRequestHandler(t),this._connections.add(h),e.connectionStatus().subscribe({onNext:e=>{"CLOSED"!==e.kind&&"ERROR"!==e.kind||this._connections.delete(h)},onSubscribe:e=>e.request(Number.MAX_SAFE_INTEGER)})}catch(n){e.sendOne({code:i.ERROR_CODES.REJECTED_SETUP,flags:0,message:"Application rejected setup, reason: "+n.message,streamId:i.CONNECTION_STREAM_ID,type:i.FRAME_TYPES.ERROR}),e.close()}break;default:(0,o.default)(!1,"RSocketServer: Expected first frame to be SETUP or RESUME, got `%s`.",(0,i.getFrameTypeName)(r.type))}},onSubscribe:e=>{r=e,r.request(1)}}))})),this._config=e,this._connections=new Set,this._started=!1,this._subscription=null}start(){(0,o.default)(!this._started,"RSocketServer: Unexpected call to start(), already started."),this._started=!0,this._config.transport.start().subscribe({onComplete:this._handleTransportComplete,onError:this._handleTransportError,onNext:this._handleTransportConnection,onSubscribe:e=>{this._subscription=e,e.request(Number.MAX_SAFE_INTEGER)}})}stop(){this._subscription&&this._subscription.cancel(),this._config.transport.stop(),this._handleTransportError(new Error("RSocketServer: Connection terminated via stop()."))}_getSerializers(){return this._config.serializers||s.IdentitySerializers}_setupLeaseError(e){const t=(e.flags&i.FLAGS.LEASE)===i.FLAGS.LEASE,r=this._config.leases;return t&&!r}};class f{constructor(e){this._target=e}swap(e){return this._target=e,this._subscription&&this._target.onSubscribe&&this._target.onSubscribe(this._subscription),this}onComplete(){(0,o.default)(this._target,"must have target"),this._target.onComplete&&this._target.onComplete()}onError(e){(0,o.default)(this._target,"must have target"),this._target.onError&&this._target.onError(e)}onNext(e){(0,o.default)(this._target,"must have target"),this._target.onNext&&this._target.onNext(e)}onSubscribe(e){(0,o.default)(this._target,"must have target"),this._subscription=e,this._target.onSubscribe&&this._target.onSubscribe(e)}}},444153:(e,t,r)=>{"use strict";r.d(t,{$:()=>o});var n=r(226260);function o(e,t){return t?new n.c((function(r){return t.schedule(i,0,{error:e,subscriber:r})})):new n.c((function(t){return t.error(e)}))}function i(e){var t=e.error;e.subscriber.error(t)}},444191:(e,t,r)=>{"use strict";r.d(t,{b:()=>o});var n=r(261297);function o(e,t,r){return new n.A(e,t,r)}},446246:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});const n=r(408406);let o=!1,i=!1;class s{constructor(e){this.sourceId=null,this.internalMonitor=e.getMonitor()}receiveHandlerId(e){this.sourceId=e}getHandlerId(){return this.sourceId}canDrag(){n(!o,"You may not call monitor.canDrag() inside your canDrag() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return o=!0,this.internalMonitor.canDragSource(this.sourceId)}finally{o=!1}}isDragging(){if(!this.sourceId)return!1;n(!i,"You may not call monitor.isDragging() inside your isDragging() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return i=!0,this.internalMonitor.isDraggingSource(this.sourceId)}finally{i=!1}}subscribeToStateChange(e,t){return this.internalMonitor.subscribeToStateChange(e,t)}isDraggingSource(e){return this.internalMonitor.isDraggingSource(e)}isOverTarget(e,t){return this.internalMonitor.isOverTarget(e,t)}getTargetIds(){return this.internalMonitor.getTargetIds()}isSourcePublic(){return this.internalMonitor.isSourcePublic()}getSourceId(){return this.internalMonitor.getSourceId()}subscribeToOffsetChange(e){return this.internalMonitor.subscribeToOffsetChange(e)}canDragSource(e){return this.internalMonitor.canDragSource(e)}canDropOnTarget(e){return this.internalMonitor.canDropOnTarget(e)}getItemType(){return this.internalMonitor.getItemType()}getItem(){return this.internalMonitor.getItem()}getDropResult(){return this.internalMonitor.getDropResult()}didDrop(){return this.internalMonitor.didDrop()}getInitialClientOffset(){return this.internalMonitor.getInitialClientOffset()}getInitialSourceClientOffset(){return this.internalMonitor.getInitialSourceClientOffset()}getSourceClientOffset(){return this.internalMonitor.getSourceClientOffset()}getClientOffset(){return this.internalMonitor.getClientOffset()}getDifferenceFromInitialOffset(){return this.internalMonitor.getDifferenceFromInitialOffset()}}},448151:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});const n=r(408406);let o=!1;class i{constructor(e){this.targetId=null,this.internalMonitor=e.getMonitor()}receiveHandlerId(e){this.targetId=e}getHandlerId(){return this.targetId}subscribeToStateChange(e,t){return this.internalMonitor.subscribeToStateChange(e,t)}canDrop(){if(!this.targetId)return!1;n(!o,"You may not call monitor.canDrop() inside your canDrop() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target-monitor");try{return o=!0,this.internalMonitor.canDropOnTarget(this.targetId)}finally{o=!1}}isOver(e){return!!this.targetId&&this.internalMonitor.isOverTarget(this.targetId,e)}getItemType(){return this.internalMonitor.getItemType()}getItem(){return this.internalMonitor.getItem()}getDropResult(){return this.internalMonitor.getDropResult()}didDrop(){return this.internalMonitor.didDrop()}getInitialClientOffset(){return this.internalMonitor.getInitialClientOffset()}getInitialSourceClientOffset(){return this.internalMonitor.getInitialSourceClientOffset()}getSourceClientOffset(){return this.internalMonitor.getSourceClientOffset()}getClientOffset(){return this.internalMonitor.getClientOffset()}getDifferenceFromInitialOffset(){return this.internalMonitor.getDifferenceFromInitialOffset()}}},450117:(e,t,r)=>{"use strict";r.d(t,{B7:()=>f,PJ:()=>l});var n=r(998083),o=r(226260),i=r(335679),s=r(997752),a=r(878796),u=r(630084),c=r(623276),l=function(e){function t(t){var r=e.call(this,t)||this;return r.destination=t,r}return n.C6(t,e),t}(i.v),f=function(e){function t(){var t=e.call(this)||this;return t.observers=[],t.closed=!1,t.isStopped=!1,t.hasError=!1,t.thrownError=null,t}return n.C6(t,e),t.prototype[c.D]=function(){return new l(this)},t.prototype.lift=function(e){var t=new h(this,this);return t.operator=e,t},t.prototype.next=function(e){if(this.closed)throw new a.P;if(!this.isStopped)for(var t=this.observers,r=t.length,n=t.slice(),o=0;o<r;o++)n[o].next(e)},t.prototype.error=function(e){if(this.closed)throw new a.P;this.hasError=!0,this.thrownError=e,this.isStopped=!0;for(var t=this.observers,r=t.length,n=t.slice(),o=0;o<r;o++)n[o].error(e);this.observers.length=0},t.prototype.complete=function(){if(this.closed)throw new a.P;this.isStopped=!0;for(var e=this.observers,t=e.length,r=e.slice(),n=0;n<t;n++)r[n].complete();this.observers.length=0},t.prototype.unsubscribe=function(){this.isStopped=!0,this.closed=!0,this.observers=null},t.prototype._trySubscribe=function(t){if(this.closed)throw new a.P;return e.prototype._trySubscribe.call(this,t)},t.prototype._subscribe=function(e){if(this.closed)throw new a.P;return this.hasError?(e.error(this.thrownError),s.y.EMPTY):this.isStopped?(e.complete(),s.y.EMPTY):(this.observers.push(e),new u.Y(this,e))},t.prototype.asObservable=function(){var e=new o.c;return e.source=this,e},t.create=function(e,t){return new h(e,t)},t}(o.c),h=function(e){function t(t,r){var n=e.call(this)||this;return n.destination=t,n.source=r,n}return n.C6(t,e),t.prototype.next=function(e){var t=this.destination;t&&t.next&&t.next(e)},t.prototype.error=function(e){var t=this.destination;t&&t.error&&this.destination.error(e)},t.prototype.complete=function(){var e=this.destination;e&&e.complete&&this.destination.complete()},t.prototype._subscribe=function(e){return this.source?this.source.subscribe(e):s.y.EMPTY},t}(f)},451329:(e,t,r)=>{"use strict";r.d(t,{P:()=>o});var n=r(390088);function o(e,t){return{type:n.KR,payload:{sourceClientOffset:t||null,clientOffset:e||null}}}},453404:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(390088),o=r(511894);const i=r(408406);function s(e){return function(t={}){const r=e.getMonitor(),s=e.getRegistry();!function(e){i(e.isDragging(),"Cannot call drop while not dragging."),i(!e.didDrop(),"Cannot call drop twice during one drag operation.")}(r);const a=function(e){const t=e.getTargetIds().filter(e.canDropOnTarget,e);return t.reverse(),t}(r);a.forEach(((a,u)=>{const c=function(e,t,r,n){const s=r.getTarget(e);let a=s?s.drop(n,e):void 0;(function(e){i(void 0===e||(0,o.Gv)(e),"Drop result must either be an object or undefined.")})(a),void 0===a&&(a=0===t?{}:n.getDropResult());return a}(a,u,s,r),l={type:n.q2,payload:{dropResult:Object.assign({},t,c)}};e.dispatch(l)}))}}},453535:(e,t,r)=>{"use strict";function n(e){return e&&"function"==typeof e.schedule}r.d(t,{m:()=>n})},455190:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createServerMachine=function(e,t,r,n,o,i,s){return new h("SERVER",e,t,r,n,void 0,o,i,s)},t.createClientMachine=function(e,t,r,n,o,i,s,a){return new h("CLIENT",e,t,r,n,o,i,s,a)};var n,o=r(914422),i=r(550558),s=r(374899),a=(r(82537),(n=r(556697))&&n.__esModule?n:{default:n});function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach((function(t){l(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function l(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class f{constructor(e){this._responder=e||{}}setResponder(e){this._responder=e||{}}fireAndForget(e){if(this._responder.fireAndForget)try{this._responder.fireAndForget(e)}catch(t){console.error("fireAndForget threw an exception",t)}}requestResponse(e){let t;if(this._responder.requestResponse)try{return this._responder.requestResponse(e)}catch(r){console.error("requestResponse threw an exception",r),t=r}return o.Single.error(t||new Error("not implemented"))}requestStream(e){let t;if(this._responder.requestStream)try{return this._responder.requestStream(e)}catch(r){console.error("requestStream threw an exception",r),t=r}return o.Flowable.error(t||new Error("not implemented"))}requestChannel(e){let t;if(this._responder.requestChannel)try{return this._responder.requestChannel(e)}catch(r){console.error("requestChannel threw an exception",r),t=r}return o.Flowable.error(t||new Error("not implemented"))}metadataPush(e){let t;if(this._responder.metadataPush)try{return this._responder.metadataPush(e)}catch(r){console.error("metadataPush threw an exception",r),t=r}return o.Single.error(t||new Error("not implemented"))}}class h{constructor(e,t,r,n,o,a,u,c,h){l(this,"_connectionAvailability",1),l(this,"_handleTransportClose",(()=>{this._handleError(new Error("RSocket: The connection was closed."))})),l(this,"_handleError",(e=>{this._receivers.forEach((t=>{t.onError(e)})),this._receivers.clear(),this._subscriptions.forEach((e=>{e.cancel()})),this._subscriptions.clear(),this._connectionAvailability=0,this._dispose(this._requesterLeaseHandler,this._responderLeaseSenderDisposable);const t=this._keepAliveTimerHandle;t&&(clearTimeout(t),this._keepAliveTimerHandle=null)})),l(this,"_handleFrame",(e=>{const{streamId:t}=e;t===i.CONNECTION_STREAM_ID?this._handleConnectionFrame(e):this._handleStreamFrame(t,e)})),this._connection=t,this._requesterLeaseHandler=c,this._responderLeaseHandler=h,this._nextStreamId="CLIENT"===e?1:2,this._receivers=new Map,this._subscriptions=new Map,this._serializers=o||s.IdentitySerializers,this._requestHandler=new f(a),this._errorHandler=u,r({onComplete:this._handleTransportClose,onError:this._handleError,onNext:this._handleFrame,onSubscribe:e=>e.request(Number.MAX_SAFE_INTEGER)});const d=this._responderLeaseHandler;d&&(this._responderLeaseSenderDisposable=d.send(this._leaseFrameSender())),this._connection.connectionStatus().subscribe({onNext:e=>{"CLOSED"===e.kind?this._handleTransportClose():"ERROR"===e.kind&&this._handleError(e.error)},onSubscribe:e=>e.request(Number.MAX_SAFE_INTEGER)});this._keepAliveLastReceivedMillis=Date.now();const p=()=>{const e=Date.now()-this._keepAliveLastReceivedMillis;e>=n?this._handleConnectionError(new Error(`No keep-alive acks for ${n} millis`)):this._keepAliveTimerHandle=setTimeout(p,Math.max(100,n-e))};this._keepAliveTimerHandle=setTimeout(p,n)}setRequestHandler(e){this._requestHandler.setResponder(e)}close(){this._connection.close()}connectionStatus(){return this._connection.connectionStatus()}availability(){const e=this._requesterLeaseHandler,t=e?e.availability():1;return Math.min(this._connectionAvailability,t)}fireAndForget(e){if(this._useLeaseOrError(this._requesterLeaseHandler))return;const t=this._getNextStreamId(this._receivers),r=this._serializers.data.serialize(e.data),n=this._serializers.metadata.serialize(e.metadata),o={data:r,flags:void 0!==e.metadata?i.FLAGS.METADATA:0,metadata:n,streamId:t,type:i.FRAME_TYPES.REQUEST_FNF};this._connection.sendOne(o)}requestResponse(e){const t=this._useLeaseOrError(this._requesterLeaseHandler);if(t)return o.Single.error(new Error(t));const r=this._getNextStreamId(this._receivers);return new o.Single((t=>{this._receivers.set(r,{onComplete:()=>{},onError:e=>t.onError(e),onNext:e=>t.onComplete(e)});const n=this._serializers.data.serialize(e.data),o=this._serializers.metadata.serialize(e.metadata),s={data:n,flags:void 0!==e.metadata?i.FLAGS.METADATA:0,metadata:o,streamId:r,type:i.FRAME_TYPES.REQUEST_RESPONSE};this._connection.sendOne(s),t.onSubscribe((()=>{this._receivers.delete(r);const e={flags:0,streamId:r,type:i.FRAME_TYPES.CANCEL};this._connection.sendOne(e)}))}))}requestStream(e){const t=this._useLeaseOrError(this._requesterLeaseHandler);if(t)return o.Flowable.error(new Error(t));const r=this._getNextStreamId(this._receivers);return new o.Flowable((t=>{this._receivers.set(r,t);let n=!1;t.onSubscribe({cancel:()=>{if(this._receivers.delete(r),!n)return;const e={flags:0,streamId:r,type:i.FRAME_TYPES.CANCEL};this._connection.sendOne(e)},request:t=>{if(t>i.MAX_REQUEST_N&&(t=i.MAX_REQUEST_N),n){const e={flags:0,requestN:t,streamId:r,type:i.FRAME_TYPES.REQUEST_N};this._connection.sendOne(e)}else{n=!0;const o=this._serializers.data.serialize(e.data),s=this._serializers.metadata.serialize(e.metadata),a={data:o,flags:void 0!==e.metadata?i.FLAGS.METADATA:0,metadata:s,requestN:t,streamId:r,type:i.FRAME_TYPES.REQUEST_STREAM};this._connection.sendOne(a)}}})}),i.MAX_REQUEST_N)}requestChannel(e){const t=this._useLeaseOrError(this._requesterLeaseHandler);if(t)return o.Flowable.error(new Error(t));const r=this._getNextStreamId(this._receivers);let n=!1;return new o.Flowable((t=>{try{this._receivers.set(r,t);let o=!1;t.onSubscribe({cancel:()=>{if(this._receivers.delete(r),!o)return;const e={flags:0,streamId:r,type:i.FRAME_TYPES.CANCEL};this._connection.sendOne(e)},request:t=>{if(t>i.MAX_REQUEST_N&&(t=i.MAX_REQUEST_N),o){const e={flags:0,requestN:t,streamId:r,type:i.FRAME_TYPES.REQUEST_N};this._connection.sendOne(e)}else n?console.warn("RSocketClient: re-entrant call to request n before initial channel established."):(n=!0,e.subscribe({onComplete:()=>{this._sendStreamComplete(r)},onError:e=>{this._sendStreamError(r,e)},onNext:e=>{const n=this._serializers.data.serialize(e.data),s=this._serializers.metadata.serialize(e.metadata);if(o){const t={data:n,flags:i.FLAGS.NEXT|(void 0!==e.metadata?i.FLAGS.METADATA:0),metadata:s,streamId:r,type:i.FRAME_TYPES.PAYLOAD};this._connection.sendOne(t)}else{o=!0;const a={data:n,flags:void 0!==e.metadata?i.FLAGS.METADATA:0,metadata:s,requestN:t,streamId:r,type:i.FRAME_TYPES.REQUEST_CHANNEL};this._connection.sendOne(a)}},onSubscribe:e=>{this._subscriptions.set(r,e),e.request(1)}}))}})}catch(o){console.warn("Exception while subscribing to channel flowable:"+o)}}),i.MAX_REQUEST_N)}metadataPush(e){return new o.Single((t=>{const r={flags:0,metadata:this._serializers.metadata.serialize(e.metadata),streamId:0,type:i.FRAME_TYPES.METADATA_PUSH};this._connection.sendOne(r),t.onSubscribe((()=>{})),t.onComplete()}))}_getNextStreamId(e){const t=this._nextStreamId;do{this._nextStreamId=this._nextStreamId+2&i.MAX_STREAM_ID}while(0===this._nextStreamId||e.has(t));return t}_useLeaseOrError(e){if(e&&!e.use())return e.errorMessage()}_leaseFrameSender(){return e=>this._connection.sendOne({flags:0,metadata:e.metadata,requestCount:e.allowedRequests,streamId:i.CONNECTION_STREAM_ID,ttl:e.timeToLiveMillis,type:i.FRAME_TYPES.LEASE})}_dispose(...e){e.forEach((e=>{e&&e.dispose()}))}_isRequest(e){switch(e){case i.FRAME_TYPES.REQUEST_FNF:case i.FRAME_TYPES.REQUEST_RESPONSE:case i.FRAME_TYPES.REQUEST_STREAM:case i.FRAME_TYPES.REQUEST_CHANNEL:return!0;default:return!1}}_handleConnectionError(e){this._handleError(e),this._connection.close();const t=this._errorHandler;t&&t(e)}_handleConnectionFrame(e){switch(e.type){case i.FRAME_TYPES.ERROR:const t=(0,i.createErrorFromFrame)(e);this._handleConnectionError(t);break;case i.FRAME_TYPES.EXT:break;case i.FRAME_TYPES.KEEPALIVE:this._keepAliveLastReceivedMillis=Date.now(),(0,i.isRespond)(e.flags)&&this._connection.sendOne(c(c({},e),{},{flags:e.flags^i.FLAGS.RESPOND,lastReceivedPosition:0}));break;case i.FRAME_TYPES.LEASE:const r=this._requesterLeaseHandler;r&&r.receive(e);break;case i.FRAME_TYPES.METADATA_PUSH:this._handleMetadataPush(e);case i.FRAME_TYPES.REQUEST_CHANNEL:case i.FRAME_TYPES.REQUEST_FNF:case i.FRAME_TYPES.REQUEST_RESPONSE:case i.FRAME_TYPES.REQUEST_STREAM:case i.FRAME_TYPES.RESERVED:case i.FRAME_TYPES.RESUME:case i.FRAME_TYPES.RESUME_OK:}}_handleStreamFrame(e,t){if(this._isRequest(t.type)){const t=this._useLeaseOrError(this._responderLeaseHandler);if(t)return void this._sendStreamError(e,new Error(t))}switch(t.type){case i.FRAME_TYPES.CANCEL:this._handleCancel(e,t);break;case i.FRAME_TYPES.REQUEST_N:this._handleRequestN(e,t);break;case i.FRAME_TYPES.REQUEST_FNF:this._handleFireAndForget(e,t);break;case i.FRAME_TYPES.REQUEST_RESPONSE:this._handleRequestResponse(e,t);break;case i.FRAME_TYPES.REQUEST_STREAM:this._handleRequestStream(e,t);break;case i.FRAME_TYPES.REQUEST_CHANNEL:this._handleRequestChannel(e,t);break;case i.FRAME_TYPES.ERROR:const r=(0,i.createErrorFromFrame)(t);this._handleStreamError(e,r);break;case i.FRAME_TYPES.PAYLOAD:const n=this._receivers.get(e);if(null!=n){if((0,i.isNext)(t.flags)){const e={data:this._serializers.data.deserialize(t.data),metadata:this._serializers.metadata.deserialize(t.metadata)};n.onNext(e)}(0,i.isComplete)(t.flags)&&(this._receivers.delete(e),n.onComplete())}}}_handleCancel(e,t){const r=this._subscriptions.get(e);r&&(r.cancel(),this._subscriptions.delete(e))}_handleRequestN(e,t){const r=this._subscriptions.get(e);r&&r.request(t.requestN)}_handleFireAndForget(e,t){const r=this._deserializePayload(t);this._requestHandler.fireAndForget(r)}_handleRequestResponse(e,t){const r=this._deserializePayload(t);this._requestHandler.requestResponse(r).subscribe({onComplete:t=>{this._sendStreamPayload(e,t,!0)},onError:t=>this._sendStreamError(e,t),onSubscribe:t=>{const r={cancel:t,request:()=>{}};this._subscriptions.set(e,r)}})}_handleRequestStream(e,t){const r=this._deserializePayload(t);this._requestHandler.requestStream(r).subscribe({onComplete:()=>this._sendStreamComplete(e),onError:t=>this._sendStreamError(e,t),onNext:t=>this._sendStreamPayload(e,t),onSubscribe:r=>{this._subscriptions.set(e,r),r.request(t.requestN)}})}_handleRequestChannel(e,t){if(this._subscriptions.get(e))return;const r=new o.Flowable((r=>{let n=!0;r.onSubscribe({cancel:()=>{this._receivers.delete(e);const t={flags:0,streamId:e,type:i.FRAME_TYPES.CANCEL};this._connection.sendOne(t)},request:o=>{if(o>i.MAX_REQUEST_N&&(o=i.MAX_REQUEST_N),n&&o--,o>0){const t={flags:0,requestN:o,streamId:e,type:i.FRAME_TYPES.REQUEST_N};this._connection.sendOne(t)}n&&o>=0&&(n=!1,r.onNext(t))}})}),i.MAX_REQUEST_N),n=new o.FlowableProcessor(r,(e=>this._deserializePayload(e)));this._receivers.set(e,n),this._requestHandler.requestChannel(n).subscribe({onComplete:()=>this._sendStreamComplete(e),onError:t=>this._sendStreamError(e,t),onNext:t=>this._sendStreamPayload(e,t),onSubscribe:r=>{this._subscriptions.set(e,r),r.request(t.requestN)}})}_handleMetadataPush(e){const t=this._deserializeMetadataPushPayload(e);this._requestHandler.metadataPush(t).subscribe({onComplete:()=>{},onError:e=>{},onSubscribe:e=>{}})}_sendStreamComplete(e){this._subscriptions.delete(e),this._connection.sendOne({data:null,flags:i.FLAGS.COMPLETE,metadata:null,streamId:e,type:i.FRAME_TYPES.PAYLOAD})}_sendStreamError(e,t){this._subscriptions.delete(e),this._connection.sendOne({code:t instanceof a.default?t.errorCode:i.ERROR_CODES.APPLICATION_ERROR,flags:0,message:t.message,streamId:e,type:i.FRAME_TYPES.ERROR});const r=new Error(`terminated from the requester: ${t.message}`);this._handleStreamError(e,r)}_sendStreamPayload(e,t,r=!1){let n=i.FLAGS.NEXT;r&&(n|=i.FLAGS.COMPLETE,this._subscriptions.delete(e));const o=this._serializers.data.serialize(t.data),s=this._serializers.metadata.serialize(t.metadata);this._connection.sendOne({data:o,flags:n,metadata:s,streamId:e,type:i.FRAME_TYPES.PAYLOAD})}_deserializePayload(e){return function(e,t){return{data:e.data.deserialize(t.data),metadata:e.metadata.deserialize(t.metadata)}}(this._serializers,e)}_deserializeMetadataPushPayload(e){return function(e,t){return{data:null,metadata:e.metadata.deserialize(t.metadata)}}(this._serializers,e)}_handleStreamError(e,t){const r=this._receivers.get(e);null!=r&&(this._receivers.delete(e),r.onError(t))}}},460907:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(224892),o=r(704605),i=r(657111),s=r(13961),a=r(983923);class u{constructor(e){this.sourcePreviewNodes=new Map,this.sourcePreviewNodeOptions=new Map,this.sourceNodes=new Map,this.sourceNodeOptions=new Map,this.dragStartSourceIds=null,this.dropTargetIds=[],this.dragEnterTargetIds=[],this.currentNativeSource=null,this.currentNativeHandle=null,this.currentDragSourceNode=null,this.altKeyPressed=!1,this.mouseMoveTimeoutTimer=null,this.asyncEndDragFrameId=null,this.dragOverTargetIds=null,this.getSourceClientOffset=e=>(0,i.Dg)(this.sourceNodes.get(e)),this.endDragNativeItem=()=>{this.isDraggingNativeItem()&&(this.actions.endDrag(),this.registry.removeSource(this.currentNativeHandle),this.currentNativeHandle=null,this.currentNativeSource=null)},this.isNodeInDocument=e=>!!document&&document.body.contains(e)||!!this.window&&this.window.document.body.contains(e),this.endDragIfSourceWasRemovedFromDOM=()=>{const e=this.currentDragSourceNode;this.isNodeInDocument(e)||this.clearCurrentDragSourceNode()&&this.actions.endDrag()},this.handleTopDragStartCapture=()=>{this.clearCurrentDragSourceNode(),this.dragStartSourceIds=[]},this.handleTopDragStart=e=>{const{dragStartSourceIds:t}=this;this.dragStartSourceIds=null;const r=(0,i.b$)(e);this.monitor.isDragging()&&this.actions.endDrag(),this.actions.beginDrag(t||[],{publishSource:!1,getSourceClientOffset:this.getSourceClientOffset,clientOffset:r});const{dataTransfer:n}=e,o=(0,s.c)(n);if(this.monitor.isDragging()){if(n&&"function"==typeof n.setDragImage){const e=this.monitor.getSourceId(),t=this.sourceNodes.get(e),o=this.sourcePreviewNodes.get(e)||t;if(o){const{anchorX:e,anchorY:s,offsetX:a,offsetY:u}=this.getCurrentSourcePreviewNodeOptions(),c={anchorX:e,anchorY:s},l={offsetX:a,offsetY:u},f=(0,i.yA)(t,o,r,c,l);n.setDragImage(o,f.x,f.y)}}try{n.setData("application/json",{})}catch(a){}this.setCurrentDragSourceNode(e.target);const{captureDraggingState:t}=this.getCurrentSourcePreviewNodeOptions();t?this.actions.publishDragSource():setTimeout((()=>this.actions.publishDragSource()),0)}else if(o)this.beginDragNativeItem(o);else{if(n&&!n.types&&(e.target&&!e.target.hasAttribute||!e.target.hasAttribute("draggable")))return;e.preventDefault()}},this.handleTopDragEndCapture=()=>{this.clearCurrentDragSourceNode()&&this.actions.endDrag()},this.handleTopDragEnterCapture=e=>{this.dragEnterTargetIds=[];if(!this.enterLeaveCounter.enter(e.target)||this.monitor.isDragging())return;const{dataTransfer:t}=e,r=(0,s.c)(t);r&&this.beginDragNativeItem(r)},this.handleTopDragEnter=e=>{const{dragEnterTargetIds:t}=this;if(this.dragEnterTargetIds=[],!this.monitor.isDragging())return;this.altKeyPressed=e.altKey,(0,o.g)()||this.actions.hover(t,{clientOffset:(0,i.b$)(e)});t.some((e=>this.monitor.canDropOnTarget(e)))&&(e.preventDefault(),e.dataTransfer&&(e.dataTransfer.dropEffect=this.getCurrentDropEffect()))},this.handleTopDragOverCapture=()=>{this.dragOverTargetIds=[]},this.handleTopDragOver=e=>{const{dragOverTargetIds:t}=this;if(this.dragOverTargetIds=[],!this.monitor.isDragging())return e.preventDefault(),void(e.dataTransfer&&(e.dataTransfer.dropEffect="none"));this.altKeyPressed=e.altKey,this.actions.hover(t||[],{clientOffset:(0,i.b$)(e)});(t||[]).some((e=>this.monitor.canDropOnTarget(e)))?(e.preventDefault(),e.dataTransfer&&(e.dataTransfer.dropEffect=this.getCurrentDropEffect())):this.isDraggingNativeItem()?e.preventDefault():(e.preventDefault(),e.dataTransfer&&(e.dataTransfer.dropEffect="none"))},this.handleTopDragLeaveCapture=e=>{this.isDraggingNativeItem()&&e.preventDefault();this.enterLeaveCounter.leave(e.target)&&this.isDraggingNativeItem()&&this.endDragNativeItem()},this.handleTopDropCapture=e=>{this.dropTargetIds=[],e.preventDefault(),this.isDraggingNativeItem()&&this.currentNativeSource.mutateItemByReadingDataTransfer(e.dataTransfer),this.enterLeaveCounter.reset()},this.handleTopDrop=e=>{const{dropTargetIds:t}=this;this.dropTargetIds=[],this.actions.hover(t,{clientOffset:(0,i.b$)(e)}),this.actions.drop({dropEffect:this.getCurrentDropEffect()}),this.isDraggingNativeItem()?this.endDragNativeItem():this.endDragIfSourceWasRemovedFromDOM()},this.handleSelectStart=e=>{const t=e.target;"function"==typeof t.dragDrop&&("INPUT"===t.tagName||"SELECT"===t.tagName||"TEXTAREA"===t.tagName||t.isContentEditable||(e.preventDefault(),t.dragDrop()))},this.actions=e.getActions(),this.monitor=e.getMonitor(),this.registry=e.getRegistry(),this.context=e.getContext(),this.enterLeaveCounter=new n.A(this.isNodeInDocument)}get window(){return this.context&&this.context.window?this.context.window:"undefined"!=typeof window?window:void 0}setup(){if(void 0!==this.window){if(this.window.__isReactDndBackendSetUp)throw new Error("Cannot have two HTML5 backends at the same time.");this.window.__isReactDndBackendSetUp=!0,this.addEventListeners(this.window)}}teardown(){void 0!==this.window&&(this.window.__isReactDndBackendSetUp=!1,this.removeEventListeners(this.window),this.clearCurrentDragSourceNode(),this.asyncEndDragFrameId&&this.window.cancelAnimationFrame(this.asyncEndDragFrameId))}connectDragPreview(e,t,r){return this.sourcePreviewNodeOptions.set(e,r),this.sourcePreviewNodes.set(e,t),()=>{this.sourcePreviewNodes.delete(e),this.sourcePreviewNodeOptions.delete(e)}}connectDragSource(e,t,r){this.sourceNodes.set(e,t),this.sourceNodeOptions.set(e,r);const n=t=>this.handleDragStart(t,e),o=e=>this.handleSelectStart(e);return t.setAttribute("draggable","true"),t.addEventListener("dragstart",n),t.addEventListener("selectstart",o),()=>{this.sourceNodes.delete(e),this.sourceNodeOptions.delete(e),t.removeEventListener("dragstart",n),t.removeEventListener("selectstart",o),t.setAttribute("draggable","false")}}connectDropTarget(e,t){const r=t=>this.handleDragEnter(t,e),n=t=>this.handleDragOver(t,e),o=t=>this.handleDrop(t,e);return t.addEventListener("dragenter",r),t.addEventListener("dragover",n),t.addEventListener("drop",o),()=>{t.removeEventListener("dragenter",r),t.removeEventListener("dragover",n),t.removeEventListener("drop",o)}}addEventListeners(e){e.addEventListener&&(e.addEventListener("dragstart",this.handleTopDragStart),e.addEventListener("dragstart",this.handleTopDragStartCapture,!0),e.addEventListener("dragend",this.handleTopDragEndCapture,!0),e.addEventListener("dragenter",this.handleTopDragEnter),e.addEventListener("dragenter",this.handleTopDragEnterCapture,!0),e.addEventListener("dragleave",this.handleTopDragLeaveCapture,!0),e.addEventListener("dragover",this.handleTopDragOver),e.addEventListener("dragover",this.handleTopDragOverCapture,!0),e.addEventListener("drop",this.handleTopDrop),e.addEventListener("drop",this.handleTopDropCapture,!0))}removeEventListeners(e){e.removeEventListener&&(e.removeEventListener("dragstart",this.handleTopDragStart),e.removeEventListener("dragstart",this.handleTopDragStartCapture,!0),e.removeEventListener("dragend",this.handleTopDragEndCapture,!0),e.removeEventListener("dragenter",this.handleTopDragEnter),e.removeEventListener("dragenter",this.handleTopDragEnterCapture,!0),e.removeEventListener("dragleave",this.handleTopDragLeaveCapture,!0),e.removeEventListener("dragover",this.handleTopDragOver),e.removeEventListener("dragover",this.handleTopDragOverCapture,!0),e.removeEventListener("drop",this.handleTopDrop),e.removeEventListener("drop",this.handleTopDropCapture,!0))}getCurrentSourceNodeOptions(){const e=this.monitor.getSourceId(),t=this.sourceNodeOptions.get(e);return Object.assign({dropEffect:this.altKeyPressed?"copy":"move"},t||{})}getCurrentDropEffect(){return this.isDraggingNativeItem()?"copy":this.getCurrentSourceNodeOptions().dropEffect}getCurrentSourcePreviewNodeOptions(){const e=this.monitor.getSourceId(),t=this.sourcePreviewNodeOptions.get(e);return Object.assign({anchorX:.5,anchorY:.5,captureDraggingState:!1},t||{})}isDraggingNativeItem(){const e=this.monitor.getItemType();return Object.keys(a).some((t=>a[t]===e))}beginDragNativeItem(e){this.clearCurrentDragSourceNode(),this.currentNativeSource=(0,s.h)(e),this.currentNativeHandle=this.registry.addSource(e,this.currentNativeSource),this.actions.beginDrag([this.currentNativeHandle])}setCurrentDragSourceNode(e){this.clearCurrentDragSourceNode(),this.currentDragSourceNode=e;this.mouseMoveTimeoutTimer=setTimeout((()=>this.window&&this.window.addEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)),1e3)}clearCurrentDragSourceNode(){return!!this.currentDragSourceNode&&(this.currentDragSourceNode=null,this.window&&(this.window.clearTimeout(this.mouseMoveTimeoutTimer||void 0),this.window.removeEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)),this.mouseMoveTimeoutTimer=null,!0)}handleDragStart(e,t){this.dragStartSourceIds||(this.dragStartSourceIds=[]),this.dragStartSourceIds.unshift(t)}handleDragEnter(e,t){this.dragEnterTargetIds.unshift(t)}handleDragOver(e,t){null===this.dragOverTargetIds&&(this.dragOverTargetIds=[]),this.dragOverTargetIds.unshift(t)}handleDrop(e,t){this.dropTargetIds.unshift(t)}}},472537:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Variable=t.Snapshot=t.currentSnapshot=void 0;const r=Symbol("Snapshot.wrapped");t.currentSnapshot={value:void 0};class n{constructor(){null!=t.currentSnapshot.value&&(this.parentSnapshot=t.currentSnapshot.value),this.run=this.run.bind(this)}run(e,...r){const n=t.currentSnapshot.value;t.currentSnapshot.value=this;try{return e.call(null,...r)}finally{t.currentSnapshot.value=n}}static wrap(e){if("function"!=typeof e)return e;if(e[r])return console.warn("Snapshot.wrap should not be called more than once on a function"),e;const o=new n,i=function(...r){const n=t.currentSnapshot.value;t.currentSnapshot.value=o;try{return e.call(this,...r)}finally{t.currentSnapshot.value=n}};return i[r]=!0,i}}t.Snapshot=n;t.Variable=class{constructor(e){this.snapshotToValue=new WeakMap,this.name=(null==e?void 0:e.name)||"",this.defaultValue=null==e?void 0:e.defaultValue}get(){let e=t.currentSnapshot.value;for(;null!=e;){if(this.snapshotToValue.has(e))return this.snapshotToValue.get(e);e=e.parentSnapshot}return this.defaultValue}run(e,t,...r){const o=new n;return this.snapshotToValue.set(o,e),o.run(t,...r)}},t.currentSnapshot.value=new n},482319:(e,t,r)=>{"use strict";r.d(t,{R:()=>o});var n=r(998083),o=function(e){function t(t,r,n){var o=e.call(this)||this;return o.parent=t,o.outerValue=r,o.outerIndex=n,o.index=0,o}return n.C6(t,e),t.prototype._next=function(e){this.parent.notifyNext(this.outerValue,e,this.outerIndex,this.index++,this)},t.prototype._error=function(e){this.parent.notifyError(e,this),this.unsubscribe()},t.prototype._complete=function(){this.parent.notifyComplete(this),this.unsubscribe()},t}(r(335679).v)},486228:(e,t,r)=>{"use strict";r.d(t,{M:()=>i});var n=r(998083),o=r(321175),i=function(e){function t(t,r){var n=e.call(this,t,r)||this;return n.scheduler=t,n.work=r,n}return n.C6(t,e),t.prototype.requestAsyncId=function(t,r,n){return void 0===n&&(n=0),null!==n&&n>0?e.prototype.requestAsyncId.call(this,t,r,n):(t.actions.push(this),t.scheduled||(t.scheduled=o.G.setImmediate(t.flush.bind(t,null))))},t.prototype.recycleAsyncId=function(t,r,n){if(void 0===n&&(n=0),null!==n&&n>0||null===n&&this.delay>0)return e.prototype.recycleAsyncId.call(this,t,r,n);0===t.actions.length&&(o.G.clearImmediate(r),t.scheduled=void 0)},t}(r(237157).R)},491577:(e,t,r)=>{"use strict";r.d(t,{x:()=>n});class n{constructor(e){this.config=e,this.item={},Object.keys(this.config.exposeProperties).forEach((e=>{Object.defineProperty(this.item,e,{configurable:!0,enumerable:!0,get:()=>(console.warn(`Browser doesn't allow reading "${e}" until the drop event.`),null)})}))}mutateItemByReadingDataTransfer(e){const t={};e&&Object.keys(this.config.exposeProperties).forEach((r=>{t[r]={value:this.config.exposeProperties[r](e,this.config.matchesTypes)}})),Object.defineProperties(this.item,t)}canDrag(){return!0}beginDrag(){return this.item}isDragging(e,t){return t===e.getSourceId()}endDrag(){}}},496192:(e,t,r)=>{"use strict";e.exports=r(311644)},502678:(e,t,r)=>{"use strict";r.d(t,{c:()=>f});var n=r(124105),o=r(168287),i=r(363295),s=r(426242),a=r(928662),u=r(72231),c=r(1408),l=r(410826);function f(e,t){if(null!=e){if((0,a.l)(e))return(0,n.x)(e,t);if((0,u.y)(e))return(0,o.P)(e,t);if((0,c.X)(e))return(0,i.V)(e,t);if((0,l.x)(e)||"string"==typeof e)return(0,s.S)(e,t)}throw new TypeError((null!==e&&typeof e||e)+" is not observable")}},506104:(e,t,r)=>{"use strict";function n(){}r.d(t,{l:()=>n})},506800:(e,t,r)=>{"use strict";r.d(t,{K:()=>o});var n=r(290676);function o(){return(0,n.U)(1)}},511894:(e,t,r)=>{"use strict";function n(e,t,r){return t.split(".").reduce(((e,t)=>e&&e[t]?e[t]:r||null),e)}function o(e,t){return e.filter((e=>e!==t))}function i(e){return"object"==typeof e}function s(e,t){const r=new Map,n=e=>r.set(e,r.has(e)?r.get(e)+1:1);e.forEach(n),t.forEach(n);const o=[];return r.forEach(((e,t)=>{1===e&&o.push(t)})),o}function a(e,t){return e.filter((e=>t.indexOf(e)>-1))}r.d(t,{E$:()=>a,FF:()=>o,Gv:()=>i,I8:()=>s,Jt:()=>n})},514984:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.LiteBuffer=t.Buffer=void 0;var n,o=(n=r(372656))&&n.__esModule?n:{default:n};const i=void 0!==r.g&&r.g.hasOwnProperty("Buffer"),s=o.default.hasOwnProperty("Buffer");function a(e){return null==e||"utf8"===e||"utf-8"===e?"utf8":function(e){switch(e.length){case 4:if("UTF8"===e)return"utf8";if("ucs2"===e||"UCS2"===e)return"utf16le";if("utf8"===(e=`${e}`.toLowerCase()))return"utf8";if("ucs2"===e)return"utf16le";break;case 3:if("hex"===e||"HEX"===e||"hex"===`${e}`.toLowerCase())return"hex";break;case 5:if("ascii"===e)return"ascii";if("ucs-2"===e)return"utf16le";if("UTF-8"===e)return"utf8";if("ASCII"===e)return"ascii";if("UCS-2"===e)return"utf16le";if("utf-8"===(e=`${e}`.toLowerCase()))return"utf8";if("ascii"===e)return"ascii";if("ucs-2"===e)return"utf16le";break;case 6:if("base64"===e)return"base64";if("latin1"===e||"binary"===e)return"latin1";if("BASE64"===e)return"base64";if("LATIN1"===e||"BINARY"===e)return"latin1";if("base64"===(e=`${e}`.toLowerCase()))return"base64";if("latin1"===e||"binary"===e)return"latin1";break;case 7:if("utf16le"===e||"UTF16LE"===e||"utf16le"===`${e}`.toLowerCase())return"utf16le";break;case 8:if("utf-16le"===e||"UTF-16LE"===e||"utf-16le"===`${e}`.toLowerCase())return"utf16le";break;default:if(""===e)return"utf8"}}(e)}function u(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}const c=["base64","hex","ascii","binary","latin1","ucs2","utf16le"];function l(e="utf8",t=!0){if("string"!=typeof e||t&&""===e){if(!t)return"utf8";throw new TypeError(`Unknown encoding: ${e}`)}const r=a(e);if(void 0===r)throw new TypeError(`Unknown encoding: ${e}`);return c.includes(e)&&function(e){throw new Error(e?`Not implemented: ${e}`:"Not implemented")}(`"${e}" encoding`),r}const f={ascii:{byteLength:e=>e.length},base64:{byteLength:e=>function(e,t){61===e.charCodeAt(t-1)&&t--;t>1&&61===e.charCodeAt(t-1)&&t--;return 3*t>>>2}(e,e.length)},hex:{byteLength:e=>e.length>>>1},latin1:{byteLength:e=>e.length},ucs2:{byteLength:e=>2*e.length},utf16le:{byteLength:e=>2*e.length},utf8:{byteLength:e=>d(e).length}};const h=4096;function d(e,t=1/0){let r,n=t;const o=e.length;let i=null;const s=[];for(let a=0;a<o;++a){if(r=e.charCodeAt(a),r>55295&&r<57344){if(!i){if(r>56319){(n-=3)>-1&&s.push(239,191,189);continue}if(a+1===o){(n-=3)>-1&&s.push(239,191,189);continue}i=r;continue}if(r<56320){(n-=3)>-1&&s.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(n-=3)>-1&&s.push(239,191,189);if(i=null,r<128){if((n-=1)<0)break;s.push(r)}else if(r<2048){if((n-=2)<0)break;s.push(r>>6|192,63&r|128)}else if(r<65536){if((n-=3)<0)break;s.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((n-=4)<0)break;s.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return s}function p(e,t,r){r=Math.min(e.length,r);const n=[];let o=t;for(;o<r;){const t=e[o];let i=null,s=t>239?4:t>223?3:t>191?2:1;if(o+s<=r){let r,n,a,u;switch(s){case 1:t<128&&(i=t);break;case 2:r=e[o+1],128==(192&r)&&(u=(31&t)<<6|63&r,u>127&&(i=u));break;case 3:r=e[o+1],n=e[o+2],128==(192&r)&&128==(192&n)&&(u=(15&t)<<12|(63&r)<<6|63&n,u>2047&&(u<55296||u>57343)&&(i=u));break;case 4:r=e[o+1],n=e[o+2],a=e[o+3],128==(192&r)&&128==(192&n)&&128==(192&a)&&(u=(15&t)<<18|(63&r)<<12|(63&n)<<6|63&a,u>65535&&u<1114112&&(i=u))}}null===i?(i=65533,s=1):i>65535&&(i-=65536,n.push(i>>>10&1023|55296),i=56320|1023&i),n.push(i),o+=s}return function(e){const t=e.length;if(t<=h)return String.fromCharCode.apply(String,e);let r="",n=0;for(;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=h));return r}(n)}class y extends Uint8Array{constructor(e,t,r){if("number"==typeof e)super(e);else{super(e,t||0,r||(u(e,Array)?e.length:e.byteLength))}}static alloc(e,t=0,r="utf8"){if("number"!=typeof e)throw new TypeError('The "size" argument must be of type number. Received type '+typeof e);const n=new y(e);if(0===e)return n;let o;if("string"==typeof t)r=l(r),1===t.length&&"utf8"===r?n.fill(t.charCodeAt(0)):o=y.from(t,r);else if("number"==typeof t)n.fill(t);else if(u(t,Uint8Array)){if(0===t.length)throw new TypeError(`The argument "value" is invalid. Received ${t.constructor.name} []`);o=t}if(o){o.length>n.length&&(o=o.subarray(0,n.length));let t=0;for(;t<e&&(n.set(o,t),t+=o.length,!(t+o.length>=e)););t!==e&&n.set(o.subarray(0,e-t),t)}return n}static allocUnsafe(e){return new y(e)}static byteLength(e,t="utf8"){return"string"!=typeof e?e.byteLength:(t=a(t)||"utf8",f[t].byteLength(e))}static concat(e,t){if(null==t){t=0;for(const r of e)t+=r.length}const r=new y(t);let n=0;for(const o of e)r.set(o,n),n+=o.length;return r}static from(e,t,r){const n="string"==typeof t?void 0:t;let o="string"==typeof t?t:void 0;if("string"==typeof e||"String"===e.constructor.name){if(e=e.toString(),o=l(o,!1),"utf8"===o)return"undefined"!=typeof TextEncoder?new y((new TextEncoder).encode(e).buffer):new y(d(e));throw new TypeError("Unknown encoding: "+o)}return new y(e,n,r)}static isBuffer(e){return u(e,y)||!i&&s&&u(e,Uint8Array)}static isEncoding(e){return"string"==typeof e&&0!==e.length&&void 0!==a(e)}copy(e,t=0,r=0,n=this.length){const o=this.subarray(r,n);return e.set(o,t),o.length}equals(e){if(!u(e,Uint8Array))throw new TypeError('The "otherBuffer" argument must be an instance of Buffer or Uint8Array. Received type '+typeof e);if(this===e)return!0;if(this.byteLength!==e.byteLength)return!1;for(let t=0;t<this.length;t++)if(this[t]!==e[t])return!1;return!0}readDoubleBE(e=0){return new DataView(this.buffer,this.byteOffset,this.byteLength).getFloat64(e)}readDoubleLE(e=0){return new DataView(this.buffer,this.byteOffset,this.byteLength).getFloat64(e,!0)}readFloatBE(e=0){return new DataView(this.buffer,this.byteOffset,this.byteLength).getFloat32(e)}readFloatLE(e=0){return new DataView(this.buffer,this.byteOffset,this.byteLength).getFloat32(e,!0)}readInt8(e=0){return new DataView(this.buffer,this.byteOffset,this.byteLength).getInt8(e)}readInt16BE(e=0){return new DataView(this.buffer,this.byteOffset,this.byteLength).getInt16(e)}readInt16LE(e=0){return new DataView(this.buffer,this.byteOffset,this.byteLength).getInt16(e,!0)}readInt32BE(e=0){return new DataView(this.buffer,this.byteOffset,this.byteLength).getInt32(e)}readInt32LE(e=0){return new DataView(this.buffer,this.byteOffset,this.byteLength).getInt32(e,!0)}readUInt8(e=0){return new DataView(this.buffer,this.byteOffset,this.byteLength).getUint8(e)}readUInt16BE(e=0){return new DataView(this.buffer,this.byteOffset,this.byteLength).getUint16(e)}readUInt16LE(e=0){return new DataView(this.buffer,this.byteOffset,this.byteLength).getUint16(e,!0)}readUInt32BE(e=0){return new DataView(this.buffer,this.byteOffset,this.byteLength).getUint32(e)}readUInt32LE(e=0){return new DataView(this.buffer,this.byteOffset,this.byteLength).getUint32(e,!0)}slice(e=0,t=this.length){return this.subarray(e,t)}subarray(e=0,t=this.length){return new y(super.subarray(e,t))}toJSON(){return{data:Array.from(this),type:"Buffer"}}toString(e="utf8",t=0,r=this.length){if(e=l(e),"undefined"!=typeof TextDecoder){const e=this.subarray(t,r);return(new TextDecoder).decode(e)}return this.slowToString(e,t,r)}slowToString(e="utf8",t=0,r=this.length){if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(t>>>=0))return"";if("utf8"===(e=l(e)))return p(this,t,r);throw new TypeError("Unsupported encoding: "+e)}write(e,t=0,r=this.length,n="utf8"){if("utf8"===(n=l(n))){if("undefined"!=typeof TextEncoder){const n=(new TextEncoder).encode(e);return this.set(n,t),n.byteLength>r-t?r-t:n.byteLength}return function(e,t,r,n){return function(e,t,r,n){let o=0;for(;o<n&&!(o+r>=t.length||o>=e.length);++o)t[o+r]=e[o];return o}(d(t,e.length-r),e,r,n)}(this,e,t,r)}throw new TypeError("Unknown encoding: "+n)}writeDoubleBE(e,t=0){return new DataView(this.buffer,this.byteOffset,this.byteLength).setFloat64(t,e),t+8}writeDoubleLE(e,t=0){return new DataView(this.buffer,this.byteOffset,this.byteLength).setFloat64(t,e,!0),t+8}writeFloatBE(e,t=0){return new DataView(this.buffer,this.byteOffset,this.byteLength).setFloat32(t,e),t+4}writeFloatLE(e,t=0){return new DataView(this.buffer,this.byteOffset,this.byteLength).setFloat32(t,e,!0),t+4}writeInt8(e,t=0){return new DataView(this.buffer,this.byteOffset,this.byteLength).setInt8(t,e),t+1}writeInt16BE(e,t=0){return new DataView(this.buffer,this.byteOffset,this.byteLength).setInt16(t,e),t+2}writeInt16LE(e,t=0){return new DataView(this.buffer,this.byteOffset,this.byteLength).setInt16(t,e,!0),t+2}writeInt32BE(e,t=0){return new DataView(this.buffer,this.byteOffset,this.byteLength).setUint32(t,e),t+4}writeInt32LE(e,t=0){return new DataView(this.buffer,this.byteOffset,this.byteLength).setInt32(t,e,!0),t+4}writeUInt8(e,t=0){return new DataView(this.buffer,this.byteOffset,this.byteLength).setUint8(t,e),t+1}writeUInt16BE(e,t=0){return new DataView(this.buffer,this.byteOffset,this.byteLength).setUint16(t,e),t+2}writeUInt16LE(e,t=0){return new DataView(this.buffer,this.byteOffset,this.byteLength).setUint16(t,e,!0),t+2}writeUInt32BE(e,t=0){return new DataView(this.buffer,this.byteOffset,this.byteLength).setUint32(t,e),t+4}writeUInt32LE(e,t=0){return new DataView(this.buffer,this.byteOffset,this.byteLength).setUint32(t,e,!0),t+4}}t.Buffer=y,i||(s&&Object.defineProperty(o.default,"Buffer",{configurable:!0,enumerable:!1,value:y,writable:!0}),Object.defineProperty(window,"Buffer",{configurable:!0,enumerable:!1,value:y,writable:!0}));const b=i?r.g.Buffer:y;t.LiteBuffer=b},515807:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CONNECTION_STATUS=void 0;const r={CLOSED:Object.freeze({kind:"CLOSED"}),CONNECTED:Object.freeze({kind:"CONNECTED"}),CONNECTING:Object.freeze({kind:"CONNECTING"}),NOT_CONNECTED:Object.freeze({kind:"NOT_CONNECTED"})};t.CONNECTION_STATUS=r},521390:e=>{"use strict";e.exports=function(e,t,...r){if(!e){let e;if(void 0===t)e=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{let n=0;e=new Error(t.replace(/%s/g,(()=>String(r[n++])))),e.name="Invariant Violation"}throw e.framesToPop=1,e}}},523173:(e,t,r)=>{"use strict";r.d(t,{JY:()=>d,ZC:()=>l,_O:()=>c});var n=r(845212),o=r(929305),i=r(366854),s=r(584600);const a=r(408406),u=r(409838),c=n.createContext({dragDropManager:void 0}),{Consumer:l,Provider:f}=c;function h(e,t,r){return{dragDropManager:(0,o.b)(e,t,r)}}function d(e,t,r){(0,i.A)("DragDropContext","backend",e);const o=h(e,t,r);return function(e){const t=e,r=t.displayName||t.name||"Component";class i extends n.Component{constructor(){super(...arguments),this.ref=n.createRef(),this.getManager=()=>o.dragDropManager}getDecoratedComponentInstance(){return a(this.ref.current,"In order to access an instance of the decorated component, it must either be a class component or use React.forwardRef()"),this.ref.current}render(){return n.createElement(f,{value:o},n.createElement(t,Object.assign({},this.props,{ref:(0,s.Y)(t)?this.ref:null})))}}return i.DecoratedComponent=e,i.displayName=`DragDropContext(${r})`,u(i,e)}}},531724:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RSocketClient",{enumerable:!0,get:function(){return n.default}}),Object.defineProperty(t,"RSocketServer",{enumerable:!0,get:function(){return o.default}}),Object.defineProperty(t,"RSocketError",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(t,"RSocketResumableTransport",{enumerable:!0,get:function(){return s.default}}),Object.defineProperty(t,"WellKnownMimeType",{enumerable:!0,get:function(){return a.default}}),Object.defineProperty(t,"UNPARSEABLE_MIME_TYPE",{enumerable:!0,get:function(){return a.UNPARSEABLE_MIME_TYPE}}),Object.defineProperty(t,"UNKNOWN_RESERVED_MIME_TYPE",{enumerable:!0,get:function(){return a.UNKNOWN_RESERVED_MIME_TYPE}}),Object.defineProperty(t,"APPLICATION_AVRO",{enumerable:!0,get:function(){return a.APPLICATION_AVRO}}),Object.defineProperty(t,"APPLICATION_CBOR",{enumerable:!0,get:function(){return a.APPLICATION_CBOR}}),Object.defineProperty(t,"APPLICATION_GRAPHQL",{enumerable:!0,get:function(){return a.APPLICATION_GRAPHQL}}),Object.defineProperty(t,"APPLICATION_GZIP",{enumerable:!0,get:function(){return a.APPLICATION_GZIP}}),Object.defineProperty(t,"APPLICATION_JAVASCRIPT",{enumerable:!0,get:function(){return a.APPLICATION_JAVASCRIPT}}),Object.defineProperty(t,"APPLICATION_JSON",{enumerable:!0,get:function(){return a.APPLICATION_JSON}}),Object.defineProperty(t,"APPLICATION_OCTET_STREAM",{enumerable:!0,get:function(){return a.APPLICATION_OCTET_STREAM}}),Object.defineProperty(t,"APPLICATION_PDF",{enumerable:!0,get:function(){return a.APPLICATION_PDF}}),Object.defineProperty(t,"APPLICATION_THRIFT",{enumerable:!0,get:function(){return a.APPLICATION_THRIFT}}),Object.defineProperty(t,"APPLICATION_PROTOBUF",{enumerable:!0,get:function(){return a.APPLICATION_PROTOBUF}}),Object.defineProperty(t,"APPLICATION_XML",{enumerable:!0,get:function(){return a.APPLICATION_XML}}),Object.defineProperty(t,"APPLICATION_ZIP",{enumerable:!0,get:function(){return a.APPLICATION_ZIP}}),Object.defineProperty(t,"AUDIO_AAC",{enumerable:!0,get:function(){return a.AUDIO_AAC}}),Object.defineProperty(t,"AUDIO_MP3",{enumerable:!0,get:function(){return a.AUDIO_MP3}}),Object.defineProperty(t,"AUDIO_MP4",{enumerable:!0,get:function(){return a.AUDIO_MP4}}),Object.defineProperty(t,"AUDIO_MPEG3",{enumerable:!0,get:function(){return a.AUDIO_MPEG3}}),Object.defineProperty(t,"AUDIO_MPEG",{enumerable:!0,get:function(){return a.AUDIO_MPEG}}),Object.defineProperty(t,"AUDIO_OGG",{enumerable:!0,get:function(){return a.AUDIO_OGG}}),Object.defineProperty(t,"AUDIO_OPUS",{enumerable:!0,get:function(){return a.AUDIO_OPUS}}),Object.defineProperty(t,"AUDIO_VORBIS",{enumerable:!0,get:function(){return a.AUDIO_VORBIS}}),Object.defineProperty(t,"IMAGE_BMP",{enumerable:!0,get:function(){return a.IMAGE_BMP}}),Object.defineProperty(t,"IMAGE_GIG",{enumerable:!0,get:function(){return a.IMAGE_GIG}}),Object.defineProperty(t,"IMAGE_HEIC_SEQUENCE",{enumerable:!0,get:function(){return a.IMAGE_HEIC_SEQUENCE}}),Object.defineProperty(t,"IMAGE_HEIC",{enumerable:!0,get:function(){return a.IMAGE_HEIC}}),Object.defineProperty(t,"IMAGE_HEIF_SEQUENCE",{enumerable:!0,get:function(){return a.IMAGE_HEIF_SEQUENCE}}),Object.defineProperty(t,"IMAGE_HEIF",{enumerable:!0,get:function(){return a.IMAGE_HEIF}}),Object.defineProperty(t,"IMAGE_JPEG",{enumerable:!0,get:function(){return a.IMAGE_JPEG}}),Object.defineProperty(t,"IMAGE_PNG",{enumerable:!0,get:function(){return a.IMAGE_PNG}}),Object.defineProperty(t,"IMAGE_TIFF",{enumerable:!0,get:function(){return a.IMAGE_TIFF}}),Object.defineProperty(t,"MULTIPART_MIXED",{enumerable:!0,get:function(){return a.MULTIPART_MIXED}}),Object.defineProperty(t,"TEXT_CSS",{enumerable:!0,get:function(){return a.TEXT_CSS}}),Object.defineProperty(t,"TEXT_CSV",{enumerable:!0,get:function(){return a.TEXT_CSV}}),Object.defineProperty(t,"TEXT_HTML",{enumerable:!0,get:function(){return a.TEXT_HTML}}),Object.defineProperty(t,"TEXT_PLAIN",{enumerable:!0,get:function(){return a.TEXT_PLAIN}}),Object.defineProperty(t,"TEXT_XML",{enumerable:!0,get:function(){return a.TEXT_XML}}),Object.defineProperty(t,"VIDEO_H264",{enumerable:!0,get:function(){return a.VIDEO_H264}}),Object.defineProperty(t,"VIDEO_H265",{enumerable:!0,get:function(){return a.VIDEO_H265}}),Object.defineProperty(t,"VIDEO_VP8",{enumerable:!0,get:function(){return a.VIDEO_VP8}}),Object.defineProperty(t,"APPLICATION_HESSIAN",{enumerable:!0,get:function(){return a.APPLICATION_HESSIAN}}),Object.defineProperty(t,"APPLICATION_JAVA_OBJECT",{enumerable:!0,get:function(){return a.APPLICATION_JAVA_OBJECT}}),Object.defineProperty(t,"APPLICATION_CLOUDEVENTS_JSON",{enumerable:!0,get:function(){return a.APPLICATION_CLOUDEVENTS_JSON}}),Object.defineProperty(t,"MESSAGE_RSOCKET_MIMETYPE",{enumerable:!0,get:function(){return a.MESSAGE_RSOCKET_MIMETYPE}}),Object.defineProperty(t,"MESSAGE_RSOCKET_ACCEPT_MIMETYPES",{enumerable:!0,get:function(){return a.MESSAGE_RSOCKET_ACCEPT_MIMETYPES}}),Object.defineProperty(t,"MESSAGE_RSOCKET_AUTHENTICATION",{enumerable:!0,get:function(){return a.MESSAGE_RSOCKET_AUTHENTICATION}}),Object.defineProperty(t,"MESSAGE_RSOCKET_TRACING_ZIPKIN",{enumerable:!0,get:function(){return a.MESSAGE_RSOCKET_TRACING_ZIPKIN}}),Object.defineProperty(t,"MESSAGE_RSOCKET_ROUTING",{enumerable:!0,get:function(){return a.MESSAGE_RSOCKET_ROUTING}}),Object.defineProperty(t,"MESSAGE_RSOCKET_COMPOSITE_METADATA",{enumerable:!0,get:function(){return a.MESSAGE_RSOCKET_COMPOSITE_METADATA}}),Object.defineProperty(t,"WellKnownAuthType",{enumerable:!0,get:function(){return u.default}}),Object.defineProperty(t,"UNPARSEABLE_AUTH_TYPE",{enumerable:!0,get:function(){return u.UNPARSEABLE_AUTH_TYPE}}),Object.defineProperty(t,"UNKNOWN_RESERVED_AUTH_TYPE",{enumerable:!0,get:function(){return u.UNKNOWN_RESERVED_AUTH_TYPE}}),Object.defineProperty(t,"SIMPLE",{enumerable:!0,get:function(){return u.SIMPLE}}),Object.defineProperty(t,"BEARER",{enumerable:!0,get:function(){return u.BEARER}}),Object.defineProperty(t,"CONNECTION_STREAM_ID",{enumerable:!0,get:function(){return c.CONNECTION_STREAM_ID}}),Object.defineProperty(t,"ERROR_CODES",{enumerable:!0,get:function(){return c.ERROR_CODES}}),Object.defineProperty(t,"ERROR_EXPLANATIONS",{enumerable:!0,get:function(){return c.ERROR_EXPLANATIONS}}),Object.defineProperty(t,"FLAGS_MASK",{enumerable:!0,get:function(){return c.FLAGS_MASK}}),Object.defineProperty(t,"FLAGS",{enumerable:!0,get:function(){return c.FLAGS}}),Object.defineProperty(t,"FRAME_TYPE_OFFFSET",{enumerable:!0,get:function(){return c.FRAME_TYPE_OFFFSET}}),Object.defineProperty(t,"FRAME_TYPES",{enumerable:!0,get:function(){return c.FRAME_TYPES}}),Object.defineProperty(t,"MAX_CODE",{enumerable:!0,get:function(){return c.MAX_CODE}}),Object.defineProperty(t,"MAX_KEEPALIVE",{enumerable:!0,get:function(){return c.MAX_KEEPALIVE}}),Object.defineProperty(t,"MAX_LIFETIME",{enumerable:!0,get:function(){return c.MAX_LIFETIME}}),Object.defineProperty(t,"MAX_MIME_LENGTH",{enumerable:!0,get:function(){return c.MAX_MIME_LENGTH}}),Object.defineProperty(t,"MAX_RESUME_LENGTH",{enumerable:!0,get:function(){return c.MAX_RESUME_LENGTH}}),Object.defineProperty(t,"MAX_STREAM_ID",{enumerable:!0,get:function(){return c.MAX_STREAM_ID}}),Object.defineProperty(t,"MAX_VERSION",{enumerable:!0,get:function(){return c.MAX_VERSION}}),Object.defineProperty(t,"createErrorFromFrame",{enumerable:!0,get:function(){return c.createErrorFromFrame}}),Object.defineProperty(t,"getErrorCodeExplanation",{enumerable:!0,get:function(){return c.getErrorCodeExplanation}}),Object.defineProperty(t,"isComplete",{enumerable:!0,get:function(){return c.isComplete}}),Object.defineProperty(t,"isIgnore",{enumerable:!0,get:function(){return c.isIgnore}}),Object.defineProperty(t,"isLease",{enumerable:!0,get:function(){return c.isLease}}),Object.defineProperty(t,"isMetadata",{enumerable:!0,get:function(){return c.isMetadata}}),Object.defineProperty(t,"isNext",{enumerable:!0,get:function(){return c.isNext}}),Object.defineProperty(t,"isRespond",{enumerable:!0,get:function(){return c.isRespond}}),Object.defineProperty(t,"isResumeEnable",{enumerable:!0,get:function(){return c.isResumeEnable}}),Object.defineProperty(t,"printFrame",{enumerable:!0,get:function(){return c.printFrame}}),Object.defineProperty(t,"deserializeFrame",{enumerable:!0,get:function(){return l.deserializeFrame}}),Object.defineProperty(t,"deserializeFrameWithLength",{enumerable:!0,get:function(){return l.deserializeFrameWithLength}}),Object.defineProperty(t,"deserializeFrames",{enumerable:!0,get:function(){return l.deserializeFrames}}),Object.defineProperty(t,"serializeFrame",{enumerable:!0,get:function(){return l.serializeFrame}}),Object.defineProperty(t,"serializeFrameWithLength",{enumerable:!0,get:function(){return l.serializeFrameWithLength}}),Object.defineProperty(t,"byteLength",{enumerable:!0,get:function(){return f.byteLength}}),Object.defineProperty(t,"createBuffer",{enumerable:!0,get:function(){return f.createBuffer}}),Object.defineProperty(t,"readUInt24BE",{enumerable:!0,get:function(){return f.readUInt24BE}}),Object.defineProperty(t,"toBuffer",{enumerable:!0,get:function(){return f.toBuffer}}),Object.defineProperty(t,"writeUInt24BE",{enumerable:!0,get:function(){return f.writeUInt24BE}}),Object.defineProperty(t,"BufferEncoders",{enumerable:!0,get:function(){return h.BufferEncoders}}),Object.defineProperty(t,"BufferEncoder",{enumerable:!0,get:function(){return h.BufferEncoder}}),Object.defineProperty(t,"Utf8Encoders",{enumerable:!0,get:function(){return h.Utf8Encoders}}),Object.defineProperty(t,"UTF8Encoder",{enumerable:!0,get:function(){return h.UTF8Encoder}}),Object.defineProperty(t,"IdentitySerializer",{enumerable:!0,get:function(){return d.IdentitySerializer}}),Object.defineProperty(t,"IdentitySerializers",{enumerable:!0,get:function(){return d.IdentitySerializers}}),Object.defineProperty(t,"JsonSerializer",{enumerable:!0,get:function(){return d.JsonSerializer}}),Object.defineProperty(t,"JsonSerializers",{enumerable:!0,get:function(){return d.JsonSerializers}}),Object.defineProperty(t,"Leases",{enumerable:!0,get:function(){return p.Leases}}),Object.defineProperty(t,"Lease",{enumerable:!0,get:function(){return p.Lease}}),Object.defineProperty(t,"CompositeMetadata",{enumerable:!0,get:function(){return y.CompositeMetadata}}),Object.defineProperty(t,"ReservedMimeTypeEntry",{enumerable:!0,get:function(){return y.ReservedMimeTypeEntry}}),Object.defineProperty(t,"WellKnownMimeTypeEntry",{enumerable:!0,get:function(){return y.WellKnownMimeTypeEntry}}),Object.defineProperty(t,"ExplicitMimeTimeEntry",{enumerable:!0,get:function(){return y.ExplicitMimeTimeEntry}}),Object.defineProperty(t,"encodeAndAddCustomMetadata",{enumerable:!0,get:function(){return y.encodeAndAddCustomMetadata}}),Object.defineProperty(t,"encodeAndAddWellKnownMetadata",{enumerable:!0,get:function(){return y.encodeAndAddWellKnownMetadata}}),Object.defineProperty(t,"encodeCompositeMetadata",{enumerable:!0,get:function(){return y.encodeCompositeMetadata}}),Object.defineProperty(t,"decodeCompositeMetadata",{enumerable:!0,get:function(){return y.decodeCompositeMetadata}}),Object.defineProperty(t,"RoutingMetadata",{enumerable:!0,get:function(){return b.RoutingMetadata}}),Object.defineProperty(t,"encodeRoute",{enumerable:!0,get:function(){return b.encodeRoute}}),Object.defineProperty(t,"encodeRoutes",{enumerable:!0,get:function(){return b.encodeRoutes}}),Object.defineProperty(t,"decodeRoutes",{enumerable:!0,get:function(){return b.decodeRoutes}}),Object.defineProperty(t,"encodeSimpleAuthMetadata",{enumerable:!0,get:function(){return g.encodeSimpleAuthMetadata}}),Object.defineProperty(t,"encodeBearerAuthMetadata",{enumerable:!0,get:function(){return g.encodeBearerAuthMetadata}}),Object.defineProperty(t,"encodeWellKnownAuthMetadata",{enumerable:!0,get:function(){return g.encodeWellKnownAuthMetadata}}),Object.defineProperty(t,"encodeCustomAuthMetadata",{enumerable:!0,get:function(){return g.encodeCustomAuthMetadata}}),Object.defineProperty(t,"decodeSimpleAuthPayload",{enumerable:!0,get:function(){return g.decodeSimpleAuthPayload}}),Object.defineProperty(t,"decodeAuthMetadata",{enumerable:!0,get:function(){return g.decodeAuthMetadata}});var n=E(r(375388)),o=E(r(431880)),i=E(r(556697)),s=E(r(790874)),a=m(r(798603)),u=m(r(339261)),c=r(550558),l=r(572954),f=r(678596),h=r(752174),d=r(374899),p=r(82537),y=r(279590),b=r(820671),g=r(897953);function v(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return v=function(){return e},e}function m(e){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=v();if(t&&t.has(e))return t.get(e);var r={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var i=n?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(r,o,i):r[o]=e[o]}return r.default=e,t&&t.set(e,r),r}function E(e){return e&&e.__esModule?e:{default:e}}},535921:(e,t,r)=>{"use strict";r.d(t,{T:()=>i});var n=r(998083),o=r(335679);function i(e,t){return function(r){if("function"!=typeof e)throw new TypeError("argument is not a function. Are you looking for `mapTo()`?");return r.lift(new s(e,t))}}var s=function(){function e(e,t){this.project=e,this.thisArg=t}return e.prototype.call=function(e,t){return t.subscribe(new a(e,this.project,this.thisArg))},e}(),a=function(e){function t(t,r,n){var o=e.call(this,t)||this;return o.project=r,o.count=0,o.thisArg=n||o,o}return n.C6(t,e),t.prototype._next=function(e){var t;try{t=this.project.call(this.thisArg,e,this.count++)}catch(r){return void this.destination.error(r)}this.destination.next(t)},t}(o.v)},537727:(e,t,r)=>{"use strict";r.d(t,{I:()=>i});var n=r(558279),o=r(599884),i={closed:!0,next:function(e){},error:function(e){if(n.$.useDeprecatedSynchronousErrorHandling)throw e;(0,o.T)(e)},complete:function(){}}},546255:(e,t,r)=>{"use strict";function n(e,t,r){const n=r.getRegistry(),o=n.addTarget(e,t);return[o,()=>n.removeTarget(o)]}r.d(t,{A:()=>n})},550558:(e,t)=>{"use strict";function r(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function n(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}Object.defineProperty(t,"__esModule",{value:!0}),t.isIgnore=function(e){return(e&s.IGNORE)===s.IGNORE},t.isMetadata=function(e){return(e&s.METADATA)===s.METADATA},t.isComplete=function(e){return(e&s.COMPLETE)===s.COMPLETE},t.isNext=function(e){return(e&s.NEXT)===s.NEXT},t.isRespond=function(e){return(e&s.RESPOND)===s.RESPOND},t.isResumeEnable=function(e){return(e&s.RESUME_ENABLE)===s.RESUME_ENABLE},t.isLease=function(e){return(e&s.LEASE)===s.LEASE},t.isFollows=function(e){return(e&s.FOLLOWS)===s.FOLLOWS},t.isResumePositionFrameType=function(e){return e===o.CANCEL||e===o.ERROR||e===o.PAYLOAD||e===o.REQUEST_CHANNEL||e===o.REQUEST_FNF||e===o.REQUEST_RESPONSE||e===o.REQUEST_STREAM||e===o.REQUEST_N},t.getFrameTypeName=c,t.createErrorFromFrame=function(e){const{code:t,message:r}=e,n=l(t),o=new Error(function(e,...t){let r=0;return e.replace(/%s/g,(e=>t[r++]))}("RSocket error %s (%s): %s. See error `source` property for details.",f(t),n,r));return o.source={code:t,explanation:n,message:r},o},t.getErrorCodeExplanation=l,t.printFrame=function(e){const t=function(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?r(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):r(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}({},e);t.type=c(e.type)+` (${f(e.type)})`;const i=[];for(const r in s){const t=s[r];(e.flags&t)===t&&i.push(r)}i.length||i.push("NO FLAGS");t.flags=i.join(" | ")+` (${f(e.flags)})`,e.type===o.ERROR&&(t.code=l(e.code)+` (${f(e.code)})`);return JSON.stringify(t,null,2)},t.MAX_VERSION=t.MAX_TTL=t.MAX_STREAM_ID=t.MAX_RESUME_LENGTH=t.MAX_REQUEST_N=t.MAX_REQUEST_COUNT=t.MAX_MIME_LENGTH=t.MAX_METADATA_LENGTH=t.MAX_LIFETIME=t.MAX_KEEPALIVE=t.MAX_CODE=t.FRAME_TYPE_OFFFSET=t.FLAGS_MASK=t.ERROR_EXPLANATIONS=t.ERROR_CODES=t.FLAGS=t.FRAME_TYPE_NAMES=t.FRAME_TYPES=t.CONNECTION_STREAM_ID=void 0;t.CONNECTION_STREAM_ID=0;const o={CANCEL:9,ERROR:11,EXT:63,KEEPALIVE:3,LEASE:2,METADATA_PUSH:12,PAYLOAD:10,REQUEST_CHANNEL:7,REQUEST_FNF:5,REQUEST_N:8,REQUEST_RESPONSE:4,REQUEST_STREAM:6,RESERVED:0,RESUME:13,RESUME_OK:14,SETUP:1};t.FRAME_TYPES=o;const i={};t.FRAME_TYPE_NAMES=i;for(const h in o){const e=o[h];i[e]=h}const s={COMPLETE:64,FOLLOWS:128,IGNORE:512,LEASE:64,METADATA:256,NEXT:32,RESPOND:128,RESUME_ENABLE:128};t.FLAGS=s;const a={APPLICATION_ERROR:513,CANCELED:515,CONNECTION_CLOSE:258,CONNECTION_ERROR:257,INVALID:516,INVALID_SETUP:1,REJECTED:514,REJECTED_RESUME:4,REJECTED_SETUP:3,RESERVED:0,RESERVED_EXTENSION:4294967295,UNSUPPORTED_SETUP:2};t.ERROR_CODES=a;const u={};t.ERROR_EXPLANATIONS=u;for(const h in a){u[a[h]]=h}t.FLAGS_MASK=1023;t.FRAME_TYPE_OFFFSET=10;t.MAX_CODE=2147483647;t.MAX_KEEPALIVE=2147483647;t.MAX_LIFETIME=2147483647;t.MAX_METADATA_LENGTH=16777215;t.MAX_MIME_LENGTH=255;t.MAX_REQUEST_COUNT=2147483647;t.MAX_REQUEST_N=2147483647;t.MAX_RESUME_LENGTH=65535;t.MAX_STREAM_ID=2147483647;t.MAX_TTL=2147483647;function c(e){const t=i[e];return null!=t?t:f(e)}function l(e){const t=u[e];return null!=t?t:e<=768?"RESERVED (PROTOCOL)":"RESERVED (APPLICATION)"}function f(e){return"0x"+e.toString(16)}t.MAX_VERSION=65535},550742:(e,t,r)=>{"use strict";r.d(t,{c:()=>s});var n=r(226260),o=r(750150),i=r(363295);function s(e,t){return t?(0,i.V)(e,t):new n.c((0,o.v)(e))}},554634:(e,t,r)=>{"use strict";r.d(t,{U:()=>o});var n=r(998083),o=function(e){function t(t,r){var n=e.call(this,t,r)||this;return n.scheduler=t,n.work=r,n}return n.C6(t,e),t.prototype.schedule=function(t,r){return void 0===r&&(r=0),r>0?e.prototype.schedule.call(this,t,r):(this.delay=r,this.state=t,this.scheduler.flush(this),this)},t.prototype.execute=function(t,r){return r>0||this.closed?e.prototype.execute.call(this,t,r):this._execute(t,r)},t.prototype.requestAsyncId=function(t,r,n){return void 0===n&&(n=0),null!==n&&n>0||null===n&&this.delay>0?e.prototype.requestAsyncId.call(this,t,r,n):t.flush(this)},t}(r(237157).R)},556697:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;class r extends Error{constructor(e,t){super(t),this.errorCode=e}}t.default=r},558279:(e,t,r)=>{"use strict";r.d(t,{$:()=>o});var n=!1,o={Promise:void 0,set useDeprecatedSynchronousErrorHandling(e){e&&(new Error).stack;n=e},get useDeprecatedSynchronousErrorHandling(){return n}}},567427:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(914422),o=r(531724),i=r(254136);function s(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}t.default=class{constructor(e,t){s(this,"_handleClosed",(e=>{this._close(new Error(e.reason||"RSocketWebSocketClient: Socket closed unexpectedly."))})),s(this,"_handleError",(e=>{this._close(e.error)})),s(this,"_handleOpened",(()=>{this._setConnectionStatus(i.CONNECTION_STATUS.CONNECTED)})),s(this,"_handleMessage",(e=>{try{const t=this._readFrame(e);this._receivers.forEach((e=>e.onNext(t)))}catch(t){this._close(t)}})),this._encoders=t,this._options=e,this._receivers=new Set,this._senders=new Set,this._socket=null,this._status=i.CONNECTION_STATUS.NOT_CONNECTED,this._statusSubscribers=new Set}close(){this._close()}connect(){if("NOT_CONNECTED"!==this._status.kind)throw new Error("RSocketWebSocketClient: Cannot connect(), a connection is already established.");this._setConnectionStatus(i.CONNECTION_STATUS.CONNECTING);const e=this._options.wsCreator,t=this._options.url;this._socket=e?e(t):new WebSocket(t);const r=this._socket;r.binaryType="arraybuffer",r.addEventListener("close",this._handleClosed),r.addEventListener("error",this._handleError),r.addEventListener("open",this._handleOpened),r.addEventListener("message",this._handleMessage)}connectionStatus(){return new n.Flowable((e=>{e.onSubscribe({cancel:()=>{this._statusSubscribers.delete(e)},request:()=>{this._statusSubscribers.add(e),e.onNext(this._status)}})}))}receive(){return new n.Flowable((e=>{e.onSubscribe({cancel:()=>{this._receivers.delete(e)},request:()=>{this._receivers.add(e)}})}))}sendOne(e){this._writeFrame(e)}send(e){let t;e.subscribe({onComplete:()=>{t&&this._senders.delete(t)},onError:e=>{t&&this._senders.delete(t),this._close(e)},onNext:e=>this._writeFrame(e),onSubscribe:e=>{t=e,this._senders.add(t),t.request(Number.MAX_SAFE_INTEGER)}})}_close(e){if("CLOSED"===this._status.kind||"ERROR"===this._status.kind)return;const t=e?{error:e,kind:"ERROR"}:i.CONNECTION_STATUS.CLOSED;this._setConnectionStatus(t),this._receivers.forEach((t=>{e?t.onError(e):t.onComplete()})),this._receivers.clear(),this._senders.forEach((e=>e.cancel())),this._senders.clear();const r=this._socket;r&&(r.removeEventListener("close",this._handleClosed),r.removeEventListener("error",this._handleError),r.removeEventListener("open",this._handleOpened),r.removeEventListener("message",this._handleMessage),r.close(),this._socket=null)}_setConnectionStatus(e){this._status=e,this._statusSubscribers.forEach((t=>t.onNext(e)))}_readFrame(e){const t=(0,o.toBuffer)(e.data);return this._options.lengthPrefixedFrames?(0,o.deserializeFrameWithLength)(t,this._encoders):(0,o.deserializeFrame)(t,this._encoders)}_writeFrame(e){try{0;const t=this._options.lengthPrefixedFrames?(0,o.serializeFrameWithLength)(e,this._encoders):(0,o.serializeFrame)(e,this._encoders);if(!this._socket)throw new Error("RSocketWebSocketClient: Cannot send frame, not connected.");this._socket.send(t)}catch(t){this._close(t)}}}},572954:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.deserializeFrameWithLength=function(e,t){const r=(0,a.readUInt24BE)(e,0);return l(e.slice(c,c+r),t)},t.deserializeFrames=function(e,t){const r=[];let n=0;for(;n+c<e.length;){const o=(0,a.readUInt24BE)(e,n),i=n+c,s=i+o;if(s>e.length)break;const u=l(e.slice(i,s),t);r.push(u),n=s}return[r,e.slice(n,e.length)]},t.serializeFrameWithLength=function(e,t){const r=f(e,t),n=(0,a.createBuffer)(r.length+c);return(0,a.writeUInt24BE)(n,r.length,0),r.copy(n,c,0,r.length),n},t.deserializeFrame=l,t.serializeFrame=f,t.sizeOfFrame=function(e,t){switch(t=t||s.Utf8Encoders,e.type){case i.FRAME_TYPES.SETUP:return function(e,t){const r=null!=e.resumeToken?t.resumeToken.byteLength(e.resumeToken):0,n=null!=e.metadataMimeType?t.metadataMimeType.byteLength(e.metadataMimeType):0,o=null!=e.dataMimeType?t.dataMimeType.byteLength(e.dataMimeType):0,i=O(e,t);return u+h+(r?d+r:0)+n+o+i}(e,t);case i.FRAME_TYPES.PAYLOAD:return function(e,t){const r=O(e,t);return u+r}(e,t);case i.FRAME_TYPES.ERROR:return function(e,t){const r=null!=e.message?t.message.byteLength(e.message):0;return u+p+r}(e,t);case i.FRAME_TYPES.KEEPALIVE:return function(e,t){const r=null!=e.data?t.data.byteLength(e.data):0;return u+y+r}(e,t);case i.FRAME_TYPES.REQUEST_FNF:case i.FRAME_TYPES.REQUEST_RESPONSE:return function(e,t){const r=O(e,t);return u+r}(e,t);case i.FRAME_TYPES.REQUEST_STREAM:case i.FRAME_TYPES.REQUEST_CHANNEL:return function(e,t){const r=O(e,t);return u+g+r}(e,t);case i.FRAME_TYPES.METADATA_PUSH:return function(e,t){return u+(null!=e.metadata?t.metadata.byteLength(e.metadata):0)}(e,t);case i.FRAME_TYPES.REQUEST_N:return u+v;case i.FRAME_TYPES.RESUME:return function(e,t){const r=t.resumeToken.byteLength(e.resumeToken);return u+m+r}(e,t);case i.FRAME_TYPES.RESUME_OK:return u+E;case i.FRAME_TYPES.CANCEL:return u;case i.FRAME_TYPES.LEASE:return function(e,t){const r=null!=e.metadata?t.metadata.byteLength(e.metadata):0;return u+b+r}(e,t);default:(0,o.default)(!1,"RSocketBinaryFraming: Unsupported frame type `%s`.",(0,i.getFrameTypeName)(e.type))}};var n,o=(n=r(308552))&&n.__esModule?n:{default:n},i=r(550558),s=r(752174),a=r(678596);const u=6,c=3;function l(e,t){t=t||s.Utf8Encoders;let r=0;const n=e.readInt32BE(r);r+=4,(0,o.default)(n>=0,"RSocketBinaryFraming: Invalid frame, expected a positive stream id, got `%s.",n);const c=e.readUInt16BE(r);r+=2;const l=c>>>i.FRAME_TYPE_OFFFSET,f=c&i.FLAGS_MASK;switch(l){case i.FRAME_TYPES.SETUP:return function(e,t,r,n){(0,o.default)(0===t,"RSocketBinaryFraming: Invalid SETUP frame, expected stream id to be 0.");const s=e.length;let a=u;const c=e.readUInt16BE(a);a+=2;const l=e.readUInt16BE(a);a+=2;const f=e.readInt32BE(a);a+=4,(0,o.default)(f>=0&&f<=i.MAX_KEEPALIVE,"RSocketBinaryFraming: Invalid SETUP frame, expected keepAlive to be >= 0 and <= %s. Got `%s`.",i.MAX_KEEPALIVE,f);const h=e.readInt32BE(a);a+=4,(0,o.default)(h>=0&&h<=i.MAX_LIFETIME,"RSocketBinaryFraming: Invalid SETUP frame, expected lifetime to be >= 0 and <= %s. Got `%s`.",i.MAX_LIFETIME,h);let d=null;if(r&i.FLAGS.RESUME_ENABLE){const t=e.readInt16BE(a);a+=2,(0,o.default)(t>=0&&t<=i.MAX_RESUME_LENGTH,"RSocketBinaryFraming: Invalid SETUP frame, expected resumeToken length to be >= 0 and <= %s. Got `%s`.",i.MAX_RESUME_LENGTH,t),d=n.resumeToken.decode(e,a,a+t),a+=t}const p=e.readUInt8(a);a+=1;const y=n.metadataMimeType.decode(e,a,a+p);a+=p;const b=e.readUInt8(a);a+=1;const g=n.dataMimeType.decode(e,a,a+b);a+=b;const v={data:null,dataMimeType:g,flags:r,keepAlive:f,length:s,lifetime:h,majorVersion:c,metadata:null,metadataMimeType:y,minorVersion:l,resumeToken:d,streamId:t,type:i.FRAME_TYPES.SETUP};return w(e,v,n,a),v}(e,n,f,t);case i.FRAME_TYPES.PAYLOAD:return function(e,t,r,n){(0,o.default)(t>0,"RSocketBinaryFraming: Invalid PAYLOAD frame, expected stream id to be > 0.");const s=e.length,a={data:null,flags:r,length:s,metadata:null,streamId:t,type:i.FRAME_TYPES.PAYLOAD};return w(e,a,n,u),a}(e,n,f,t);case i.FRAME_TYPES.ERROR:return function(e,t,r,n){const s=e.length;let a=u;const c=e.readInt32BE(a);a+=4,(0,o.default)(c>=0&&c<=i.MAX_CODE,"RSocketBinaryFraming: Invalid ERROR frame, expected code to be >= 0 and <= %s. Got `%s`.",i.MAX_CODE,c);const l=e.length-a;let f="";l>0&&(f=n.message.decode(e,a,a+l),a+=l);return{code:c,flags:r,length:s,message:f,streamId:t,type:i.FRAME_TYPES.ERROR}}(e,n,f,t);case i.FRAME_TYPES.KEEPALIVE:return function(e,t,r,n){(0,o.default)(0===t,"RSocketBinaryFraming: Invalid KEEPALIVE frame, expected stream id to be 0.");const s=e.length;let c=u;const l=(0,a.readUInt64BE)(e,c);c+=8;let f=null;c<e.length&&(f=n.data.decode(e,c,e.length));return{data:f,flags:r,lastReceivedPosition:l,length:s,streamId:t,type:i.FRAME_TYPES.KEEPALIVE}}(e,n,f,t);case i.FRAME_TYPES.REQUEST_FNF:return function(e,t,r,n){(0,o.default)(t>0,"RSocketBinaryFraming: Invalid REQUEST_FNF frame, expected stream id to be > 0.");const s=e.length,a={data:null,flags:r,length:s,metadata:null,streamId:t,type:i.FRAME_TYPES.REQUEST_FNF};return w(e,a,n,u),a}(e,n,f,t);case i.FRAME_TYPES.REQUEST_RESPONSE:return function(e,t,r,n){(0,o.default)(t>0,"RSocketBinaryFraming: Invalid REQUEST_RESPONSE frame, expected stream id to be > 0.");const s=e.length,a={data:null,flags:r,length:s,metadata:null,streamId:t,type:i.FRAME_TYPES.REQUEST_RESPONSE};return w(e,a,n,u),a}(e,n,f,t);case i.FRAME_TYPES.REQUEST_STREAM:return function(e,t,r,n){(0,o.default)(t>0,"RSocketBinaryFraming: Invalid REQUEST_STREAM frame, expected stream id to be > 0.");const s=e.length;let a=u;const c=e.readInt32BE(a);a+=4,(0,o.default)(c>0,"RSocketBinaryFraming: Invalid REQUEST_STREAM frame, expected requestN to be > 0, got `%s`.",c);const l={data:null,flags:r,length:s,metadata:null,requestN:c,streamId:t,type:i.FRAME_TYPES.REQUEST_STREAM};return w(e,l,n,a),l}(e,n,f,t);case i.FRAME_TYPES.REQUEST_CHANNEL:return function(e,t,r,n){(0,o.default)(t>0,"RSocketBinaryFraming: Invalid REQUEST_CHANNEL frame, expected stream id to be > 0.");const s=e.length;let a=u;const c=e.readInt32BE(a);a+=4,(0,o.default)(c>0,"RSocketBinaryFraming: Invalid REQUEST_STREAM frame, expected requestN to be > 0, got `%s`.",c);const l={data:null,flags:r,length:s,metadata:null,requestN:c,streamId:t,type:i.FRAME_TYPES.REQUEST_CHANNEL};return w(e,l,n,a),l}(e,n,f,t);case i.FRAME_TYPES.METADATA_PUSH:return function(e,t,r,n){(0,o.default)(0===t,"RSocketBinaryFraming: Invalid METADATA_PUSH frame, expected stream id to be 0.");const s=e.length;return{flags:r,length:s,metadata:s===u?null:n.metadata.decode(e,u,s),streamId:t,type:i.FRAME_TYPES.METADATA_PUSH}}(e,n,f,t);case i.FRAME_TYPES.REQUEST_N:return function(e,t,r,n){(0,o.default)(t>0,"RSocketBinaryFraming: Invalid REQUEST_N frame, expected stream id to be > 0.");const s=e.length,a=e.readInt32BE(u);return(0,o.default)(a>0,"RSocketBinaryFraming: Invalid REQUEST_STREAM frame, expected requestN to be > 0, got `%s`.",a),{flags:r,length:s,requestN:a,streamId:t,type:i.FRAME_TYPES.REQUEST_N}}(e,n,f);case i.FRAME_TYPES.RESUME:return function(e,t,r,n){(0,o.default)(0===t,"RSocketBinaryFraming: Invalid RESUME frame, expected stream id to be 0.");const s=e.length;let c=u;const l=e.readUInt16BE(c);c+=2;const f=e.readUInt16BE(c);c+=2;const h=e.readInt16BE(c);c+=2,(0,o.default)(h>=0&&h<=i.MAX_RESUME_LENGTH,"RSocketBinaryFraming: Invalid SETUP frame, expected resumeToken length to be >= 0 and <= %s. Got `%s`.",i.MAX_RESUME_LENGTH,h);const d=n.resumeToken.decode(e,c,c+h);c+=h;const p=(0,a.readUInt64BE)(e,c);c+=8;const y=(0,a.readUInt64BE)(e,c);return c+=8,{clientPosition:y,flags:r,length:s,majorVersion:l,minorVersion:f,resumeToken:d,serverPosition:p,streamId:t,type:i.FRAME_TYPES.RESUME}}(e,n,f,t);case i.FRAME_TYPES.RESUME_OK:return function(e,t,r,n){(0,o.default)(0===t,"RSocketBinaryFraming: Invalid RESUME frame, expected stream id to be 0.");const s=e.length,c=(0,a.readUInt64BE)(e,u);return{clientPosition:c,flags:r,length:s,streamId:t,type:i.FRAME_TYPES.RESUME_OK}}(e,n,f);case i.FRAME_TYPES.CANCEL:return function(e,t,r,n){(0,o.default)(t>0,"RSocketBinaryFraming: Invalid CANCEL frame, expected stream id to be > 0.");const s=e.length;return{flags:r,length:s,streamId:t,type:i.FRAME_TYPES.CANCEL}}(e,n,f);case i.FRAME_TYPES.LEASE:return function(e,t,r,n){(0,o.default)(0===t,"RSocketBinaryFraming: Invalid LEASE frame, expected stream id to be 0.");const s=e.length;let a=u;const c=e.readUInt32BE(a);a+=4;const l=e.readUInt32BE(a);a+=4;let f=null;a<e.length&&(f=n.metadata.decode(e,a,e.length));return{flags:r,length:s,metadata:f,requestCount:l,streamId:t,ttl:c,type:i.FRAME_TYPES.LEASE}}(e,n,f,t);default:(0,o.default)(!1,"RSocketBinaryFraming: Unsupported frame type `%s`.",(0,i.getFrameTypeName)(l))}}function f(e,t){switch(t=t||s.Utf8Encoders,e.type){case i.FRAME_TYPES.SETUP:return function(e,t){const r=null!=e.resumeToken?t.resumeToken.byteLength(e.resumeToken):0,n=null!=e.metadataMimeType?t.metadataMimeType.byteLength(e.metadataMimeType):0,o=null!=e.dataMimeType?t.dataMimeType.byteLength(e.dataMimeType):0,s=O(e,t),c=(0,a.createBuffer)(u+h+(r?d+r:0)+n+o+s);let l=_(e,c);l=c.writeUInt16BE(e.majorVersion,l),l=c.writeUInt16BE(e.minorVersion,l),l=c.writeUInt32BE(e.keepAlive,l),l=c.writeUInt32BE(e.lifetime,l),e.flags&i.FLAGS.RESUME_ENABLE&&(l=c.writeUInt16BE(r,l),null!=e.resumeToken&&(l=t.resumeToken.encode(e.resumeToken,c,l,l+r)));l=c.writeUInt8(n,l),null!=e.metadataMimeType&&(l=t.metadataMimeType.encode(e.metadataMimeType,c,l,l+n));l=c.writeUInt8(o,l),null!=e.dataMimeType&&(l=t.dataMimeType.encode(e.dataMimeType,c,l,l+o));return S(e,c,t,l),c}(e,t);case i.FRAME_TYPES.PAYLOAD:return function(e,t){const r=O(e,t),n=(0,a.createBuffer)(u+r),o=_(e,n);return S(e,n,t,o),n}(e,t);case i.FRAME_TYPES.ERROR:return function(e,t){const r=null!=e.message?t.message.byteLength(e.message):0,n=(0,a.createBuffer)(u+p+r);let o=_(e,n);o=n.writeUInt32BE(e.code,o),null!=e.message&&t.message.encode(e.message,n,o,o+r);return n}(e,t);case i.FRAME_TYPES.KEEPALIVE:return function(e,t){const r=null!=e.data?t.data.byteLength(e.data):0,n=(0,a.createBuffer)(u+y+r);let o=_(e,n);o=(0,a.writeUInt64BE)(n,e.lastReceivedPosition,o),null!=e.data&&t.data.encode(e.data,n,o,o+r);return n}(e,t);case i.FRAME_TYPES.REQUEST_FNF:case i.FRAME_TYPES.REQUEST_RESPONSE:return function(e,t){const r=O(e,t),n=(0,a.createBuffer)(u+r),o=_(e,n);return S(e,n,t,o),n}(e,t);case i.FRAME_TYPES.REQUEST_STREAM:case i.FRAME_TYPES.REQUEST_CHANNEL:return function(e,t){const r=O(e,t),n=(0,a.createBuffer)(u+g+r);let o=_(e,n);return o=n.writeUInt32BE(e.requestN,o),S(e,n,t,o),n}(e,t);case i.FRAME_TYPES.METADATA_PUSH:return function(e,t){const r=e.metadata;if(null!=r){const n=(0,a.createBuffer)(u+t.metadata.byteLength(r)),o=_(e,n);return t.metadata.encode(r,n,o,n.length),n}{const t=(0,a.createBuffer)(u);return _(e,t),t}}(e,t);case i.FRAME_TYPES.REQUEST_N:return function(e,t){const r=(0,a.createBuffer)(u+v),n=_(e,r);return r.writeUInt32BE(e.requestN,n),r}(e);case i.FRAME_TYPES.RESUME:return function(e,t){const r=t.resumeToken.byteLength(e.resumeToken),n=(0,a.createBuffer)(u+m+r);let o=_(e,n);return o=n.writeUInt16BE(e.majorVersion,o),o=n.writeUInt16BE(e.minorVersion,o),o=n.writeUInt16BE(r,o),o=t.resumeToken.encode(e.resumeToken,n,o,o+r),o=(0,a.writeUInt64BE)(n,e.serverPosition,o),(0,a.writeUInt64BE)(n,e.clientPosition,o),n}(e,t);case i.FRAME_TYPES.RESUME_OK:return function(e,t){const r=(0,a.createBuffer)(u+E),n=_(e,r);return(0,a.writeUInt64BE)(r,e.clientPosition,n),r}(e);case i.FRAME_TYPES.CANCEL:return function(e,t){const r=(0,a.createBuffer)(u);return _(e,r),r}(e);case i.FRAME_TYPES.LEASE:return function(e,t){const r=null!=e.metadata?t.metadata.byteLength(e.metadata):0,n=(0,a.createBuffer)(u+b+r);let o=_(e,n);o=n.writeUInt32BE(e.ttl,o),o=n.writeUInt32BE(e.requestCount,o),null!=e.metadata&&t.metadata.encode(e.metadata,n,o,o+r);return n}(e,t);default:(0,o.default)(!1,"RSocketBinaryFraming: Unsupported frame type `%s`.",(0,i.getFrameTypeName)(e.type))}}const h=14,d=2;const p=4;const y=8;const b=8;const g=4;const v=4;const m=22;const E=8;function _(e,t){const r=t.writeInt32BE(e.streamId,0);return t.writeUInt16BE(e.type<<i.FRAME_TYPE_OFFFSET|e.flags&i.FLAGS_MASK,r)}function O(e,t){let r=0;return null!=e.data&&(r+=t.data.byteLength(e.data)),(0,i.isMetadata)(e.flags)&&(r+=c,null!=e.metadata&&(r+=t.metadata.byteLength(e.metadata))),r}function S(e,t,r,n){if((0,i.isMetadata)(e.flags))if(null!=e.metadata){const o=r.metadata.byteLength(e.metadata);n=(0,a.writeUInt24BE)(t,o,n),n=r.metadata.encode(e.metadata,t,n,n+o)}else n=(0,a.writeUInt24BE)(t,0,n);null!=e.data&&r.data.encode(e.data,t,n,t.length)}function w(e,t,r,n){if((0,i.isMetadata)(t.flags)){const o=(0,a.readUInt24BE)(e,n);n+=c,o>0&&(t.metadata=r.metadata.decode(e,n,n+o),n+=o)}n<e.length&&(t.data=r.data.decode(e,n,e.length))}},576206:(e,t,r)=>{"use strict";r.d(t,{YZ:()=>s});var n=r(998083),o=r(335679),i=r(354414);var s=function(e){function t(t,r,n){void 0===n&&(n=0);var o=e.call(this,t)||this;return o.scheduler=r,o.delay=n,o}return n.C6(t,e),t.dispatch=function(e){var t=e.notification,r=e.destination;t.observe(r),this.unsubscribe()},t.prototype.scheduleMessage=function(e){this.destination.add(this.scheduler.schedule(t.dispatch,this.delay,new a(e,this.destination)))},t.prototype._next=function(e){this.scheduleMessage(i.E.createNext(e))},t.prototype._error=function(e){this.scheduleMessage(i.E.createError(e)),this.unsubscribe()},t.prototype._complete=function(){this.scheduleMessage(i.E.createComplete()),this.unsubscribe()},t}(o.v),a=function(){return function(e,t){this.notification=e,this.destination=t}}()},584600:(e,t,r)=>{"use strict";function n(e){return(t=e)&&t.prototype&&"function"==typeof t.prototype.render||function(e){return e&&e.$$typeof&&"Symbol(react.forward_ref)"===e.$$typeof.toString()}(e);var t}r.d(t,{Y:()=>n})},584657:(e,t,r)=>{"use strict";r.d(t,{H:()=>o});var n=r(895693);function o(e,t){return(0,n.ZZ)(e,t,1)}},599884:(e,t,r)=>{"use strict";function n(e){setTimeout((function(){throw e}),0)}r.d(t,{T:()=>n})},604057:(e,t,r)=>{"use strict";r.d(t,{i:()=>a});var n=r(139520),o=r(125e3),i=r(845212);const s=r(408406);function a(e){const t=(0,i.useRef)(e);t.current=e,s(null!=e.item,"item must be defined"),s(null!=e.item.type,"item type must be defined");const[r,a]=(0,o.P)();(0,o.k)(t,r,a);const u=(0,n.F)(r,t.current.collect||(()=>({})),(()=>a.reconnect())),c=(0,i.useMemo)((()=>a.hooks.dragSource()),[a]),l=(0,i.useMemo)((()=>a.hooks.dragPreview()),[a]);return(0,i.useEffect)((()=>{a.dragSourceOptions=t.current.options||null,a.reconnect()}),[a]),(0,i.useEffect)((()=>{a.dragPreviewOptions=t.current.previewOptions||null,a.reconnect()}),[a]),[u,c,l]}},623276:(e,t,r)=>{"use strict";r.d(t,{D:()=>n});var n=function(){return"function"==typeof Symbol?Symbol("rxSubscriber"):"@@rxSubscriber_"+Math.random()}()},630084:(e,t,r)=>{"use strict";r.d(t,{Y:()=>o});var n=r(998083),o=function(e){function t(t,r){var n=e.call(this)||this;return n.subject=t,n.subscriber=r,n.closed=!1,n}return n.C6(t,e),t.prototype.unsubscribe=function(){if(!this.closed){this.closed=!0;var e=this.subject,t=e.observers;if(this.subject=null,t&&0!==t.length&&!e.isStopped&&!e.closed){var r=t.indexOf(this.subscriber);-1!==r&&t.splice(r,1)}}},t}(r(997752).y)},631998:(e,t,r)=>{"use strict";var n=r(311773),o=[],i=[],s=n.makeRequestCallFromTimer((function(){if(i.length)throw i.shift()}));function a(e){var t;(t=o.length?o.pop():new u).task=e,n(t)}function u(){this.task=null}e.exports=a,u.prototype.call=function(){try{this.task.call()}catch(e){a.onerror?a.onerror(e):(i.push(e),s())}finally{this.task=null,o[o.length]=this}}},641186:(e,t,r)=>{"use strict";r.d(t,{H:()=>a});var n=r(139520),o=r(189859),i=r(845212);const s=r(408406);function a(e){const t=(0,i.useRef)(e);t.current=e,s(null!=e.accept,"accept must be defined");const[r,a]=(0,o.y)();(0,o.V)(t,r,a);const u=(0,n.F)(r,t.current.collect||(()=>({})),(()=>a.reconnect())),c=(0,i.useMemo)((()=>a.hooks.dropTarget()),[a]);return(0,i.useEffect)((()=>{a.dropTargetOptions=e.options||null,a.reconnect()}),[e.options]),[u,c]}},656772:(e,t,r)=>{"use strict";function n(e){let t=null;return()=>(null==t&&(t=e()),t)}function o(e,t){return e.filter((e=>e!==t))}function i(e,t){const r=new Set,n=e=>r.add(e);e.forEach(n),t.forEach(n);const o=[];return r.forEach((e=>o.push(e))),o}r.d(t,{Bj:()=>n,FF:()=>o,KC:()=>i})},657111:(e,t,r)=>{"use strict";r.d(t,{Dg:()=>s,b$:()=>a,yA:()=>u});var n=r(704605),o=r(814737);const i=1;function s(e){const t=e.nodeType===i?e:e.parentElement;if(!t)return null;const{top:r,left:n}=t.getBoundingClientRect();return{x:n,y:r}}function a(e){return{x:e.clientX,y:e.clientY}}function u(e,t,r,i,a){const u="IMG"===(c=t).nodeName&&((0,n.g)()||!document.documentElement.contains(c));var c;const l=s(u?e:t),f={x:r.x-l.x,y:r.y-l.y},{offsetWidth:h,offsetHeight:d}=e,{anchorX:p,anchorY:y}=i,{dragPreviewWidth:b,dragPreviewHeight:g}=function(e,t,r,o){let i=e?t.width:r,s=e?t.height:o;return(0,n.n)()&&e&&(s/=window.devicePixelRatio,i/=window.devicePixelRatio),{dragPreviewWidth:i,dragPreviewHeight:s}}(u,t,h,d),{offsetX:v,offsetY:m}=a,E=0===m||m;return{x:0===v||v?v:new o.A([0,.5,1],[f.x,f.x/h*b,f.x+b-h]).interpolate(p),y:E?m:(()=>{let e=new o.A([0,.5,1],[f.y,f.y/d*g,f.y+g-d]).interpolate(y);return(0,n.n)()&&u&&(e+=(window.devicePixelRatio-1)*g),e})()}}},660264:(e,t,r)=>{"use strict";r.d(t,{m:()=>l});var n=r(998083),o=r(450117),i=r(77808),s=r(997752),a=r(576206),u=r(878796),c=r(630084),l=function(e){function t(t,r,n){void 0===t&&(t=Number.POSITIVE_INFINITY),void 0===r&&(r=Number.POSITIVE_INFINITY);var o=e.call(this)||this;return o.scheduler=n,o._events=[],o._infiniteTimeWindow=!1,o._bufferSize=t<1?1:t,o._windowTime=r<1?1:r,r===Number.POSITIVE_INFINITY?(o._infiniteTimeWindow=!0,o.next=o.nextInfiniteTimeWindow):o.next=o.nextTimeWindow,o}return n.C6(t,e),t.prototype.nextInfiniteTimeWindow=function(t){if(!this.isStopped){var r=this._events;r.push(t),r.length>this._bufferSize&&r.shift()}e.prototype.next.call(this,t)},t.prototype.nextTimeWindow=function(t){this.isStopped||(this._events.push(new f(this._getNow(),t)),this._trimBufferThenGetEvents()),e.prototype.next.call(this,t)},t.prototype._subscribe=function(e){var t,r=this._infiniteTimeWindow,n=r?this._events:this._trimBufferThenGetEvents(),o=this.scheduler,i=n.length;if(this.closed)throw new u.P;if(this.isStopped||this.hasError?t=s.y.EMPTY:(this.observers.push(e),t=new c.Y(this,e)),o&&e.add(e=new a.YZ(e,o)),r)for(var l=0;l<i&&!e.closed;l++)e.next(n[l]);else for(l=0;l<i&&!e.closed;l++)e.next(n[l].value);return this.hasError?e.error(this.thrownError):this.isStopped&&e.complete(),t},t.prototype._getNow=function(){return(this.scheduler||i.g).now()},t.prototype._trimBufferThenGetEvents=function(){for(var e=this._getNow(),t=this._bufferSize,r=this._windowTime,n=this._events,o=n.length,i=0;i<o&&!(e-n[i].time<r);)i++;return o>t&&(i=Math.max(i,o-t)),i>0&&n.splice(0,i),n},t}(o.B7),f=function(){return function(e,t){this.time=e,this.value=t}}()},662661:(e,t,r)=>{"use strict";r.d(t,{DS:()=>o.D,jG:()=>n.j,ke:()=>i.k});var n=r(84287),o=r(949339),i=r(912360)},664191:(e,t,r)=>{"use strict";r.d(t,{i:()=>h});var n=r(750150),o=r(37186),i=r(771909),s=r(190418),a=r(1408),u=r(72231),c=r(47911),l=r(211794),f=r(977305),h=function(e){if(e&&"function"==typeof e[f.s])return(0,s.X)(e);if((0,a.X)(e))return(0,n.v)(e);if((0,u.y)(e))return(0,o.F)(e);if(e&&"function"==typeof e[l.lJ])return(0,i.S)(e);var t=(0,c.G)(e)?"an invalid object":"'"+e+"'";throw new TypeError("You provided "+t+" where a stream was expected. You can provide an Observable, Promise, Array, or Iterable.")}},678596:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.readUInt24BE=function(e,t){const r=e.readUInt8(t)<<16,n=e.readUInt8(t+1)<<8,o=e.readUInt8(t+2);return r|n|o},t.writeUInt24BE=function(e,t,r){return r=e.writeUInt8(t>>>16,r),r=e.writeUInt8(t>>>8&255,r),e.writeUInt8(255&t,r)},t.readUInt64BE=function(e,t){const r=e.readUInt32BE(t),n=e.readUInt32BE(t+4);return r*o+n},t.writeUInt64BE=function(e,t,r){const n=t/o|0,i=t%o;return r=e.writeUInt32BE(n,r),e.writeUInt32BE(i,r)},t.byteLength=function(e,t){if(null==e)return 0;return n.LiteBuffer.byteLength(e,t)},t.createBuffer=t.toBuffer=void 0;var n=r(514984);const o=4294967296;const i="function"==typeof n.LiteBuffer.from?(...e)=>e[0]instanceof n.LiteBuffer?e[0]:n.LiteBuffer.from.apply(n.LiteBuffer,e):(...e)=>e[0]instanceof n.LiteBuffer?e[0]:new(n.LiteBuffer.bind.apply(n.LiteBuffer,[n.LiteBuffer,...e]));t.toBuffer=i;const s="function"==typeof n.LiteBuffer.alloc?e=>n.LiteBuffer.alloc(e):e=>new n.LiteBuffer(e).fill(0);t.createBuffer=s},680639:(e,t,r)=>{"use strict";r.d(t,{y$:()=>u});r(262772);function n(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var o="function"==typeof Symbol&&Symbol.observable||"@@observable",i=function(){return Math.random().toString(36).substring(7).split("").join(".")},s={INIT:"@@redux/INIT"+i(),REPLACE:"@@redux/REPLACE"+i(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+i()}};function a(e){if("object"!=typeof e||null===e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function u(e,t,r){var i;if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw new Error(n(0));if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw new Error(n(1));return r(u)(e,t)}if("function"!=typeof e)throw new Error(n(2));var c=e,l=t,f=[],h=f,d=!1;function p(){h===f&&(h=f.slice())}function y(){if(d)throw new Error(n(3));return l}function b(e){if("function"!=typeof e)throw new Error(n(4));if(d)throw new Error(n(5));var t=!0;return p(),h.push(e),function(){if(t){if(d)throw new Error(n(6));t=!1,p();var r=h.indexOf(e);h.splice(r,1),f=null}}}function g(e){if(!a(e))throw new Error(n(7));if(void 0===e.type)throw new Error(n(8));if(d)throw new Error(n(9));try{d=!0,l=c(l,e)}finally{d=!1}for(var t=f=h,r=0;r<t.length;r++){(0,t[r])()}return e}return g({type:s.INIT}),(i={dispatch:g,subscribe:b,getState:y,replaceReducer:function(e){if("function"!=typeof e)throw new Error(n(10));c=e,g({type:s.REPLACE})}})[o]=function(){var e,t=b;return(e={subscribe:function(e){if("object"!=typeof e||null===e)throw new Error(n(11));function r(){e.next&&e.next(y())}return r(),{unsubscribe:t(r)}}})[o]=function(){return this},e},i}},682560:(e,t,r)=>{"use strict";r.d(t,{_:()=>n});var n=function(){function e(t,r){void 0===r&&(r=e.now),this.SchedulerAction=t,this.now=r}return e.prototype.schedule=function(e,t,r){return void 0===t&&(t=0),new this.SchedulerAction(this,e).schedule(r,t)},e.now=function(){return Date.now()},e}()},692840:(e,t,r)=>{"use strict";r.d(t,{U:()=>i});var n=r(998083),o=r(335679);function i(e){return void 0===e&&(e=null),function(t){return t.lift(new s(e))}}var s=function(){function e(e){this.defaultValue=e}return e.prototype.call=function(e,t){return t.subscribe(new a(e,this.defaultValue))},e}(),a=function(e){function t(t,r){var n=e.call(this,t)||this;return n.defaultValue=r,n.isEmpty=!0,n}return n.C6(t,e),t.prototype._next=function(e){this.isEmpty=!1,this.destination.next(e)},t.prototype._complete=function(){this.isEmpty&&this.destination.next(this.defaultValue),this.destination.complete()},t}(o.v)},701569:(e,t,r)=>{"use strict";function n(e,t){return{x:e.x-t.x,y:e.y-t.y}}function o(e){const{clientOffset:t,initialClientOffset:r,initialSourceClientOffset:o}=e;return t&&r&&o?n((s=o,{x:(i=t).x+s.x,y:i.y+s.y}),r):null;var i,s}function i(e){const{clientOffset:t,initialClientOffset:r}=e;return t&&r?n(t,r):null}r.d(t,{kO:()=>o,ne:()=>i})},704605:(e,t,r)=>{"use strict";r.d(t,{g:()=>o,n:()=>i});var n=r(656772);const o=(0,n.Bj)((()=>/firefox/i.test(navigator.userAgent))),i=(0,n.Bj)((()=>Boolean(window.safari)))},716339:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(374750),o=r(39716),i=r(142771),s=r(267921),a=r(88647),u=r(511894);function c(e={},t){return{dirtyHandlerIds:(0,s.A)(e.dirtyHandlerIds,{type:t.type,payload:Object.assign({},t.payload,{prevTargetIds:(0,u.Jt)(e,"dragOperation.targetIds",[])})}),dragOffset:(0,n.A)(e.dragOffset,t),refCount:(0,i.A)(e.refCount,t),dragOperation:(0,o.A)(e.dragOperation,t),stateId:(0,a.A)(e.stateId)}}},739001:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ReassemblyDuplexConnection=void 0;var n=r(514984),o=(r(914422),r(550558));t.ReassemblyDuplexConnection=class{constructor(e){this._source=e}sendOne(e){this._source.sendOne(e)}send(e){this._source.send(e)}receive(){return this._source.receive().lift((e=>new i(e)))}close(){this._source.close()}connect(){this._source.connect()}connectionStatus(){return this._source.connectionStatus()}};class i{constructor(e){var t,r,n;t=this,r="_framesReassemblyMap",n=new Map,r in t?Object.defineProperty(t,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[r]=n,this._actual=e}request(e){this._subscription.request(e)}cancel(){this._subscription.cancel(),this._framesReassemblyMap.clear()}onSubscribe(e){null==this._subscription?(this._subscription=e,this._actual.onSubscribe(this)):e.cancel()}onComplete(){this._actual.onComplete()}onError(e){this._actual.onError(e)}onNext(e){const t=e.streamId;if(t!==o.CONNECTION_STREAM_ID){const r=(0,o.isFollows)(e.flags),n=(0,o.isComplete)(e.flags),i=e.type===o.FRAME_TYPES.ERROR||e.type===o.FRAME_TYPES.CANCEL,a=this._framesReassemblyMap.get(t);if(a){if(!i)return a.metadata&&e.metadata&&(a.metadata=s(a.metadata,e.metadata)),a.data&&e.data?a.data=s(a.data,e.data):!a.data&&e.data&&(a.data=e.data),void(r&&!n||(n&&(a.flags|=o.FLAGS.COMPLETE),this._framesReassemblyMap.delete(t),this._actual.onNext(a)));this._framesReassemblyMap.delete(t)}else if(r&&!n&&!i)return void this._framesReassemblyMap.set(t,e)}this._actual.onNext(e)}}const s=(e,t)=>{switch(e.constructor.name){case"String":return e+t;case"Uint8Array":const r=new Uint8Array(e.length+t.length);return r.set(e),r.set(t,e.length),r;default:return n.LiteBuffer.concat([e,t])}}},741199:(e,t,r)=>{"use strict";r.d(t,{d:()=>o});var n=r(998083),o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n.C6(t,e),t}(r(799214).q)},741430:(e,t,r)=>{"use strict";r.d(t,{M:()=>a});var n=r(998083),o=r(335679),i=r(506104),s=r(387904);function a(e,t,r){return function(n){return n.lift(new u(e,t,r))}}var u=function(){function e(e,t,r){this.nextOrObserver=e,this.error=t,this.complete=r}return e.prototype.call=function(e,t){return t.subscribe(new c(e,this.nextOrObserver,this.error,this.complete))},e}(),c=function(e){function t(t,r,n,o){var a=e.call(this,t)||this;return a._tapNext=i.l,a._tapError=i.l,a._tapComplete=i.l,a._tapError=n||i.l,a._tapComplete=o||i.l,(0,s.T)(r)?(a._context=a,a._tapNext=r):r&&(a._context=r,a._tapNext=r.next||i.l,a._tapError=r.error||i.l,a._tapComplete=r.complete||i.l),a}return n.C6(t,e),t.prototype._next=function(e){try{this._tapNext.call(this._context,e)}catch(t){return void this.destination.error(t)}this.destination.next(e)},t.prototype._error=function(e){try{this._tapError.call(this._context,e)}catch(e){return void this.destination.error(e)}this.destination.error(e)},t.prototype._complete=function(){try{this._tapComplete.call(this._context)}catch(e){return void this.destination.error(e)}return this.destination.complete()},t}(o.v)},750150:(e,t,r)=>{"use strict";r.d(t,{v:()=>n});var n=function(e){return function(t){for(var r=0,n=e.length;r<n&&!t.closed;r++)t.next(e[r]);t.complete()}}},752174:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.BufferEncoders=t.Utf8Encoders=t.BufferEncoder=t.UTF8Encoder=void 0;var n,o=r(678596),i=(n=r(308552))&&n.__esModule?n:{default:n};const s={byteLength:e=>(0,o.byteLength)(e,"utf8"),decode:(e,t,r)=>e.toString("utf8",t,r),encode:(e,t,r,n)=>((0,i.default)("string"==typeof e,"RSocketEncoding: Expected value to be a string, got `%s`.",e),t.write(e,r,n-r,"utf8"),n)};t.UTF8Encoder=s;const a={byteLength:e=>((0,i.default)(Buffer.isBuffer(e),"RSocketEncoding: Expected value to be a buffer, got `%s`.",e),e.length),decode:(e,t,r)=>e.slice(t,r),encode:(e,t,r,n)=>((0,i.default)(Buffer.isBuffer(e),"RSocketEncoding: Expected value to be a buffer, got `%s`.",e),e.copy(t,r,0,e.length),n)};t.BufferEncoder=a;const u={data:s,dataMimeType:s,message:s,metadata:s,metadataMimeType:s,resumeToken:s};t.Utf8Encoders=u;const c={data:a,dataMimeType:s,message:s,metadata:a,metadataMimeType:s,resumeToken:a};t.BufferEncoders=c},753939:(e,t,r)=>{"use strict";r.d(t,{BI:()=>i,Xf:()=>o});const n=(e,t)=>e===t;function o(e,t){return!e&&!t||!(!e||!t)&&(e.x===t.x&&e.y===t.y)}function i(e,t,r=n){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(!r(e[n],t[n]))return!1;return!0}},757130:()=>{"use strict";String.prototype.endsWith||(String.prototype.endsWith=function(e,t){return(void 0===t||t>this.length)&&(t=this.length),this.substring(t-e.length,t)===e})},759098:(e,t,r)=>{"use strict";function n(e,t,r){const n=t.reduce(((t,r)=>t||e.getData(r)),"");return null!=n?n:r}r.d(t,{W:()=>n})},767430:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(390088);const o=r(408406);function i(e){return function(){const t=e.getMonitor(),r=e.getRegistry();!function(e){o(e.isDragging(),"Cannot call endDrag while not dragging.")}(t);const i=t.getSourceId();return r.getSource(i,!0).endDrag(t,i),r.unpinSource(),{type:n.dU}}}},771909:(e,t,r)=>{"use strict";r.d(t,{S:()=>o});var n=r(211794),o=function(e){return function(t){for(var r=e[n.lJ]();;){var o=void 0;try{o=r.next()}catch(i){return t.error(i),t}if(o.done){t.complete();break}if(t.next(o.value),t.closed)break}return"function"==typeof r.return&&t.add((function(){r.return&&r.return()})),t}}},773827:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(330238),o=r(390088);const i=r(408406);function s(e){return function(t,{clientOffset:r}={}){!function(e){i(Array.isArray(e),"Expected targetIds to be an array.")}(t);const s=t.slice(0),a=e.getMonitor(),u=e.getRegistry();!function(e,t,r){i(t.isDragging(),"Cannot call hover while not dragging."),i(!t.didDrop(),"Cannot call hover after drop.");for(let n=0;n<e.length;n++){const t=e[n];i(e.lastIndexOf(t)===n,"Expected targetIds to be unique in the passed array.");const o=r.getTarget(t);i(o,"Expected targetIds to be registered.")}}(s,a,u);return function(e,t,r){for(let o=e.length-1;o>=0;o--){const i=e[o],s=t.getTargetType(i);(0,n.A)(s,r)||e.splice(o,1)}}(s,u,a.getItemType()),function(e,t,r){for(const n of e){r.getTarget(n).hover(t,n)}}(s,a,u),{type:o.l6,payload:{targetIds:s,clientOffset:r||null}}}}},774850:(e,t,r)=>{"use strict";r.d(t,{F:()=>s});var n=r(482319),o=r(664191),i=r(226260);function s(e,t,r,s,a){if(void 0===a&&(a=new n.R(e,r,s)),!a.closed)return t instanceof i.c?t.subscribe(a):(0,o.i)(t)(a)}},779900:(e,t,r)=>{"use strict";r.d(t,{W:()=>i});var n=r(998083),o=r(914941);function i(e){return function(t){var r=new s(e),n=t.lift(r);return r.caught=n}}var s=function(){function e(e){this.selector=e}return e.prototype.call=function(e,t){return t.subscribe(new a(e,this.selector,this.caught))},e}(),a=function(e){function t(t,r,n){var o=e.call(this,t)||this;return o.selector=r,o.caught=n,o}return n.C6(t,e),t.prototype.error=function(t){if(!this.isStopped){var r=void 0;try{r=this.selector(t,this.caught)}catch(s){return void e.prototype.error.call(this,s)}this._unsubscribeAndRecycle();var n=new o.zA(this);this.add(n);var i=(0,o.tS)(r,n);i!==n&&this.add(i)}},t}(o.gn)},790337:(e,t,r)=>{"use strict";r.d(t,{$:()=>c});var n=r(317261),o=r(268245),i=r(316440),s=r(692840),a=r(868923),u=r(897210);function c(e,t){var r=arguments.length>=2;return function(c){return c.pipe(e?(0,o.p)((function(t,r){return e(t,r,c)})):u.D,(0,i.s)(1),r?(0,s.U)(t):(0,a.v)((function(){return new n.G})))}}},790874:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n,o=r(914422),i=(n=r(308552))&&n.__esModule?n:{default:n},s=r(550558),a=r(254136),u=r(572954);function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach((function(t){f(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function f(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}t.default=class{constructor(e,t,r){(0,i.default)(t.bufferSize>=0,"RSocketResumableTransport: bufferSize option must be >= 0, got `%s`.",t.bufferSize),this._encoders=r,this._bufferSize=t.bufferSize,this._sentFramesSize=0,this._position={client:0,server:0},this._currentConnection=null,this._statusSubscription=null,this._receiveSubscription=null,this._receivers=new Set,this._resumeToken=t.resumeToken,this._sessionTimeoutMillis=1e3*t.sessionDurationSeconds,this._sessionTimeoutHandle=null,this._senders=new Set,this._sentFrames=[],this._setupFrame=null,this._source=e,this._status=a.CONNECTION_STATUS.NOT_CONNECTED,this._statusSubscribers=new Set}close(){this._close()}connect(){(0,i.default)(!this._isTerminated(),"RSocketResumableTransport: Cannot connect(), connection terminated (%s: %s).",this._status.kind,"ERROR"===this._status.kind?this._status.error.message:"no message");try{this._disconnect(),this._currentConnection=null,this._receiveSubscription=null,this._statusSubscription=null,this._setConnectionStatus(a.CONNECTION_STATUS.CONNECTING);const e=this._source();e.connectionStatus().subscribe({onNext:t=>{t.kind!==this._status.kind&&("CONNECTED"===t.kind?(this._sessionTimeoutHandle&&(clearTimeout(this._sessionTimeoutHandle),this._sessionTimeoutHandle=null),null==this._setupFrame?this._handleConnected(e):this._handleResume(e)):this._isTerminationStatus(t)&&(this._sessionTimeoutHandle||(this._sessionTimeoutHandle=setTimeout((()=>this._close(this._resumeTimeoutError())),this._sessionTimeoutMillis)),this._disconnect(),this._setConnectionStatus(a.CONNECTION_STATUS.NOT_CONNECTED)))},onSubscribe:e=>{this._statusSubscription=e,e.request(Number.MAX_SAFE_INTEGER)}}),e.connect()}catch(e){this._close(e)}}connectionStatus(){return new o.Flowable((e=>{e.onSubscribe({cancel:()=>{this._statusSubscribers.delete(e)},request:()=>{this._statusSubscribers.add(e),e.onNext(this._status)}})}))}receive(){return new o.Flowable((e=>{let t=!1;e.onSubscribe({cancel:()=>{this._receivers.delete(e)},request:()=>{t||(t=!0,this._receivers.add(e))}})}))}sendOne(e){try{this._writeFrame(e)}catch(t){this._close(t)}}send(e){let t;e.subscribe({onComplete:()=>{t&&this._senders.delete(t)},onError:e=>{t&&this._senders.delete(t),this._close(e)},onNext:e=>this._writeFrame(e),onSubscribe:e=>{t=e,this._senders.add(t),t.request(Number.MAX_SAFE_INTEGER)}})}_close(e){if(this._isTerminated())return;e?this._setConnectionStatus({error:e,kind:"ERROR"}):this._setConnectionStatus(a.CONNECTION_STATUS.CLOSED);const t=this._receivers;t.forEach((e=>e.onComplete())),t.clear();const r=this._senders;r.forEach((e=>e.cancel())),r.clear(),this._sentFrames.length=0,this._disconnect()}_disconnect(){this._statusSubscription&&(this._statusSubscription.cancel(),this._statusSubscription=null),this._receiveSubscription&&(this._receiveSubscription.cancel(),this._receiveSubscription=null),this._currentConnection&&(this._currentConnection.close(),this._currentConnection=null)}_handleConnected(e){this._currentConnection=e,this._flushFrames(),this._setConnectionStatus(a.CONNECTION_STATUS.CONNECTED),e.receive().subscribe({onNext:e=>{try{this._receiveFrame(e)}catch(t){this._close(t)}},onSubscribe:e=>{this._receiveSubscription=e,e.request(Number.MAX_SAFE_INTEGER)}})}_handleResume(e){e.receive().take(1).subscribe({onNext:t=>{try{if(t.type===s.FRAME_TYPES.RESUME_OK){const{clientPosition:r}=t;if(r<this._position.client)return void this._close(this._nonResumableStateError());let n=r-this._position.client,o=0;for(;n>0;){const e=this._onReleasedTailFrame(this._sentFrames[o]);if(!e)return void this._close(this._absentLengthError(t));n-=e,o++}if(0!==n)return void this._close(this._inconsistentImpliedPositionError());o>0&&this._sentFrames.splice(0,o),this._handleConnected(e)}else{const e=t.type===s.FRAME_TYPES.ERROR?(0,s.createErrorFromFrame)(t):new Error("RSocketResumableTransport: Resumption failed for an unspecified reason.");this._close(e)}}catch(r){this._close(r)}},onSubscribe:e=>{this._receiveSubscription=e,e.request(1)}});const t=this._setupFrame;(0,i.default)(t,"RSocketResumableTransport: Cannot resume, setup frame has not been sent."),e.sendOne({clientPosition:this._position.client,flags:0,majorVersion:t.majorVersion,minorVersion:t.minorVersion,resumeToken:this._resumeToken,serverPosition:this._position.server,streamId:s.CONNECTION_STREAM_ID,type:s.FRAME_TYPES.RESUME})}_absentLengthError(e){return new Error("RSocketResumableTransport: absent frame.length for type "+e.type)}_inconsistentImpliedPositionError(){return new Error("RSocketResumableTransport: local frames are inconsistent with remote implied position")}_nonResumableStateError(){return new Error("RSocketResumableTransport: resumption failed, server is missing frames that are no longer in the client buffer.")}_resumeTimeoutError(){return new Error("RSocketResumableTransport: resumable session timed out")}_isTerminated(){return this._isTerminationStatus(this._status)}_isTerminationStatus(e){const t=e.kind;return"CLOSED"===t||"ERROR"===t}_setConnectionStatus(e){e.kind!==this._status.kind&&(this._status=e,this._statusSubscribers.forEach((t=>t.onNext(e))))}_receiveFrame(e){(0,s.isResumePositionFrameType)(e.type)&&e.length&&(this._position.server+=e.length),this._receivers.forEach((t=>t.onNext(e)))}_flushFrames(){this._sentFrames.forEach((e=>{const t=this._currentConnection;t&&t.sendOne(e)}))}_onReleasedTailFrame(e){const t=e.length;if(t)return this._sentFramesSize-=t,this._position.client+=t,t}_writeFrame(e){if(e.type===s.FRAME_TYPES.SETUP&&(e=l(l({},e),{},{flags:e.flags|s.FLAGS.RESUME_ENABLE,resumeToken:this._resumeToken}),this._setupFrame=e),e.length=(0,u.sizeOfFrame)(e,this._encoders),(0,s.isResumePositionFrameType)(e.type)){let t=this._bufferSize-this._sentFramesSize;const r=e.length;if(!r)return void this._close(this._absentLengthError(e));for(;t<r;){const r=this._sentFrames.shift();if(!r)break;{const n=this._onReleasedTailFrame(r);if(!n)return void this._close(this._absentLengthError(e));t+=n}}t>=r?(this._sentFrames.push(e),this._sentFramesSize+=r):this._position.client+=r}const t=this._currentConnection;t&&t.sendOne(e)}}},792768:(e,t,r)=>{"use strict";r.d(t,{Dk:()=>s,x3:()=>o,y2:()=>i});var n=r(511894);const o=[],i=[];function s(e,t){if(e===o)return!1;if(e===i||void 0===t)return!0;return(0,n.E$)(t,e).length>0}o.__IS_NONE__=!0,i.__IS_ALL__=!0},798603:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TYPES_BY_MIME_STRING=t.TYPES_BY_MIME_ID=t.MESSAGE_RSOCKET_COMPOSITE_METADATA=t.MESSAGE_RSOCKET_ROUTING=t.MESSAGE_RSOCKET_TRACING_ZIPKIN=t.MESSAGE_RSOCKET_AUTHENTICATION=t.MESSAGE_RSOCKET_ACCEPT_MIMETYPES=t.MESSAGE_RSOCKET_MIMETYPE=t.APPLICATION_CLOUDEVENTS_JSON=t.APPLICATION_JAVA_OBJECT=t.APPLICATION_HESSIAN=t.VIDEO_VP8=t.VIDEO_H265=t.VIDEO_H264=t.TEXT_XML=t.TEXT_PLAIN=t.TEXT_HTML=t.TEXT_CSV=t.TEXT_CSS=t.MULTIPART_MIXED=t.IMAGE_TIFF=t.IMAGE_PNG=t.IMAGE_JPEG=t.IMAGE_HEIF=t.IMAGE_HEIF_SEQUENCE=t.IMAGE_HEIC=t.IMAGE_HEIC_SEQUENCE=t.IMAGE_GIG=t.IMAGE_BMP=t.AUDIO_VORBIS=t.AUDIO_OPUS=t.AUDIO_OGG=t.AUDIO_MPEG=t.AUDIO_MPEG3=t.AUDIO_MP4=t.AUDIO_MP3=t.AUDIO_AAC=t.APPLICATION_ZIP=t.APPLICATION_XML=t.APPLICATION_PROTOBUF=t.APPLICATION_THRIFT=t.APPLICATION_PDF=t.APPLICATION_OCTET_STREAM=t.APPLICATION_JSON=t.APPLICATION_JAVASCRIPT=t.APPLICATION_GZIP=t.APPLICATION_GRAPHQL=t.APPLICATION_CBOR=t.APPLICATION_AVRO=t.UNKNOWN_RESERVED_MIME_TYPE=t.UNPARSEABLE_MIME_TYPE=t.default=void 0;class r{constructor(e,t){this._string=e,this._identifier=t}static fromIdentifier(e){return e<0||e>127?n:J[e]}static fromString(e){if(!e)throw new Error("type must be non-null");return e===o.string?n:Z.get(e)||n}get identifier(){return this._identifier}get string(){return this._string}toString(){return this._string}}t.default=r;const n=new r("UNPARSEABLE_MIME_TYPE_DO_NOT_USE",-2);t.UNPARSEABLE_MIME_TYPE=n;const o=new r("UNKNOWN_YET_RESERVED_DO_NOT_USE",-1);t.UNKNOWN_RESERVED_MIME_TYPE=o;const i=new r("application/avro",0);t.APPLICATION_AVRO=i;const s=new r("application/cbor",1);t.APPLICATION_CBOR=s;const a=new r("application/graphql",2);t.APPLICATION_GRAPHQL=a;const u=new r("application/gzip",3);t.APPLICATION_GZIP=u;const c=new r("application/javascript",4);t.APPLICATION_JAVASCRIPT=c;const l=new r("application/json",5);t.APPLICATION_JSON=l;const f=new r("application/octet-stream",6);t.APPLICATION_OCTET_STREAM=f;const h=new r("application/pdf",7);t.APPLICATION_PDF=h;const d=new r("application/vnd.apache.thrift.binary",8);t.APPLICATION_THRIFT=d;const p=new r("application/vnd.google.protobuf",9);t.APPLICATION_PROTOBUF=p;const y=new r("application/xml",10);t.APPLICATION_XML=y;const b=new r("application/zip",11);t.APPLICATION_ZIP=b;const g=new r("audio/aac",12);t.AUDIO_AAC=g;const v=new r("audio/mp3",13);t.AUDIO_MP3=v;const m=new r("audio/mp4",14);t.AUDIO_MP4=m;const E=new r("audio/mpeg3",15);t.AUDIO_MPEG3=E;const _=new r("audio/mpeg",16);t.AUDIO_MPEG=_;const O=new r("audio/ogg",17);t.AUDIO_OGG=O;const S=new r("audio/opus",18);t.AUDIO_OPUS=S;const w=new r("audio/vorbis",19);t.AUDIO_VORBIS=w;const T=new r("image/bmp",20);t.IMAGE_BMP=T;const A=new r("image/gif",21);t.IMAGE_GIG=A;const N=new r("image/heic-sequence",22);t.IMAGE_HEIC_SEQUENCE=N;const P=new r("image/heic",23);t.IMAGE_HEIC=P;const I=new r("image/heif-sequence",24);t.IMAGE_HEIF_SEQUENCE=I;const R=new r("image/heif",25);t.IMAGE_HEIF=R;const C=new r("image/jpeg",26);t.IMAGE_JPEG=C;const j=new r("image/png",27);t.IMAGE_PNG=j;const M=new r("image/tiff",28);t.IMAGE_TIFF=M;const x=new r("multipart/mixed",29);t.MULTIPART_MIXED=x;const D=new r("text/css",30);t.TEXT_CSS=D;const L=new r("text/csv",31);t.TEXT_CSV=L;const k=new r("text/html",32);t.TEXT_HTML=k;const U=new r("text/plain",33);t.TEXT_PLAIN=U;const F=new r("text/xml",34);t.TEXT_XML=F;const B=new r("video/H264",35);t.VIDEO_H264=B;const q=new r("video/H265",36);t.VIDEO_H265=q;const V=new r("video/VP8",37);t.VIDEO_VP8=V;const Y=new r("application/x-hessian",38);t.APPLICATION_HESSIAN=Y;const G=new r("application/x-java-object",39);t.APPLICATION_JAVA_OBJECT=G;const H=new r("application/cloudevents+json",40);t.APPLICATION_CLOUDEVENTS_JSON=H;const z=new r("message/x.rsocket.mime-type.v0",122);t.MESSAGE_RSOCKET_MIMETYPE=z;const K=new r("message/x.rsocket.accept-mime-types.v0",123);t.MESSAGE_RSOCKET_ACCEPT_MIMETYPES=K;const X=new r("message/x.rsocket.authentication.v0",124);t.MESSAGE_RSOCKET_AUTHENTICATION=X;const $=new r("message/x.rsocket.tracing-zipkin.v0",125);t.MESSAGE_RSOCKET_TRACING_ZIPKIN=$;const W=new r("message/x.rsocket.routing.v0",126);t.MESSAGE_RSOCKET_ROUTING=W;const Q=new r("message/x.rsocket.composite-metadata.v0",127);t.MESSAGE_RSOCKET_COMPOSITE_METADATA=Q;const J=new Array(128);t.TYPES_BY_MIME_ID=J;const Z=new Map;t.TYPES_BY_MIME_STRING=Z;const ee=[n,o,i,s,a,u,c,l,f,h,d,p,y,b,g,v,m,E,_,O,S,w,T,A,N,P,I,R,C,j,M,x,D,L,k,U,F,B,q,V,Y,G,H,z,K,X,$,W,Q];J.fill(o);for(const te of ee)te.identifier>=0&&(J[te.identifier]=te,Z.set(te.string,te));Object.seal&&Object.seal(J)},799214:(e,t,r)=>{"use strict";r.d(t,{q:()=>i});var n=r(998083),o=r(682560),i=function(e){function t(r,n){void 0===n&&(n=o._.now);var i=e.call(this,r,(function(){return t.delegate&&t.delegate!==i?t.delegate.now():n()}))||this;return i.actions=[],i.active=!1,i.scheduled=void 0,i}return n.C6(t,e),t.prototype.schedule=function(r,n,o){return void 0===n&&(n=0),t.delegate&&t.delegate!==this?t.delegate.schedule(r,n,o):e.prototype.schedule.call(this,r,n,o)},t.prototype.flush=function(e){var t=this.actions;if(this.active)t.push(e);else{var r;this.active=!0;do{if(r=e.execute(e.state,e.delay))break}while(e=t.shift());if(this.active=!1,r){for(;e=t.shift();)e.unsubscribe();throw r}}},t}(o._)},799716:(e,t,r)=>{"use strict";const n=r(845212).memo((({connect:e,src:t})=>{if("undefined"!=typeof Image){const r=new Image;r.src=t,r.onload=()=>e(r)}return null}));n.displayName="DragPreviewImage"},799769:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>c,BS:()=>u.BS,KR:()=>u.KR,Vw:()=>u.Vw,dU:()=>u.dU,l6:()=>u.l6,q2:()=>u.q2});var n=r(372222),o=r(38173),i=r(773827),s=r(453404),a=r(767430),u=r(390088);function c(e){return{beginDrag:(0,n.A)(e),publishDragSource:(0,o.A)(e),hover:(0,i.A)(e),drop:(0,s.A)(e),endDrag:(0,a.A)(e)}}},807148:(e,t,r)=>{"use strict";function n(e){return"function"==typeof e}function o(){}function i(e){if(!function(e){return"object"==typeof e&&null!==e}(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}r.d(t,{Qd:()=>i,Tn:()=>n,lQ:()=>o})},809774:e=>{!function(){"use strict";e.exports={polyfill:function(){var e=window,t=document;if(!("scrollBehavior"in t.documentElement.style)||!0===e.__forceSmoothScrollPolyfill__){var r,n=e.HTMLElement||e.Element,o=468,i={scroll:e.scroll||e.scrollTo,scrollBy:e.scrollBy,elementScroll:n.prototype.scroll||u,scrollIntoView:n.prototype.scrollIntoView},s=e.performance&&e.performance.now?e.performance.now.bind(e.performance):Date.now,a=(r=e.navigator.userAgent,new RegExp(["MSIE ","Trident/","Edge/"].join("|")).test(r)?1:0);e.scroll=e.scrollTo=function(){void 0!==arguments[0]&&(!0!==c(arguments[0])?p.call(e,t.body,void 0!==arguments[0].left?~~arguments[0].left:e.scrollX||e.pageXOffset,void 0!==arguments[0].top?~~arguments[0].top:e.scrollY||e.pageYOffset):i.scroll.call(e,void 0!==arguments[0].left?arguments[0].left:"object"!=typeof arguments[0]?arguments[0]:e.scrollX||e.pageXOffset,void 0!==arguments[0].top?arguments[0].top:void 0!==arguments[1]?arguments[1]:e.scrollY||e.pageYOffset))},e.scrollBy=function(){void 0!==arguments[0]&&(c(arguments[0])?i.scrollBy.call(e,void 0!==arguments[0].left?arguments[0].left:"object"!=typeof arguments[0]?arguments[0]:0,void 0!==arguments[0].top?arguments[0].top:void 0!==arguments[1]?arguments[1]:0):p.call(e,t.body,~~arguments[0].left+(e.scrollX||e.pageXOffset),~~arguments[0].top+(e.scrollY||e.pageYOffset)))},n.prototype.scroll=n.prototype.scrollTo=function(){if(void 0!==arguments[0])if(!0!==c(arguments[0])){var e=arguments[0].left,t=arguments[0].top;p.call(this,this,void 0===e?this.scrollLeft:~~e,void 0===t?this.scrollTop:~~t)}else{if("number"==typeof arguments[0]&&void 0===arguments[1])throw new SyntaxError("Value could not be converted");i.elementScroll.call(this,void 0!==arguments[0].left?~~arguments[0].left:"object"!=typeof arguments[0]?~~arguments[0]:this.scrollLeft,void 0!==arguments[0].top?~~arguments[0].top:void 0!==arguments[1]?~~arguments[1]:this.scrollTop)}},n.prototype.scrollBy=function(){void 0!==arguments[0]&&(!0!==c(arguments[0])?this.scroll({left:~~arguments[0].left+this.scrollLeft,top:~~arguments[0].top+this.scrollTop,behavior:arguments[0].behavior}):i.elementScroll.call(this,void 0!==arguments[0].left?~~arguments[0].left+this.scrollLeft:~~arguments[0]+this.scrollLeft,void 0!==arguments[0].top?~~arguments[0].top+this.scrollTop:~~arguments[1]+this.scrollTop))},n.prototype.scrollIntoView=function(){if(!0!==c(arguments[0])){var r=function(e){for(;e!==t.body&&!1===h(e);)e=e.parentNode||e.host;return e}(this),n=r.getBoundingClientRect(),o=this.getBoundingClientRect();r!==t.body?(p.call(this,r,r.scrollLeft+o.left-n.left,r.scrollTop+o.top-n.top),"fixed"!==e.getComputedStyle(r).position&&e.scrollBy({left:n.left,top:n.top,behavior:"smooth"})):e.scrollBy({left:o.left,top:o.top,behavior:"smooth"})}else i.scrollIntoView.call(this,void 0===arguments[0]||arguments[0])}}function u(e,t){this.scrollLeft=e,this.scrollTop=t}function c(e){if(null===e||"object"!=typeof e||void 0===e.behavior||"auto"===e.behavior||"instant"===e.behavior)return!0;if("object"==typeof e&&"smooth"===e.behavior)return!1;throw new TypeError("behavior member of ScrollOptions "+e.behavior+" is not a valid value for enumeration ScrollBehavior.")}function l(e,t){return"Y"===t?e.clientHeight+a<e.scrollHeight:"X"===t?e.clientWidth+a<e.scrollWidth:void 0}function f(t,r){var n=e.getComputedStyle(t,null)["overflow"+r];return"auto"===n||"scroll"===n}function h(e){var t=l(e,"Y")&&f(e,"Y"),r=l(e,"X")&&f(e,"X");return t||r}function d(t){var r,n,i,a,u=(s()-t.startTime)/o;a=u=u>1?1:u,r=.5*(1-Math.cos(Math.PI*a)),n=t.startX+(t.x-t.startX)*r,i=t.startY+(t.y-t.startY)*r,t.method.call(t.scrollable,n,i),n===t.x&&i===t.y||e.requestAnimationFrame(d.bind(e,t))}function p(r,n,o){var a,c,l,f,h=s();r===t.body?(a=e,c=e.scrollX||e.pageXOffset,l=e.scrollY||e.pageYOffset,f=i.scroll):(a=r,c=r.scrollLeft,l=r.scrollTop,f=u),d({scrollable:a,method:f,startTime:h,startX:c,startY:l,x:n,y:o})}}}}()},809785:(e,t,r)=>{"use strict";let n;function o(){return n||(n=new Image,n.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="),n}r.d(t,{A:()=>o})},814737:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});class n{constructor(e,t){const{length:r}=e,n=[];for(let d=0;d<r;d++)n.push(d);n.sort(((t,r)=>e[t]<e[r]?-1:1));const o=[],i=[],s=[];let a,u;for(let d=0;d<r-1;d++)a=e[d+1]-e[d],u=t[d+1]-t[d],i.push(a),o.push(u),s.push(u/a);const c=[s[0]];for(let d=0;d<i.length-1;d++){const e=s[d],t=s[d+1];if(e*t<=0)c.push(0);else{a=i[d];const r=i[d+1],n=a+r;c.push(3*n/((n+r)/e+(n+a)/t))}}c.push(s[s.length-1]);const l=[],f=[];let h;for(let d=0;d<c.length-1;d++){h=s[d];const e=c[d],t=1/i[d],r=e+c[d+1]-h-h;l.push((h-e-r)*t),f.push(r*t*t)}this.xs=e,this.ys=t,this.c1s=c,this.c2s=l,this.c3s=f}interpolate(e){const{xs:t,ys:r,c1s:n,c2s:o,c3s:i}=this;let s=t.length-1;if(e===t[s])return r[s];let a,u=0,c=i.length-1;for(;u<=c;){a=Math.floor(.5*(u+c));const n=t[a];if(n<e)u=a+1;else{if(!(n>e))return r[a];c=a-1}}s=Math.max(0,c);const l=e-t[s],f=l*l;return r[s]+n[s]*l+o[s]*f+i[s]*l*f}}},818334:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var n=function(){function e(e){return Error.call(this),this.message=e?e.length+" errors occurred during unsubscription:\n"+e.map((function(e,t){return t+1+") "+e.toString()})).join("\n  "):"",this.name="UnsubscriptionError",this.errors=e,this}return e.prototype=Object.create(Error.prototype),e}()},820671:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.encodeRoutes=function(...e){if(e.length<1)throw new Error("routes should be non empty array");return n.LiteBuffer.concat(e.map((e=>s(e))))},t.encodeRoute=s,t.decodeRoutes=a,t.RoutingMetadata=void 0;var n=r(514984),o=r(678596);class i{constructor(e){this._buffer=e}iterator(){return a(this._buffer)}[Symbol.iterator](){return a(this._buffer)}}function s(e){const t=(0,o.toBuffer)(e,"utf8");if(t.length>255)throw new Error(`route length should fit into unsigned byte length but the given one is ${t.length}`);const r=(0,o.createBuffer)(1);return r.writeUInt8(t.length),n.LiteBuffer.concat([r,t])}function*a(e){const t=e.byteLength;let r=0;for(;r<t;){const n=e.readUInt8(r++);if(r+n>t)throw new Error(`Malformed RouteMetadata. Offset(${r}) + RouteLength(${n}) is greater than TotalLength`);const o=e.toString("utf8",r,r+n);r+=n,yield o}}t.RoutingMetadata=i},836439:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>s,n5:()=>o.A,vQ:()=>i});var n=r(460907),o=r(809785),i=r(983923);function s(e){return new n.A(e)}},851900:(e,t,r)=>{"use strict";r.d(t,{c:()=>u});var n=r(998083),o=r(949359),i=r(870516),s=r(335679),a=r(354414);function u(e,t){void 0===t&&(t=o.b);var r=(0,i.$)(e)?+e-t.now():Math.abs(e);return function(e){return e.lift(new c(r,t))}}var c=function(){function e(e,t){this.delay=e,this.scheduler=t}return e.prototype.call=function(e,t){return t.subscribe(new l(e,this.delay,this.scheduler))},e}(),l=function(e){function t(t,r,n){var o=e.call(this,t)||this;return o.delay=r,o.scheduler=n,o.queue=[],o.active=!1,o.errored=!1,o}return n.C6(t,e),t.dispatch=function(e){for(var t=e.source,r=t.queue,n=e.scheduler,o=e.destination;r.length>0&&r[0].time-n.now()<=0;)r.shift().notification.observe(o);if(r.length>0){var i=Math.max(0,r[0].time-n.now());this.schedule(e,i)}else this.unsubscribe(),t.active=!1},t.prototype._schedule=function(e){this.active=!0,this.destination.add(e.schedule(t.dispatch,this.delay,{source:this,destination:this.destination,scheduler:e}))},t.prototype.scheduleNotification=function(e){if(!0!==this.errored){var t=this.scheduler,r=new f(t.now()+this.delay,e);this.queue.push(r),!1===this.active&&this._schedule(t)}},t.prototype._next=function(e){this.scheduleNotification(a.E.createNext(e))},t.prototype._error=function(e){this.errored=!0,this.queue=[],this.destination.error(e),this.unsubscribe()},t.prototype._complete=function(){this.scheduleNotification(a.E.createComplete()),this.unsubscribe()},t}(s.v),f=function(){return function(e,t){this.time=e,this.notification=t}}()},868923:(e,t,r)=>{"use strict";r.d(t,{v:()=>s});var n=r(998083),o=r(317261),i=r(335679);function s(e){return void 0===e&&(e=c),function(t){return t.lift(new a(e))}}var a=function(){function e(e){this.errorFactory=e}return e.prototype.call=function(e,t){return t.subscribe(new u(e,this.errorFactory))},e}(),u=function(e){function t(t,r){var n=e.call(this,t)||this;return n.errorFactory=r,n.hasValue=!1,n}return n.C6(t,e),t.prototype._next=function(e){this.hasValue=!0,this.destination.next(e)},t.prototype._complete=function(){if(this.hasValue)return this.destination.complete();var e=void 0;try{e=this.errorFactory()}catch(t){e=t}this.destination.error(e)},t}(i.v);function c(){return new o.G}},870516:(e,t,r)=>{"use strict";function n(e){return e instanceof Date&&!isNaN(+e)}r.d(t,{$:()=>n})},875828:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(330238),o=r(701569),i=r(792768);const s=r(408406);class a{constructor(e,t){this.store=e,this.registry=t}subscribeToStateChange(e,t={handlerIds:void 0}){const{handlerIds:r}=t;s("function"==typeof e,"listener must be a function."),s(void 0===r||Array.isArray(r),"handlerIds, when specified, must be an array of strings.");let n=this.store.getState().stateId;return this.store.subscribe((()=>{const t=this.store.getState(),o=t.stateId;try{o===n||o===n+1&&!(0,i.Dk)(t.dirtyHandlerIds,r)||e()}finally{n=o}}))}subscribeToOffsetChange(e){s("function"==typeof e,"listener must be a function.");let t=this.store.getState().dragOffset;return this.store.subscribe((()=>{const r=this.store.getState().dragOffset;r!==t&&(t=r,e())}))}canDragSource(e){if(!e)return!1;const t=this.registry.getSource(e);return s(t,"Expected to find a valid source."),!this.isDragging()&&t.canDrag(this,e)}canDropOnTarget(e){if(!e)return!1;const t=this.registry.getTarget(e);if(s(t,"Expected to find a valid target."),!this.isDragging()||this.didDrop())return!1;const r=this.registry.getTargetType(e),o=this.getItemType();return(0,n.A)(r,o)&&t.canDrop(this,e)}isDragging(){return Boolean(this.getItemType())}isDraggingSource(e){if(!e)return!1;const t=this.registry.getSource(e,!0);if(s(t,"Expected to find a valid source."),!this.isDragging()||!this.isSourcePublic())return!1;return this.registry.getSourceType(e)===this.getItemType()&&t.isDragging(this,e)}isOverTarget(e,t={shallow:!1}){if(!e)return!1;const{shallow:r}=t;if(!this.isDragging())return!1;const o=this.registry.getTargetType(e),i=this.getItemType();if(i&&!(0,n.A)(o,i))return!1;const s=this.getTargetIds();if(!s.length)return!1;const a=s.indexOf(e);return r?a===s.length-1:a>-1}getItemType(){return this.store.getState().dragOperation.itemType}getItem(){return this.store.getState().dragOperation.item}getSourceId(){return this.store.getState().dragOperation.sourceId}getTargetIds(){return this.store.getState().dragOperation.targetIds}getDropResult(){return this.store.getState().dragOperation.dropResult}didDrop(){return this.store.getState().dragOperation.didDrop}isSourcePublic(){return this.store.getState().dragOperation.isSourcePublic}getInitialClientOffset(){return this.store.getState().dragOffset.initialClientOffset}getInitialSourceClientOffset(){return this.store.getState().dragOffset.initialSourceClientOffset}getClientOffset(){return this.store.getState().dragOffset.clientOffset}getSourceClientOffset(){return(0,o.kO)(this.store.getState().dragOffset)}getDifferenceFromInitialOffset(){return(0,o.ne)(this.store.getState().dragOffset)}}},878796:(e,t,r)=>{"use strict";r.d(t,{P:()=>n});var n=function(){function e(){return Error.call(this),this.message="object unsubscribed",this.name="ObjectUnsubscribedError",this}return e.prototype=Object.create(Error.prototype),e}()},880029:(e,t,r)=>{"use strict";r.d(t,{zV:()=>l});var n=r(998083),o=r(453535),i=r(375527),s=r(229664),a=r(774850),u=r(550742),c={};function l(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=void 0,n=void 0;return(0,o.m)(e[e.length-1])&&(n=e.pop()),"function"==typeof e[e.length-1]&&(r=e.pop()),1===e.length&&(0,i.c)(e[0])&&(e=e[0]),(0,u.c)(e,n).lift(new f(r))}var f=function(){function e(e){this.resultSelector=e}return e.prototype.call=function(e,t){return t.subscribe(new h(e,this.resultSelector))},e}(),h=function(e){function t(t,r){var n=e.call(this,t)||this;return n.resultSelector=r,n.active=0,n.values=[],n.observables=[],n}return n.C6(t,e),t.prototype._next=function(e){this.values.push(c),this.observables.push(e)},t.prototype._complete=function(){var e=this.observables,t=e.length;if(0===t)this.destination.complete();else{this.active=t,this.toRespond=t;for(var r=0;r<t;r++){var n=e[r];this.add((0,a.F)(this,n,void 0,r))}}},t.prototype.notifyComplete=function(e){0==(this.active-=1)&&this.destination.complete()},t.prototype.notifyNext=function(e,t,r){var n=this.values,o=n[r],i=this.toRespond?o===c?--this.toRespond:this.toRespond:0;n[r]=t,0===i&&(this.resultSelector?this._tryResultSelector(n):this.destination.next(n.slice()))},t.prototype._tryResultSelector=function(e){var t;try{t=this.resultSelector.apply(this,e)}catch(r){return void this.destination.error(r)}this.destination.next(t)},t}(s.E)},881800:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(11585);function o(e,t){if("object"!=(0,n.A)(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,t||"default");if("object"!=(0,n.A)(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}},895693:(e,t,r)=>{"use strict";r.d(t,{ZZ:()=>a,qI:()=>l});var n=r(998083),o=r(535921),i=r(212943),s=r(914941);function a(e,t,r){return void 0===r&&(r=Number.POSITIVE_INFINITY),"function"==typeof t?function(n){return n.pipe(a((function(r,n){return(0,i.H)(e(r,n)).pipe((0,o.T)((function(e,o){return t(r,e,n,o)})))}),r))}:("number"==typeof t&&(r=t),function(t){return t.lift(new u(e,r))})}var u=function(){function e(e,t){void 0===t&&(t=Number.POSITIVE_INFINITY),this.project=e,this.concurrent=t}return e.prototype.call=function(e,t){return t.subscribe(new c(e,this.project,this.concurrent))},e}(),c=function(e){function t(t,r,n){void 0===n&&(n=Number.POSITIVE_INFINITY);var o=e.call(this,t)||this;return o.project=r,o.concurrent=n,o.hasCompleted=!1,o.buffer=[],o.active=0,o.index=0,o}return n.C6(t,e),t.prototype._next=function(e){this.active<this.concurrent?this._tryNext(e):this.buffer.push(e)},t.prototype._tryNext=function(e){var t,r=this.index++;try{t=this.project(e,r)}catch(n){return void this.destination.error(n)}this.active++,this._innerSub(t)},t.prototype._innerSub=function(e){var t=new s.zA(this),r=this.destination;r.add(t);var n=(0,s.tS)(e,t);n!==t&&r.add(n)},t.prototype._complete=function(){this.hasCompleted=!0,0===this.active&&0===this.buffer.length&&this.destination.complete(),this.unsubscribe()},t.prototype.notifyNext=function(e){this.destination.next(e)},t.prototype.notifyComplete=function(){var e=this.buffer;this.active--,e.length>0?this._next(e.shift()):0===this.active&&this.hasCompleted&&this.destination.complete()},t}(s.gn),l=a},897e3:(e,t,r)=>{"use strict";r.d(t,{A:()=>f});var n=r(845212),o=r(366854),i=r(523173),s=r(584600),a=r(807148);const u=r(409838),c=r(408406),l=r(146462);function f(e,t={}){return(0,o.A)("DragLayer","collect[, options]",e,t),c("function"==typeof e,'Expected "collect" provided as the first argument to DragLayer to be a function that collects props to inject into the component. ',"Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-layer",e),c((0,a.Qd)(t),'Expected "options" provided as the second argument to DragLayer to be a plain object when specified. Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-layer',t),function(r){const o=r,{arePropsEqual:a=l}=t,f=o.displayName||o.name||"Component";class h extends n.Component{constructor(){super(...arguments),this.isCurrentlyMounted=!1,this.ref=n.createRef(),this.handleChange=()=>{if(!this.isCurrentlyMounted)return;const e=this.getCurrentState();l(e,this.state)||this.setState(e)}}getDecoratedComponentInstance(){return c(this.ref.current,"In order to access an instance of the decorated component, it must either be a class component or use React.forwardRef()"),this.ref.current}shouldComponentUpdate(e,t){return!a(e,this.props)||!l(t,this.state)}componentDidMount(){this.isCurrentlyMounted=!0,this.handleChange()}componentWillUnmount(){this.isCurrentlyMounted=!1,this.unsubscribeFromOffsetChange&&(this.unsubscribeFromOffsetChange(),this.unsubscribeFromOffsetChange=void 0),this.unsubscribeFromStateChange&&(this.unsubscribeFromStateChange(),this.unsubscribeFromStateChange=void 0)}render(){return n.createElement(i.ZC,null,(({dragDropManager:e})=>void 0===e?null:(this.receiveDragDropManager(e),this.isCurrentlyMounted?n.createElement(o,Object.assign({},this.props,this.state,{ref:(0,s.Y)(o)?this.ref:null})):null)))}receiveDragDropManager(e){if(void 0!==this.manager){const e=this.manager.getMonitor();return void 0===this.unsubscribeFromOffsetChange&&(this.unsubscribeFromOffsetChange=e.subscribeToOffsetChange(this.handleChange)),void(void 0===this.unsubscribeFromStateChange&&(this.unsubscribeFromStateChange=e.subscribeToStateChange(this.handleChange)))}this.manager=e,c("object"==typeof e,"Could not find the drag and drop manager in the context of %s. Make sure to wrap the top-level component of your app with DragDropContext. Read more: http://react-dnd.github.io/react-dnd/docs/troubleshooting#could-not-find-the-drag-and-drop-manager-in-the-context",f,f);const t=this.manager.getMonitor();this.unsubscribeFromOffsetChange=t.subscribeToOffsetChange(this.handleChange),this.unsubscribeFromStateChange=t.subscribeToStateChange(this.handleChange)}getCurrentState(){if(!this.manager)return{};const t=this.manager.getMonitor();return e(t,this.props)}}return h.displayName=`DragLayer(${f})`,h.DecoratedComponent=r,u(h,r)}}},897210:(e,t,r)=>{"use strict";function n(e){return e}r.d(t,{D:()=>n})},897953:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.encodeWellKnownAuthMetadata=function(e,t){if(e===i.UNPARSEABLE_AUTH_TYPE||e===i.UNKNOWN_RESERVED_AUTH_TYPE)throw new Error(`Illegal WellKnownAuthType[${e.toString()}]. Only allowed AuthType should be used`);const r=(0,o.createBuffer)(a);return r.writeUInt8(e.identifier|l),n.LiteBuffer.concat([r,t])},t.encodeCustomAuthMetadata=function(e,t){const r=(0,o.toBuffer)(e);if(r.byteLength!==e.length)throw new Error("Custom auth type must be US_ASCII characters only");if(r.byteLength<1||r.byteLength>128)throw new Error("Custom auth type must have a strictly positive length that fits on 7 unsigned bits, ie 1-128");const i=(0,o.createBuffer)(u+r.byteLength);return i.writeUInt8(r.byteLength-1),i.write(e,u),n.LiteBuffer.concat([i,t])},t.encodeSimpleAuthMetadata=function(e,t){const r=(0,o.toBuffer)(e),s=(0,o.toBuffer)(t),u=r.byteLength;if(u>65535)throw new Error(`Username should be shorter than or equal to 65535 bytes length in UTF-8 encoding but the given was ${u}`);const f=a+c,h=(0,o.createBuffer)(f);return h.writeUInt8(i.SIMPLE.identifier|l),h.writeUInt16BE(u,1),n.LiteBuffer.concat([h,r,s])},t.encodeBearerAuthMetadata=function(e){const t=(0,o.toBuffer)(e),r=(0,o.createBuffer)(a);return r.writeUInt8(i.BEARER.identifier|l),n.LiteBuffer.concat([r,t])},t.decodeAuthMetadata=function(e){if(e.byteLength<1)throw new Error("Unable to decode Auth metadata. Not enough readable bytes");const t=e.readUInt8(),r=t&f;if(r!==t){const t=i.default.fromIdentifier(r);return{payload:e.slice(1),type:{identifier:t.identifier,string:t.string}}}{const r=t+1;if(e.byteLength<r+u)throw new Error("Unable to decode custom Auth type. Malformed length or auth type string");const n=e.toString("utf8",u,u+r);return{payload:e.slice(r+u),type:{identifier:i.UNPARSEABLE_AUTH_TYPE.identifier,string:n}}}},t.decodeSimpleAuthPayload=function(e){if(e.byteLength<c)throw new Error("Unable to decode Simple Auth Payload. Not enough readable bytes");const t=e.readUInt16BE();if(e.byteLength<t+c)throw new Error("Unable to decode Simple Auth Payload. Not enough readable bytes");const r=e.slice(c,c+t);return{password:e.slice(c+t),username:r}};var n=r(514984),o=r(678596),i=function(e){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=s();if(t&&t.has(e))return t.get(e);var r={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var i=n?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(r,o,i):r[o]=e[o]}r.default=e,t&&t.set(e,r);return r}(r(339261));function s(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return s=function(){return e},e}const a=1,u=1,c=2,l=128,f=127},899474:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(198333);const o=r(408406),i=["canDrop","hover","drop"];class s{constructor(e,t,r){this.spec=e,this.monitor=t,this.ref=r,this.props=null}receiveProps(e){this.props=e}receiveMonitor(e){this.monitor=e}canDrop(){return!this.spec.canDrop||this.spec.canDrop(this.props,this.monitor)}hover(){this.spec.hover&&this.spec.hover(this.props,this.monitor,(0,n.P)(this.ref))}drop(){if(!this.spec.drop)return;return this.spec.drop(this.props,this.monitor,this.ref.current)}}function a(e){return Object.keys(e).forEach((t=>{o(i.indexOf(t)>-1,'Expected the drop target specification to only have some of the following keys: %s. Instead received a specification with an unexpected "%s" key. Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target',i.join(", "),t),o("function"==typeof e[t],"Expected %s in the drop target specification to be a function. Instead received a specification with %s: %s. Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target",t,t,e[t])})),function(t,r){return new s(e,t,r)}}},911832:e=>{"use strict";e.exports=e=>!(!e||e.length<3)&&(255===e[0]&&216===e[1]&&255===e[2])},912360:(e,t,r)=>{"use strict";r.d(t,{k:()=>n});class n{constructor(...e){this.isDisposed=!1,this.disposables=e}add(e){this.isDisposed?e.dispose():this.disposables.push(e)}remove(e){let t=!1;if(!this.isDisposed){const r=this.disposables.indexOf(e);-1!==r&&(t=!0,this.disposables.splice(r,1),e.dispose())}return t}clear(){if(!this.isDisposed){const e=this.disposables.length,t=new Array(e);for(let r=0;r<e;r++)t[r]=this.disposables[r];this.disposables=[];for(let r=0;r<e;r++)t[r].dispose()}}dispose(){if(!this.isDisposed){this.isDisposed=!0;const e=this.disposables.length,t=new Array(e);for(let r=0;r<e;r++)t[r]=this.disposables[r];this.disposables=[];for(let r=0;r<e;r++)t[r].dispose()}}}},914422:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Flowable",{enumerable:!0,get:function(){return n.default}}),Object.defineProperty(t,"Single",{enumerable:!0,get:function(){return o.default}}),Object.defineProperty(t,"FlowableProcessor",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(t,"every",{enumerable:!0,get:function(){return s.every}});var n=a(r(110676)),o=a(r(29732)),i=a(r(312098)),s=r(223331);function a(e){return e&&e.__esModule?e:{default:e}}},914941:(e,t,r)=>{"use strict";r.d(t,{gn:()=>u,tS:()=>c,zA:()=>a});var n=r(998083),o=r(335679),i=r(226260),s=r(664191),a=function(e){function t(t){var r=e.call(this)||this;return r.parent=t,r}return n.C6(t,e),t.prototype._next=function(e){this.parent.notifyNext(e)},t.prototype._error=function(e){this.parent.notifyError(e),this.unsubscribe()},t.prototype._complete=function(){this.parent.notifyComplete(),this.unsubscribe()},t}(o.v),u=(o.v,function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n.C6(t,e),t.prototype.notifyNext=function(e){this.destination.next(e)},t.prototype.notifyError=function(e){this.destination.error(e)},t.prototype.notifyComplete=function(){this.destination.complete()},t}(o.v));o.v;function c(e,t){if(!t.closed){if(e instanceof i.c)return e.subscribe(t);var r;try{r=(0,s.i)(e)(t)}catch(n){t.error(n)}return r}}},915838:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(198333);const o=r(408406),i=["canDrag","beginDrag","isDragging","endDrag"],s=["beginDrag"];class a{constructor(e,t,r){this.spec=e,this.monitor=t,this.ref=r,this.props=null,this.beginDrag=()=>{if(!this.props)return;return this.spec.beginDrag(this.props,this.monitor,this.ref.current)}}receiveProps(e){this.props=e}canDrag(){return!!this.props&&(!this.spec.canDrag||this.spec.canDrag(this.props,this.monitor))}isDragging(e,t){return!!this.props&&(this.spec.isDragging?this.spec.isDragging(this.props,this.monitor):t===e.getSourceId())}endDrag(){this.props&&this.spec.endDrag&&this.spec.endDrag(this.props,this.monitor,(0,n.P)(this.ref))}}function u(e){return Object.keys(e).forEach((t=>{o(i.indexOf(t)>-1,'Expected the drag source specification to only have some of the following keys: %s. Instead received a specification with an unexpected "%s" key. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source',i.join(", "),t),o("function"==typeof e[t],"Expected %s in the drag source specification to be a function. Instead received a specification with %s: %s. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source",t,t,e[t])})),s.forEach((t=>{o("function"==typeof e[t],"Expected %s in the drag source specification to be a function. Instead received a specification with %s: %s. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source",t,t,e[t])})),function(t,r){return new a(e,t,r)}}},922211:e=>{var t;window,t=function(){return function(e){var t={};function r(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return e[n].call(o.exports,o,o.exports,r),o.l=!0,o.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)r.d(n,o,function(t){return e[t]}.bind(null,o));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=85)}([function(e,t,r){"use strict";r.d(t,"c",(function(){return S})),r.d(t,"b",(function(){return T})),r.d(t,"d",(function(){return N})),r.d(t,"a",(function(){return w}));var n=r(1),o=r.n(n),i=r(10),s=r.n(i),a=r(17),u=r.n(a),c=r(2),l=r(5),f=r(3),h=r(9),d=r(6),p=r(25),y=r(11),b=r(20);function g(e){return g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},g(e)}function v(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function m(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i=[],s=!0,a=!1;try{for(r=r.call(e);!(s=(n=r.next()).done)&&(i.push(n.value),!t||i.length!==t);s=!0);}catch(u){a=!0,o=u}finally{try{s||null==r.return||r.return()}finally{if(a)throw o}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return E(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?E(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function E(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function _(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var O=Object(y.a)("quill"),S=new c.Registry;c.ParentBlot.uiClass="ql-ui";var w=function(){function e(t){var r=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.options=T(t,n),this.container=this.options.container,null==this.container)return O.error("Invalid Quill container",t);this.options.debug&&e.debug(this.options.debug);var o=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",p.a.set(this.container,this),this.root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.scrollingContainer=this.options.scrollingContainer||this.root,this.emitter=new f.a;var i=this.options.registry.query(c.ScrollBlot.blotName);this.scroll=new i(this.options.registry,this.root,{emitter:this.emitter}),this.editor=new l.a(this.scroll),this.selection=new d.b(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.uploader=this.theme.addModule("uploader"),this.theme.init(),this.emitter.on(f.a.events.EDITOR_CHANGE,(function(e){e===f.a.events.TEXT_CHANGE&&r.root.classList.toggle("ql-blank",r.editor.isBlank())})),this.emitter.on(f.a.events.SCROLL_UPDATE,(function(e,t,n){var o=r.selection.lastRange,i=m(r.selection.getRange(),1)[0],s=o&&i?{oldRange:o,newRange:i}:void 0;A.call(r,(function(){return r.editor.update(null,t,s)}),e),null!=n&&Object.entries(n).forEach((function(e){var t=m(e,2),n=t[0],o=t[1];return r.selection.format(n,o)}))}));var s=this.clipboard.convert({html:"".concat(o,"<p><br></p>"),text:"\n"});this.setContents(s),this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable(),this.allowReadOnlyEdits=!1}var t,r,n;return t=e,r=[{key:"addContainer",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if("string"==typeof e){var r=e;(e=document.createElement("div")).classList.add(r)}return this.container.insertBefore(e,t),e}},{key:"blur",value:function(){this.selection.setRange(null)}},{key:"deleteText",value:function(e,t,r){var n=this,o=m(N(e,t,r),4);return e=o[0],t=o[1],r=o[3],A.call(this,(function(){return n.editor.deleteText(e,t)}),r,e,-1*t)}},{key:"disable",value:function(){this.enable(!1)}},{key:"editReadOnly",value:function(e){this.allowReadOnlyEdits=!0;var t=e();return this.allowReadOnlyEdits=!1,t}},{key:"enable",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.scroll.enable(e),this.container.classList.toggle("ql-disabled",!e)}},{key:"focus",value:function(){var e=this.scrollingContainer.scrollTop;this.selection.focus(),this.scrollingContainer.scrollTop=e,this.scrollIntoView()}},{key:"format",value:function(e,t){var r=this,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:f.a.sources.API;return A.call(this,(function(){var n=r.getSelection(!0),i=new o.a;if(null==n)return i;if(r.scroll.query(e,c.Scope.BLOCK))i=r.editor.formatLine(n.index,n.length,v({},e,t));else{if(0===n.length)return r.selection.format(e,t),i;i=r.editor.formatText(n.index,n.length,v({},e,t))}return r.setSelection(n,f.a.sources.SILENT),i}),n)}},{key:"formatLine",value:function(e,t,r,n,o){var i,s=this,a=m(N(e,t,r,n,o),4);return e=a[0],t=a[1],i=a[2],o=a[3],A.call(this,(function(){return s.editor.formatLine(e,t,i)}),o,e,0)}},{key:"formatText",value:function(e,t,r,n,o){var i,s=this,a=m(N(e,t,r,n,o),4);return e=a[0],t=a[1],i=a[2],o=a[3],A.call(this,(function(){return s.editor.formatText(e,t,i)}),o,e,0)}},{key:"getBounds",value:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(!(t="number"==typeof e?this.selection.getBounds(e,r):this.selection.getBounds(e.index,e.length)))return null;var n=this.container.getBoundingClientRect();return{bottom:t.bottom-n.top,height:t.height,left:t.left-n.left,right:t.right-n.left,top:t.top-n.top,width:t.width}}},{key:"getContents",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.getLength()-e,r=m(N(e,t),2);return e=r[0],t=r[1],this.editor.getContents(e,t)}},{key:"getFormat",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getSelection(!0),t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return"number"==typeof e?this.editor.getFormat(e,t):this.editor.getFormat(e.index,e.length)}},{key:"getIndex",value:function(e){return e.offset(this.scroll)}},{key:"getLength",value:function(){return this.scroll.length()}},{key:"getLeaf",value:function(e){return this.scroll.leaf(e)}},{key:"getLine",value:function(e){return this.scroll.line(e)}},{key:"getLines",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE;return"number"!=typeof e?this.scroll.lines(e.index,e.length):this.scroll.lines(e,t)}},{key:"getModule",value:function(e){return this.theme.modules[e]}},{key:"getSelection",value:function(){return arguments.length>0&&void 0!==arguments[0]&&arguments[0]&&this.focus(),this.update(),this.selection.getRange()[0]}},{key:"getSemanticHTML",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.getLength()-e,r=m(N(e,t),2);return e=r[0],t=r[1],this.editor.getHTML(e,t)}},{key:"getText",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.getLength()-e,r=m(N(e,t),2);return e=r[0],t=r[1],this.editor.getText(e,t)}},{key:"hasFocus",value:function(){return this.selection.hasFocus()}},{key:"insertEmbed",value:function(t,r,n){var o=this,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:e.sources.API;return A.call(this,(function(){return o.editor.insertEmbed(t,r,n)}),i,t)}},{key:"insertText",value:function(e,t,r,n,o){var i,s=this,a=m(N(e,0,r,n,o),4);return e=a[0],i=a[2],o=a[3],A.call(this,(function(){return s.editor.insertText(e,t,i)}),o,e,t.length)}},{key:"isEnabled",value:function(){return this.scroll.isEnabled()}},{key:"off",value:function(){var e;return(e=this.emitter).off.apply(e,arguments)}},{key:"on",value:function(){var e;return(e=this.emitter).on.apply(e,arguments)}},{key:"once",value:function(){var e;return(e=this.emitter).once.apply(e,arguments)}},{key:"removeFormat",value:function(e,t,r){var n=this,o=m(N(e,t,r),4);return e=o[0],t=o[1],r=o[3],A.call(this,(function(){return n.editor.removeFormat(e,t)}),r,e)}},{key:"scrollIntoView",value:function(){this.selection.scrollIntoView(this.scrollingContainer)}},{key:"setContents",value:function(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:f.a.sources.API;return A.call(this,(function(){e=new o.a(e);var r=t.getLength(),n=t.editor.deleteText(0,r),i=t.editor.applyDelta(e),s=t.editor.deleteText(t.getLength()-1,1);return n.compose(i).compose(s)}),r)}},{key:"setSelection",value:function(t,r,n){if(null==t)this.selection.setRange(null,r||e.sources.API);else{var o=m(N(t,r,n),4);t=o[0],r=o[1],n=o[3],this.selection.setRange(new d.a(Math.max(0,t),r),n),n!==f.a.sources.SILENT&&this.selection.scrollIntoView(this.scrollingContainer)}}},{key:"setText",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:f.a.sources.API,r=(new o.a).insert(e);return this.setContents(r,t)}},{key:"update",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f.a.sources.USER,t=this.scroll.update(e);return this.selection.update(e),t}},{key:"updateContents",value:function(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:f.a.sources.API;return A.call(this,(function(){return e=new o.a(e),t.editor.applyDelta(e,r)}),r,!0)}}],n=[{key:"debug",value:function(e){!0===e&&(e="log"),y.a.level(e)}},{key:"find",value:function(e){return p.a.get(e)||S.find(e)}},{key:"import",value:function(e){return null==this.imports[e]&&O.error("Cannot import ".concat(e,". Are you sure it was registered?")),this.imports[e]}},{key:"register",value:function(e,t){var r=this,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if("string"!=typeof e){var o=e.attrName||e.blotName;"string"==typeof o?this.register("formats/".concat(o),e,t):Object.keys(e).forEach((function(n){r.register(n,e[n],t)}))}else null==this.imports[e]||n||O.warn("Overwriting ".concat(e," with"),t),this.imports[e]=t,(e.startsWith("blots/")||e.startsWith("formats/"))&&"abstract"!==t.blotName&&S.register(t),"function"==typeof t.register&&t.register(S)}}],r&&_(t.prototype,r),n&&_(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();function T(e,t){if((t=u()({container:e,modules:{clipboard:!0,keyboard:!0,history:!0,uploader:!0}},t)).theme&&t.theme!==w.DEFAULTS.theme){if(t.theme=w.import("themes/".concat(t.theme)),null==t.theme)throw new Error("Invalid theme ".concat(t.theme,". Did you register it?"))}else t.theme=b.a;var r=s()(t.theme.DEFAULTS);[r,t].forEach((function(e){e.modules=e.modules||{},Object.keys(e.modules).forEach((function(t){!0===e.modules[t]&&(e.modules[t]={})}))}));var n=Object.keys(r.modules).concat(Object.keys(t.modules)).reduce((function(e,t){var r=w.import("modules/".concat(t));return null==r?O.error("Cannot load ".concat(t," module. Are you sure you registered it?")):e[t]=r.DEFAULTS||{},e}),{});return null!=t.modules&&t.modules.toolbar&&t.modules.toolbar.constructor!==Object&&(t.modules.toolbar={container:t.modules.toolbar}),t=u()({},w.DEFAULTS,{modules:n},r,t),["bounds","container","scrollingContainer"].forEach((function(e){"string"==typeof t[e]&&(t[e]=document.querySelector(t[e]))})),t.modules=Object.keys(t.modules).reduce((function(e,r){return t.modules[r]&&(e[r]=t.modules[r]),e}),{}),t}function A(e,t,r,n){if(!this.isEnabled()&&t===f.a.sources.USER&&!this.allowReadOnlyEdits)return new o.a;var i=null==r?null:this.getSelection(),s=this.editor.delta,a=e();if(null!=i&&(!0===r&&(r=i.index),null==n?i=P(i,a,t):0!==n&&(i=P(i,r,n,t)),this.setSelection(i,f.a.sources.SILENT)),a.length()>0){var u,c,l=[f.a.events.TEXT_CHANGE,a,s,t];(u=this.emitter).emit.apply(u,[f.a.events.EDITOR_CHANGE].concat(l)),t!==f.a.sources.SILENT&&(c=this.emitter).emit.apply(c,l)}return a}function N(e,t,r,n,o){var i={};return"number"==typeof e.index&&"number"==typeof e.length?"number"!=typeof t?(o=n,n=r,r=t,t=e.length,e=e.index):(t=e.length,e=e.index):"number"!=typeof t&&(o=n,n=r,r=t,t=0),"object"===g(r)?(i=r,o=n):"string"==typeof r&&(null!=n?i[r]=n:o=r),[e,t,i,o=o||f.a.sources.API]}function P(e,t,r,n){if(null==e)return null;var i,s;if(t instanceof o.a){var a=m([e.index,e.index+e.length].map((function(e){return t.transformPosition(e,n!==f.a.sources.USER)})),2);i=a[0],s=a[1]}else{var u=m([e.index,e.index+e.length].map((function(e){return e<t||e===t&&n===f.a.sources.USER?e:r>=0?e+r:Math.max(t,e+r)})),2);i=u[0],s=u[1]}return new d.a(i,s-i)}w.DEFAULTS={bounds:null,modules:{},placeholder:"",readOnly:!1,registry:S,scrollingContainer:null,theme:"default"},w.events=f.a.events,w.sources=f.a.sources,w.version="2.0.0-dev.46",w.imports={delta:o.a,parchment:c,"core/module":h.a,"core/theme":b.a}},function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}},o=n(r(50)),i=n(r(10)),s=n(r(18)),a=n(r(51)),u=n(r(42)),c=String.fromCharCode(0),l=function(){function e(e){Array.isArray(e)?this.ops=e:null!=e&&Array.isArray(e.ops)?this.ops=e.ops:this.ops=[]}return e.prototype.insert=function(e,t){var r={};return"string"==typeof e&&0===e.length?this:(r.insert=e,null!=t&&"object"==typeof t&&Object.keys(t).length>0&&(r.attributes=t),this.push(r))},e.prototype.delete=function(e){return e<=0?this:this.push({delete:e})},e.prototype.retain=function(e,t){if(e<=0)return this;var r={retain:e};return null!=t&&"object"==typeof t&&Object.keys(t).length>0&&(r.attributes=t),this.push(r)},e.prototype.push=function(e){var t=this.ops.length,r=this.ops[t-1];if(e=i.default(e),"object"==typeof r){if("number"==typeof e.delete&&"number"==typeof r.delete)return this.ops[t-1]={delete:r.delete+e.delete},this;if("number"==typeof r.delete&&null!=e.insert&&(t-=1,"object"!=typeof(r=this.ops[t-1])))return this.ops.unshift(e),this;if(s.default(e.attributes,r.attributes)){if("string"==typeof e.insert&&"string"==typeof r.insert)return this.ops[t-1]={insert:r.insert+e.insert},"object"==typeof e.attributes&&(this.ops[t-1].attributes=e.attributes),this;if("number"==typeof e.retain&&"number"==typeof r.retain)return this.ops[t-1]={retain:r.retain+e.retain},"object"==typeof e.attributes&&(this.ops[t-1].attributes=e.attributes),this}}return t===this.ops.length?this.ops.push(e):this.ops.splice(t,0,e),this},e.prototype.chop=function(){var e=this.ops[this.ops.length-1];return e&&e.retain&&!e.attributes&&this.ops.pop(),this},e.prototype.filter=function(e){return this.ops.filter(e)},e.prototype.forEach=function(e){this.ops.forEach(e)},e.prototype.map=function(e){return this.ops.map(e)},e.prototype.partition=function(e){var t=[],r=[];return this.forEach((function(n){(e(n)?t:r).push(n)})),[t,r]},e.prototype.reduce=function(e,t){return this.ops.reduce(e,t)},e.prototype.changeLength=function(){return this.reduce((function(e,t){return t.insert?e+u.default.length(t):t.delete?e-t.delete:e}),0)},e.prototype.length=function(){return this.reduce((function(e,t){return e+u.default.length(t)}),0)},e.prototype.slice=function(t,r){void 0===t&&(t=0),void 0===r&&(r=1/0);for(var n=[],o=u.default.iterator(this.ops),i=0;i<r&&o.hasNext();){var s=void 0;i<t?s=o.next(t-i):(s=o.next(r-i),n.push(s)),i+=u.default.length(s)}return new e(n)},e.prototype.compose=function(t){var r=u.default.iterator(this.ops),n=u.default.iterator(t.ops),o=[],i=n.peek();if(null!=i&&"number"==typeof i.retain&&null==i.attributes){for(var c=i.retain;"insert"===r.peekType()&&r.peekLength()<=c;)c-=r.peekLength(),o.push(r.next());i.retain-c>0&&n.next(i.retain-c)}for(var l=new e(o);r.hasNext()||n.hasNext();)if("insert"===n.peekType())l.push(n.next());else if("delete"===r.peekType())l.push(r.next());else{var f=Math.min(r.peekLength(),n.peekLength()),h=r.next(f),d=n.next(f);if("number"==typeof d.retain){var p={};"number"==typeof h.retain?p.retain=f:p.insert=h.insert;var y=a.default.compose(h.attributes,d.attributes,"number"==typeof h.retain);if(y&&(p.attributes=y),l.push(p),!n.hasNext()&&s.default(l.ops[l.ops.length-1],p)){var b=new e(r.rest());return l.concat(b).chop()}}else"number"==typeof d.delete&&"number"==typeof h.retain&&l.push(d)}return l.chop()},e.prototype.concat=function(t){var r=new e(this.ops.slice());return t.ops.length>0&&(r.push(t.ops[0]),r.ops=r.ops.concat(t.ops.slice(1))),r},e.prototype.diff=function(t,r){if(this.ops===t.ops)return new e;var n=[this,t].map((function(e){return e.map((function(r){if(null!=r.insert)return"string"==typeof r.insert?r.insert:c;throw new Error("diff() called "+(e===t?"on":"with")+" non-document")})).join("")})),i=new e,l=o.default(n[0],n[1],r),f=u.default.iterator(this.ops),h=u.default.iterator(t.ops);return l.forEach((function(e){for(var t=e[1].length;t>0;){var r=0;switch(e[0]){case o.default.INSERT:r=Math.min(h.peekLength(),t),i.push(h.next(r));break;case o.default.DELETE:r=Math.min(t,f.peekLength()),f.next(r),i.delete(r);break;case o.default.EQUAL:r=Math.min(f.peekLength(),h.peekLength(),t);var n=f.next(r),u=h.next(r);s.default(n.insert,u.insert)?i.retain(r,a.default.diff(n.attributes,u.attributes)):i.push(u).delete(r)}t-=r}})),i.chop()},e.prototype.eachLine=function(t,r){void 0===r&&(r="\n");for(var n=u.default.iterator(this.ops),o=new e,i=0;n.hasNext();){if("insert"!==n.peekType())return;var s=n.peek(),a=u.default.length(s)-n.peekLength(),c="string"==typeof s.insert?s.insert.indexOf(r,a)-a:-1;if(c<0)o.push(n.next());else if(c>0)o.push(n.next(c));else{if(!1===t(o,n.next(1).attributes||{},i))return;i+=1,o=new e}}o.length()>0&&t(o,{},i)},e.prototype.invert=function(t){var r=new e;return this.reduce((function(e,n){if(n.insert)r.delete(u.default.length(n));else{if(n.retain&&null==n.attributes)return r.retain(n.retain),e+n.retain;if(n.delete||n.retain&&n.attributes){var o=n.delete||n.retain;return t.slice(e,e+o).forEach((function(e){n.delete?r.push(e):n.retain&&n.attributes&&r.retain(u.default.length(e),a.default.invert(n.attributes,e.attributes))})),e+o}}return e}),0),r.chop()},e.prototype.transform=function(t,r){if(void 0===r&&(r=!1),r=!!r,"number"==typeof t)return this.transformPosition(t,r);for(var n=t,o=u.default.iterator(this.ops),i=u.default.iterator(n.ops),s=new e;o.hasNext()||i.hasNext();)if("insert"!==o.peekType()||!r&&"insert"===i.peekType())if("insert"===i.peekType())s.push(i.next());else{var c=Math.min(o.peekLength(),i.peekLength()),l=o.next(c),f=i.next(c);if(l.delete)continue;f.delete?s.push(f):s.retain(c,a.default.transform(l.attributes,f.attributes,r))}else s.retain(u.default.length(o.next()));return s.chop()},e.prototype.transformPosition=function(e,t){void 0===t&&(t=!1),t=!!t;for(var r=u.default.iterator(this.ops),n=0;r.hasNext()&&n<=e;){var o=r.peekLength(),i=r.peekType();r.next(),"delete"!==i?("insert"===i&&(n<e||!t)&&(e+=o),n+=o):e-=Math.min(o,e-n)}return e},e.Op=u.default,e.AttributeMap=a.default,e}();e.exports=l},function(e,t,r){"use strict";r.r(t),r.d(t,"Blot",(function(){return o.Blot})),r.d(t,"ParentBlot",(function(){return b})),r.d(t,"ContainerBlot",(function(){return m})),r.d(t,"LeafBlot",(function(){return O})),r.d(t,"EmbedBlot",(function(){return U})),r.d(t,"ScrollBlot",(function(){return V})),r.d(t,"BlockBlot",(function(){return D})),r.d(t,"InlineBlot",(function(){return M})),r.d(t,"TextBlot",(function(){return G})),r.d(t,"Formattable",(function(){return o.Formattable})),r.d(t,"Attributor",(function(){return S})),r.d(t,"ClassAttributor",(function(){return N})),r.d(t,"StyleAttributor",(function(){return R})),r.d(t,"AttributorStore",(function(){return C})),r.d(t,"Registry",(function(){return h})),r.d(t,"Scope",(function(){return s}));var n,o=r(41);!function(e){e[e.TYPE=3]="TYPE",e[e.LEVEL=12]="LEVEL",e[e.ATTRIBUTE=13]="ATTRIBUTE",e[e.BLOT=14]="BLOT",e[e.INLINE=7]="INLINE",e[e.BLOCK=11]="BLOCK",e[e.BLOCK_BLOT=10]="BLOCK_BLOT",e[e.INLINE_BLOT=6]="INLINE_BLOT",e[e.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",e[e.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",e[e.ANY=15]="ANY"}(n||(n={}));var i,s=n,a=function(){function e(){this.head=null,this.tail=null,this.length=0}return e.prototype.append=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];if(this.insertBefore(e[0],null),e.length>1){var r=e.slice(1);this.append.apply(this,r)}},e.prototype.at=function(e){for(var t=this.iterator(),r=t();r&&e>0;)e-=1,r=t();return r},e.prototype.contains=function(e){for(var t=this.iterator(),r=t();r;){if(r===e)return!0;r=t()}return!1},e.prototype.indexOf=function(e){for(var t=this.iterator(),r=t(),n=0;r;){if(r===e)return n;n+=1,r=t()}return-1},e.prototype.insertBefore=function(e,t){null!=e&&e!==t&&(this.remove(e),e.next=t,null!=t?(e.prev=t.prev,null!=t.prev&&(t.prev.next=e),t.prev=e,t===this.head&&(this.head=e)):null!=this.tail?(this.tail.next=e,e.prev=this.tail,this.tail=e):(e.prev=null,this.head=this.tail=e),this.length+=1)},e.prototype.offset=function(e){for(var t=0,r=this.head;null!=r;){if(r===e)return t;t+=r.length(),r=r.next}return-1},e.prototype.remove=function(e){this.contains(e)&&(null!=e.prev&&(e.prev.next=e.next),null!=e.next&&(e.next.prev=e.prev),e===this.head&&(this.head=e.next),e===this.tail&&(this.tail=e.prev),this.length-=1)},e.prototype.iterator=function(e){return void 0===e&&(e=this.head),function(){var t=e;return null!=e&&(e=e.next),t}},e.prototype.find=function(e,t){void 0===t&&(t=!1);for(var r=this.iterator(),n=r();n;){var o=n.length();if(e<o||t&&e===o&&(null==n.next||0!==n.next.length()))return[n,e];e-=o,n=r()}return[null,0]},e.prototype.forEach=function(e){for(var t=this.iterator(),r=t();r;)e(r),r=t()},e.prototype.forEachAt=function(e,t,r){if(!(t<=0))for(var n=this.find(e),o=n[0],i=e-n[1],s=this.iterator(o),a=s();a&&i<e+t;){var u=a.length();e>i?r(a,e-i,Math.min(t,i+u-e)):r(a,0,Math.min(u,e+t-i)),i+=u,a=s()}},e.prototype.map=function(e){return this.reduce((function(t,r){return t.push(e(r)),t}),[])},e.prototype.reduce=function(e,t){for(var r=this.iterator(),n=r();n;)t=e(t,n),n=r();return t},e}(),u=a,c=(i=function(e,t){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},i(e,t)},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),l=function(e){function t(t){var r=this;return t="[Parchment] "+t,(r=e.call(this,t)||this).message=t,r.name=r.constructor.name,r}return c(t,e),t}(Error),f=function(){function e(){this.attributes={},this.classes={},this.tags={},this.types={}}return e.find=function(e,t){return void 0===t&&(t=!1),null==e?null:this.blots.has(e)?this.blots.get(e)||null:t?this.find(e.parentNode,t):null},e.prototype.create=function(t,r,n){var o=this.query(r);if(null==o)throw new l("Unable to create "+r+" blot");var i=o,s=r instanceof Node||r.nodeType===Node.TEXT_NODE?r:i.create(n),a=new i(t,s,n);return e.blots.set(a.domNode,a),a},e.prototype.find=function(t,r){return void 0===r&&(r=!1),e.find(t,r)},e.prototype.query=function(e,t){var r,n=this;return void 0===t&&(t=s.ANY),"string"==typeof e?r=this.types[e]||this.attributes[e]:e instanceof Text||e.nodeType===Node.TEXT_NODE?r=this.types.text:"number"==typeof e?e&s.LEVEL&s.BLOCK?r=this.types.block:e&s.LEVEL&s.INLINE&&(r=this.types.inline):e instanceof HTMLElement&&((e.getAttribute("class")||"").split(/\s+/).some((function(e){return!!(r=n.classes[e])})),r=r||this.tags[e.tagName]),null==r?null:t&s.LEVEL&r.scope&&t&s.TYPE&r.scope?r:null},e.prototype.register=function(){for(var e=this,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];if(t.length>1)return t.map((function(t){return e.register(t)}));var n=t[0];if("string"!=typeof n.blotName&&"string"!=typeof n.attrName)throw new l("Invalid definition");if("abstract"===n.blotName)throw new l("Cannot register abstract class");return this.types[n.blotName||n.attrName]=n,"string"==typeof n.keyName?this.attributes[n.keyName]=n:(null!=n.className&&(this.classes[n.className]=n),null!=n.tagName&&(Array.isArray(n.tagName)?n.tagName=n.tagName.map((function(e){return e.toUpperCase()})):n.tagName=n.tagName.toUpperCase(),(Array.isArray(n.tagName)?n.tagName:[n.tagName]).forEach((function(t){null!=e.tags[t]&&null!=n.className||(e.tags[t]=n)})))),n},e.blots=new WeakMap,e}(),h=f,d=function(){function e(e,t){this.scroll=e,this.domNode=t,h.blots.set(t,this),this.prev=null,this.next=null}return e.create=function(e){if(null==this.tagName)throw new l("Blot definition missing tagName");var t;return Array.isArray(this.tagName)?("string"==typeof e&&(e=e.toUpperCase(),parseInt(e,10).toString()===e&&(e=parseInt(e,10))),t="number"==typeof e?document.createElement(this.tagName[e-1]):this.tagName.indexOf(e)>-1?document.createElement(e):document.createElement(this.tagName[0])):t=document.createElement(this.tagName),this.className&&t.classList.add(this.className),t},Object.defineProperty(e.prototype,"statics",{get:function(){return this.constructor},enumerable:!1,configurable:!0}),e.prototype.attach=function(){},e.prototype.clone=function(){var e=this.domNode.cloneNode(!1);return this.scroll.create(e)},e.prototype.detach=function(){null!=this.parent&&this.parent.removeChild(this),h.blots.delete(this.domNode)},e.prototype.deleteAt=function(e,t){this.isolate(e,t).remove()},e.prototype.formatAt=function(e,t,r,n){var o=this.isolate(e,t);if(null!=this.scroll.query(r,s.BLOT)&&n)o.wrap(r,n);else if(null!=this.scroll.query(r,s.ATTRIBUTE)){var i=this.scroll.create(this.statics.scope);o.wrap(i),i.format(r,n)}},e.prototype.insertAt=function(e,t,r){var n=null==r?this.scroll.create("text",t):this.scroll.create(t,r),o=this.split(e);this.parent.insertBefore(n,o||void 0)},e.prototype.isolate=function(e,t){var r=this.split(e);if(null==r)throw new Error("Attempt to isolate at end");return r.split(t),r},e.prototype.length=function(){return 1},e.prototype.offset=function(e){return void 0===e&&(e=this.parent),null==this.parent||this===e?0:this.parent.children.offset(this)+this.parent.offset(e)},e.prototype.optimize=function(e){!this.statics.requiredContainer||this.parent instanceof this.statics.requiredContainer||this.wrap(this.statics.requiredContainer.blotName)},e.prototype.remove=function(){null!=this.domNode.parentNode&&this.domNode.parentNode.removeChild(this.domNode),this.detach()},e.prototype.replaceWith=function(e,t){var r="string"==typeof e?this.scroll.create(e,t):e;return null!=this.parent&&(this.parent.insertBefore(r,this.next||void 0),this.remove()),r},e.prototype.split=function(e,t){return 0===e?this:this.next},e.prototype.update=function(e,t){},e.prototype.wrap=function(e,t){var r="string"==typeof e?this.scroll.create(e,t):e;if(null!=this.parent&&this.parent.insertBefore(r,this.next||void 0),"function"!=typeof r.appendChild)throw new l("Cannot wrap "+e);return r.appendChild(this),r},e.blotName="abstract",e}(),p=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},e(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();function y(e,t){var r=t.find(e);if(null==r)try{r=t.create(e)}catch(n){r=t.create(s.INLINE),Array.from(e.childNodes).forEach((function(e){r.domNode.appendChild(e)})),e.parentNode&&e.parentNode.replaceChild(r.domNode,e),r.attach()}return r}var b=function(e){function t(t,r){var n=e.call(this,t,r)||this;return n.uiNode=null,n.build(),n}return p(t,e),t.prototype.appendChild=function(e){this.insertBefore(e)},t.prototype.attach=function(){e.prototype.attach.call(this),this.children.forEach((function(e){e.attach()}))},t.prototype.attachUI=function(e){null!=this.uiNode&&this.uiNode.remove(),this.uiNode=e,t.uiClass&&this.uiNode.classList.add(t.uiClass),this.uiNode.setAttribute("contenteditable","false"),this.domNode.insertBefore(this.uiNode,this.domNode.firstChild)},t.prototype.build=function(){var e=this;this.children=new u,Array.from(this.domNode.childNodes).filter((function(t){return t!==e.uiNode})).reverse().forEach((function(t){try{var r=y(t,e.scroll);e.insertBefore(r,e.children.head||void 0)}catch(n){if(n instanceof l)return;throw n}}))},t.prototype.deleteAt=function(e,t){if(0===e&&t===this.length())return this.remove();this.children.forEachAt(e,t,(function(e,t,r){e.deleteAt(t,r)}))},t.prototype.descendant=function(e,r){void 0===r&&(r=0);var n=this.children.find(r),o=n[0],i=n[1];return null==e.blotName&&e(o)||null!=e.blotName&&o instanceof e?[o,i]:o instanceof t?o.descendant(e,i):[null,-1]},t.prototype.descendants=function(e,r,n){void 0===r&&(r=0),void 0===n&&(n=Number.MAX_VALUE);var o=[],i=n;return this.children.forEachAt(r,n,(function(r,n,s){(null==e.blotName&&e(r)||null!=e.blotName&&r instanceof e)&&o.push(r),r instanceof t&&(o=o.concat(r.descendants(e,n,i))),i-=s})),o},t.prototype.detach=function(){this.children.forEach((function(e){e.detach()})),e.prototype.detach.call(this)},t.prototype.enforceAllowedChildren=function(){var e=this,r=!1;this.children.forEach((function(n){r||e.statics.allowedChildren.some((function(e){return n instanceof e}))||(n.statics.scope===s.BLOCK_BLOT?(null!=n.next&&e.splitAfter(n),null!=n.prev&&e.splitAfter(n.prev),n.parent.unwrap(),r=!0):n instanceof t?n.unwrap():n.remove())}))},t.prototype.formatAt=function(e,t,r,n){this.children.forEachAt(e,t,(function(e,t,o){e.formatAt(t,o,r,n)}))},t.prototype.insertAt=function(e,t,r){var n=this.children.find(e),o=n[0],i=n[1];if(o)o.insertAt(i,t,r);else{var s=null==r?this.scroll.create("text",t):this.scroll.create(t,r);this.appendChild(s)}},t.prototype.insertBefore=function(e,t){if(e!==t){null!=e.parent&&e.parent.children.remove(e);var r=null;this.children.insertBefore(e,t||null),e.parent=this,null!=t&&(r=t.domNode),e.domNode.parentNode===this.domNode&&e.domNode.nextSibling===r||this.domNode.insertBefore(e.domNode,r),e.attach()}},t.prototype.length=function(){return this.children.reduce((function(e,t){return e+t.length()}),0)},t.prototype.moveChildren=function(e,t){this.children.forEach((function(r){e.insertBefore(r,t)}))},t.prototype.optimize=function(t){if(e.prototype.optimize.call(this,t),this.enforceAllowedChildren(),null!=this.uiNode&&this.uiNode!==this.domNode.firstChild&&this.domNode.insertBefore(this.uiNode,this.domNode.firstChild),0===this.children.length)if(null!=this.statics.defaultChild){var r=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(r)}else this.remove()},t.prototype.path=function(e,r){void 0===r&&(r=!1);var n=this.children.find(e,r),o=n[0],i=n[1],s=[[this,e]];return o instanceof t?s.concat(o.path(i,r)):(null!=o&&s.push([o,i]),s)},t.prototype.removeChild=function(e){this.children.remove(e)},t.prototype.replaceWith=function(r,n){var o="string"==typeof r?this.scroll.create(r,n):r;return o instanceof t&&this.moveChildren(o),e.prototype.replaceWith.call(this,o)},t.prototype.split=function(e,t){if(void 0===t&&(t=!1),!t){if(0===e)return this;if(e===this.length())return this.next}var r=this.clone();return this.parent&&this.parent.insertBefore(r,this.next||void 0),this.children.forEachAt(e,this.length(),(function(e,n,o){var i=e.split(n,t);null!=i&&r.appendChild(i)})),r},t.prototype.splitAfter=function(e){for(var t=this.clone();null!=e.next;)t.appendChild(e.next);return this.parent&&this.parent.insertBefore(t,this.next||void 0),t},t.prototype.unwrap=function(){this.parent&&this.moveChildren(this.parent,this.next||void 0),this.remove()},t.prototype.update=function(e,t){var r=this,n=[],o=[];e.forEach((function(e){e.target===r.domNode&&"childList"===e.type&&(n.push.apply(n,e.addedNodes),o.push.apply(o,e.removedNodes))})),o.forEach((function(e){if(null==e.parentNode||e instanceof Element&&("IFRAME"===e.tagName||null!=e.querySelector("iframe"))||!(document.body.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_CONTAINED_BY)){var t=r.scroll.find(e);null!=t&&(null!=t.domNode.parentNode&&t.domNode.parentNode!==r.domNode||t.detach())}})),n.filter((function(e){return e.parentNode===r.domNode||e===r.uiNode})).sort((function(e,t){return e===t?0:e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1})).forEach((function(e){var t=null;null!=e.nextSibling&&(t=r.scroll.find(e.nextSibling));var n=y(e,r.scroll);n.next===t&&null!=n.next||(null!=n.parent&&n.parent.removeChild(r),r.insertBefore(n,t||void 0))})),this.enforceAllowedChildren()},t.uiClass="",t}(d),g=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},e(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),v=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return g(t,e),t.prototype.checkMerge=function(){return null!==this.next&&this.next.statics.blotName===this.statics.blotName},t.prototype.deleteAt=function(t,r){e.prototype.deleteAt.call(this,t,r),this.enforceAllowedChildren()},t.prototype.formatAt=function(t,r,n,o){e.prototype.formatAt.call(this,t,r,n,o),this.enforceAllowedChildren()},t.prototype.insertAt=function(t,r,n){e.prototype.insertAt.call(this,t,r,n),this.enforceAllowedChildren()},t.prototype.optimize=function(t){e.prototype.optimize.call(this,t),this.children.length>0&&null!=this.next&&this.checkMerge()&&(this.next.moveChildren(this),this.next.remove())},t.blotName="container",t.scope=s.BLOCK_BLOT,t}(b),m=v,E=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},e(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),_=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return E(t,e),t.value=function(e){return!0},t.prototype.index=function(e,t){return this.domNode===e||this.domNode.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(t,1):-1},t.prototype.position=function(e,t){var r=Array.from(this.parent.domNode.childNodes).indexOf(this.domNode);return e>0&&(r+=1),[this.parent.domNode,r]},t.prototype.value=function(){var e;return(e={})[this.statics.blotName]=this.statics.value(this.domNode)||!0,e},t.scope=s.INLINE_BLOT,t}(d),O=_,S=function(){function e(e,t,r){void 0===r&&(r={}),this.attrName=e,this.keyName=t;var n=s.TYPE&s.ATTRIBUTE;this.scope=null!=r.scope?r.scope&s.LEVEL|n:s.ATTRIBUTE,null!=r.whitelist&&(this.whitelist=r.whitelist)}return e.keys=function(e){return Array.from(e.attributes).map((function(e){return e.name}))},e.prototype.add=function(e,t){return!!this.canAdd(e,t)&&(e.setAttribute(this.keyName,t),!0)},e.prototype.canAdd=function(e,t){return null==this.whitelist||("string"==typeof t?this.whitelist.indexOf(t.replace(/["']/g,""))>-1:this.whitelist.indexOf(t)>-1)},e.prototype.remove=function(e){e.removeAttribute(this.keyName)},e.prototype.value=function(e){var t=e.getAttribute(this.keyName);return this.canAdd(e,t)&&t?t:""},e}(),w=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},e(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();function T(e,t){return(e.getAttribute("class")||"").split(/\s+/).filter((function(e){return 0===e.indexOf(t+"-")}))}var A=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return w(t,e),t.keys=function(e){return(e.getAttribute("class")||"").split(/\s+/).map((function(e){return e.split("-").slice(0,-1).join("-")}))},t.prototype.add=function(e,t){return!!this.canAdd(e,t)&&(this.remove(e),e.classList.add(this.keyName+"-"+t),!0)},t.prototype.remove=function(e){T(e,this.keyName).forEach((function(t){e.classList.remove(t)})),0===e.classList.length&&e.removeAttribute("class")},t.prototype.value=function(e){var t=(T(e,this.keyName)[0]||"").slice(this.keyName.length+1);return this.canAdd(e,t)?t:""},t}(S),N=A,P=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},e(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),I=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return P(t,e),t.keys=function(e){return(e.getAttribute("style")||"").split(";").map((function(e){return e.split(":")[0].trim()}))},t.prototype.add=function(e,t){return!!this.canAdd(e,t)&&(e.style.setProperty(this.keyName,t),!0)},t.prototype.remove=function(e){e.style.removeProperty(this.keyName),e.getAttribute("style")||e.removeAttribute("style")},t.prototype.value=function(e){var t=e.style.getPropertyValue(this.keyName).trim();return this.canAdd(e,t)?t:""},t}(S),R=I,C=function(){function e(e){this.attributes={},this.domNode=e,this.build()}return e.prototype.attribute=function(e,t){t?e.add(this.domNode,t)&&(null!=e.value(this.domNode)?this.attributes[e.attrName]=e:delete this.attributes[e.attrName]):(e.remove(this.domNode),delete this.attributes[e.attrName])},e.prototype.build=function(){var e=this;this.attributes={};var t=h.find(this.domNode);if(null!=t){var r=S.keys(this.domNode),n=N.keys(this.domNode),o=R.keys(this.domNode);r.concat(n).concat(o).forEach((function(r){var n=t.scroll.query(r,s.ATTRIBUTE);n instanceof S&&(e.attributes[n.attrName]=n)}))}},e.prototype.copy=function(e){var t=this;Object.keys(this.attributes).forEach((function(r){var n=t.attributes[r].value(t.domNode);e.format(r,n)}))},e.prototype.move=function(e){var t=this;this.copy(e),Object.keys(this.attributes).forEach((function(e){t.attributes[e].remove(t.domNode)})),this.attributes={}},e.prototype.values=function(){var e=this;return Object.keys(this.attributes).reduce((function(t,r){return t[r]=e.attributes[r].value(e.domNode),t}),{})},e}(),j=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},e(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),M=function(e){function t(t,r){var n=e.call(this,t,r)||this;return n.attributes=new C(n.domNode),n}return j(t,e),t.formats=function(e,r){var n=r.query(t.blotName);if(null==n||e.tagName!==n.tagName)return"string"==typeof this.tagName||(Array.isArray(this.tagName)?e.tagName.toLowerCase():void 0)},t.prototype.format=function(e,r){var n=this;if(e!==this.statics.blotName||r){var o=this.scroll.query(e,s.INLINE);if(null==o)return;o instanceof S?this.attributes.attribute(o,r):!r||e===this.statics.blotName&&this.formats()[e]===r||this.replaceWith(e,r)}else this.children.forEach((function(e){e instanceof t||(e=e.wrap(t.blotName,!0)),n.attributes.copy(e)})),this.unwrap()},t.prototype.formats=function(){var e=this.attributes.values(),t=this.statics.formats(this.domNode,this.scroll);return null!=t&&(e[this.statics.blotName]=t),e},t.prototype.formatAt=function(t,r,n,o){null!=this.formats()[n]||this.scroll.query(n,s.ATTRIBUTE)?this.isolate(t,r).format(n,o):e.prototype.formatAt.call(this,t,r,n,o)},t.prototype.optimize=function(r){e.prototype.optimize.call(this,r);var n=this.formats();if(0===Object.keys(n).length)return this.unwrap();var o=this.next;o instanceof t&&o.prev===this&&function(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(var r in e)if(e[r]!==t[r])return!1;return!0}(n,o.formats())&&(o.moveChildren(this),o.remove())},t.prototype.replaceWith=function(t,r){var n=e.prototype.replaceWith.call(this,t,r);return this.attributes.copy(n),n},t.prototype.update=function(t,r){var n=this;e.prototype.update.call(this,t,r),t.some((function(e){return e.target===n.domNode&&"attributes"===e.type}))&&this.attributes.build()},t.prototype.wrap=function(r,n){var o=e.prototype.wrap.call(this,r,n);return o instanceof t&&this.attributes.move(o),o},t.allowedChildren=[t,O],t.blotName="inline",t.scope=s.INLINE_BLOT,t.tagName="SPAN",t}(b),x=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},e(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),D=function(e){function t(t,r){var n=e.call(this,t,r)||this;return n.attributes=new C(n.domNode),n}return x(t,e),t.formats=function(e,r){var n=r.query(t.blotName);if(null==n||e.tagName!==n.tagName)return"string"==typeof this.tagName||(Array.isArray(this.tagName)?e.tagName.toLowerCase():void 0)},t.prototype.format=function(e,r){var n=this.scroll.query(e,s.BLOCK);null!=n&&(n instanceof S?this.attributes.attribute(n,r):e!==this.statics.blotName||r?!r||e===this.statics.blotName&&this.formats()[e]===r||this.replaceWith(e,r):this.replaceWith(t.blotName))},t.prototype.formats=function(){var e=this.attributes.values(),t=this.statics.formats(this.domNode,this.scroll);return null!=t&&(e[this.statics.blotName]=t),e},t.prototype.formatAt=function(t,r,n,o){null!=this.scroll.query(n,s.BLOCK)?this.format(n,o):e.prototype.formatAt.call(this,t,r,n,o)},t.prototype.insertAt=function(t,r,n){if(null==n||null!=this.scroll.query(r,s.INLINE))e.prototype.insertAt.call(this,t,r,n);else{var o=this.split(t);if(null==o)throw new Error("Attempt to insertAt after block boundaries");var i=this.scroll.create(r,n);o.parent.insertBefore(i,o)}},t.prototype.replaceWith=function(t,r){var n=e.prototype.replaceWith.call(this,t,r);return this.attributes.copy(n),n},t.prototype.update=function(t,r){var n=this;e.prototype.update.call(this,t,r),t.some((function(e){return e.target===n.domNode&&"attributes"===e.type}))&&this.attributes.build()},t.blotName="block",t.scope=s.BLOCK_BLOT,t.tagName="P",t.allowedChildren=[M,t,O],t}(b),L=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},e(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),k=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return L(t,e),t.formats=function(e,t){},t.prototype.format=function(t,r){e.prototype.formatAt.call(this,0,this.length(),t,r)},t.prototype.formatAt=function(t,r,n,o){0===t&&r===this.length()?this.format(n,o):e.prototype.formatAt.call(this,t,r,n,o)},t.prototype.formats=function(){return this.statics.formats(this.domNode,this.scroll)},t}(O),U=k,F=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},e(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),B={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},q=function(e){function t(t,r){var n=e.call(this,null,r)||this;return n.registry=t,n.scroll=n,n.build(),n.observer=new MutationObserver((function(e){n.update(e)})),n.observer.observe(n.domNode,B),n.attach(),n}return F(t,e),t.prototype.create=function(e,t){return this.registry.create(this,e,t)},t.prototype.find=function(e,t){void 0===t&&(t=!1);var r=this.registry.find(e,t);return r?r.scroll===this?r:t?this.find(r.scroll.domNode.parentNode,!0):null:null},t.prototype.query=function(e,t){return void 0===t&&(t=s.ANY),this.registry.query(e,t)},t.prototype.register=function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return(e=this.registry).register.apply(e,t)},t.prototype.build=function(){null!=this.scroll&&e.prototype.build.call(this)},t.prototype.detach=function(){e.prototype.detach.call(this),this.observer.disconnect()},t.prototype.deleteAt=function(t,r){this.update(),0===t&&r===this.length()?this.children.forEach((function(e){e.remove()})):e.prototype.deleteAt.call(this,t,r)},t.prototype.formatAt=function(t,r,n,o){this.update(),e.prototype.formatAt.call(this,t,r,n,o)},t.prototype.insertAt=function(t,r,n){this.update(),e.prototype.insertAt.call(this,t,r,n)},t.prototype.optimize=function(t,r){var n=this;void 0===t&&(t=[]),void 0===r&&(r={}),e.prototype.optimize.call(this,r);for(var o=r.mutationsMap||new WeakMap,i=Array.from(this.observer.takeRecords());i.length>0;)t.push(i.pop());for(var s=function(e,t){void 0===t&&(t=!0),null!=e&&e!==n&&null!=e.domNode.parentNode&&(o.has(e.domNode)||o.set(e.domNode,[]),t&&s(e.parent))},a=function(e){o.has(e.domNode)&&(e instanceof b&&e.children.forEach(a),o.delete(e.domNode),e.optimize(r))},u=t,c=0;u.length>0;c+=1){if(c>=100)throw new Error("[Parchment] Maximum optimize iterations reached");for(u.forEach((function(e){var t=n.find(e.target,!0);null!=t&&(t.domNode===e.target&&("childList"===e.type?(s(n.find(e.previousSibling,!1)),Array.from(e.addedNodes).forEach((function(e){var t=n.find(e,!1);s(t,!1),t instanceof b&&t.children.forEach((function(e){s(e,!1)}))}))):"attributes"===e.type&&s(t.prev)),s(t))})),this.children.forEach(a),i=(u=Array.from(this.observer.takeRecords())).slice();i.length>0;)t.push(i.pop())}},t.prototype.update=function(t,r){var n=this;void 0===r&&(r={}),t=t||this.observer.takeRecords();var o=new WeakMap;t.map((function(e){var t=n.find(e.target,!0);return null==t?null:o.has(t.domNode)?(o.get(t.domNode).push(e),null):(o.set(t.domNode,[e]),t)})).forEach((function(e){null!=e&&e!==n&&o.has(e.domNode)&&e.update(o.get(e.domNode)||[],r)})),r.mutationsMap=o,o.has(this.domNode)&&e.prototype.update.call(this,o.get(this.domNode),r),this.optimize(t,r)},t.blotName="scroll",t.defaultChild=D,t.allowedChildren=[D,m],t.scope=s.BLOCK_BLOT,t.tagName="DIV",t}(b),V=q,Y=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},e(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),G=function(e){function t(t,r){var n=e.call(this,t,r)||this;return n.text=n.statics.value(n.domNode),n}return Y(t,e),t.create=function(e){return document.createTextNode(e)},t.value=function(e){return e.data},t.prototype.deleteAt=function(e,t){this.domNode.data=this.text=this.text.slice(0,e)+this.text.slice(e+t)},t.prototype.index=function(e,t){return this.domNode===e?t:-1},t.prototype.insertAt=function(t,r,n){null==n?(this.text=this.text.slice(0,t)+r+this.text.slice(t),this.domNode.data=this.text):e.prototype.insertAt.call(this,t,r,n)},t.prototype.length=function(){return this.text.length},t.prototype.optimize=function(r){e.prototype.optimize.call(this,r),this.text=this.statics.value(this.domNode),0===this.text.length?this.remove():this.next instanceof t&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())},t.prototype.position=function(e,t){return void 0===t&&(t=!1),[this.domNode,e]},t.prototype.split=function(e,t){if(void 0===t&&(t=!1),!t){if(0===e)return this;if(e===this.length())return this.next}var r=this.scroll.create(this.domNode.splitText(e));return this.parent.insertBefore(r,this.next||void 0),this.text=this.statics.value(this.domNode),r},t.prototype.update=function(e,t){var r=this;e.some((function(e){return"characterData"===e.type&&e.target===r.domNode}))&&(this.text=this.statics.value(this.domNode))},t.prototype.value=function(){return this.text},t.blotName="text",t.scope=s.INLINE_BLOT,t}(O)},function(e,t,r){"use strict";var n=r(43),o=r.n(n),i=r(25),s=r(11);function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function u(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function c(){return c="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,r){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=h(e)););return e}(e,t);if(n){var o=Object.getOwnPropertyDescriptor(n,t);return o.get?o.get.call(arguments.length<3?e:r):o.value}},c.apply(this,arguments)}function l(e,t){return l=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},l(e,t)}function f(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=h(e);if(t){var o=h(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===a(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function h(e){return h=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},h(e)}var d=Object(s.a)("quill:events");["selectionchange","mousedown","mouseup","click"].forEach((function(e){document.addEventListener(e,(function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];Array.from(document.querySelectorAll(".ql-container")).forEach((function(e){var r,n=i.a.get(e);n&&n.emitter&&(r=n.emitter).handleDOM.apply(r,t)}))}))}));var p=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&l(e,t)}(i,e);var t,r,n,o=f(i);function i(){var e;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),(e=o.call(this)).listeners={},e.on("error",d.error),e}return t=i,r=[{key:"emit",value:function(){for(var e,t,r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];(e=d.log).call.apply(e,[d].concat(n)),(t=c(h(i.prototype),"emit",this)).call.apply(t,[this].concat(n))}},{key:"handleDOM",value:function(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];(this.listeners[e.type]||[]).forEach((function(t){var n=t.node,o=t.handler;(e.target===n||n.contains(e.target))&&o.apply(void 0,[e].concat(r))}))}},{key:"listenDOM",value:function(e,t,r){this.listeners[e]||(this.listeners[e]=[]),this.listeners[e].push({node:t,handler:r})}}],r&&u(t.prototype,r),n&&u(t,n),Object.defineProperty(t,"prototype",{writable:!1}),i}(o.a);p.events={EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_BLOT_MOUNT:"scroll-blot-mount",SCROLL_BLOT_UNMOUNT:"scroll-blot-unmount",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change"},p.sources={API:"api",SILENT:"silent",USER:"user"},t.a=p},function(e,t,r){"use strict";r.d(t,"b",(function(){return S})),r.d(t,"c",(function(){return w})),r.d(t,"a",(function(){return O})),r.d(t,"d",(function(){return _}));var n=r(1),o=r.n(n),i=r(2),s=r(8),a=r(12),u=r(7);function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach((function(t){h(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function h(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function d(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function p(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function y(e,t,r){return t&&p(e.prototype,t),r&&p(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function b(){return b="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,r){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=E(e)););return e}(e,t);if(n){var o=Object.getOwnPropertyDescriptor(n,t);return o.get?o.get.call(arguments.length<3?e:r):o.value}},b.apply(this,arguments)}function g(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&v(e,t)}function v(e,t){return v=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},v(e,t)}function m(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=E(e);if(t){var o=E(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===c(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function E(e){return E=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},E(e)}var _=function(e){g(r,e);var t=m(r);function r(e,n){var o;return d(this,r),(o=t.call(this,e,n)).cache={},o}return y(r,[{key:"delta",value:function(){return null==this.cache.delta&&(this.cache.delta=S(this)),this.cache.delta}},{key:"deleteAt",value:function(e,t){b(E(r.prototype),"deleteAt",this).call(this,e,t),this.cache={}}},{key:"formatAt",value:function(e,t,n,o){t<=0||(this.scroll.query(n,i.Scope.BLOCK)?e+t===this.length()&&this.format(n,o):b(E(r.prototype),"formatAt",this).call(this,e,Math.min(t,this.length()-e-1),n,o),this.cache={})}},{key:"insertAt",value:function(e,t,n){if(null!=n)return b(E(r.prototype),"insertAt",this).call(this,e,t,n),void(this.cache={});if(0!==t.length){var o=t.split("\n"),i=o.shift();i.length>0&&(e<this.length()-1||null==this.children.tail?b(E(r.prototype),"insertAt",this).call(this,Math.min(e,this.length()-1),i):this.children.tail.insertAt(this.children.tail.length(),i),this.cache={});var s=this;o.reduce((function(e,t){return(s=s.split(e,!0)).insertAt(0,t),t.length}),e+i.length)}}},{key:"insertBefore",value:function(e,t){var n=this.children.head;b(E(r.prototype),"insertBefore",this).call(this,e,t),n instanceof s.a&&n.remove(),this.cache={}}},{key:"length",value:function(){return null==this.cache.length&&(this.cache.length=b(E(r.prototype),"length",this).call(this)+1),this.cache.length}},{key:"moveChildren",value:function(e,t){b(E(r.prototype),"moveChildren",this).call(this,e,t),this.cache={}}},{key:"optimize",value:function(e){b(E(r.prototype),"optimize",this).call(this,e),this.cache={}}},{key:"path",value:function(e){return b(E(r.prototype),"path",this).call(this,e,!0)}},{key:"removeChild",value:function(e){b(E(r.prototype),"removeChild",this).call(this,e),this.cache={}}},{key:"split",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(t&&(0===e||e>=this.length()-1)){var n=this.clone();return 0===e?(this.parent.insertBefore(n,this),this):(this.parent.insertBefore(n,this.next),n)}var o=b(E(r.prototype),"split",this).call(this,e,t);return this.cache={},o}}]),r}(i.BlockBlot);_.blotName="block",_.tagName="P",_.defaultChild=s.a,_.allowedChildren=[s.a,a.a,i.EmbedBlot,u.a];var O=function(e){g(r,e);var t=m(r);function r(){return d(this,r),t.apply(this,arguments)}return y(r,[{key:"attach",value:function(){b(E(r.prototype),"attach",this).call(this),this.attributes=new i.AttributorStore(this.domNode)}},{key:"delta",value:function(){return(new o.a).insert(this.value(),f(f({},this.formats()),this.attributes.values()))}},{key:"format",value:function(e,t){var r=this.scroll.query(e,i.Scope.BLOCK_ATTRIBUTE);null!=r&&this.attributes.attribute(r,t)}},{key:"formatAt",value:function(e,t,r,n){this.format(r,n)}},{key:"insertAt",value:function(e,t,n){if("string"==typeof t&&t.endsWith("\n")){var o=this.scroll.create(_.blotName);this.parent.insertBefore(o,0===e?this:this.next),o.insertAt(0,t.slice(0,-1))}else b(E(r.prototype),"insertAt",this).call(this,e,t,n)}}]),r}(i.EmbedBlot);function S(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return e.descendants(i.LeafBlot).reduce((function(e,r){return 0===r.length()?e:e.insert(r.value(),w(r,{},t))}),new o.a).insert("\n",w(e))}function w(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return null==e?t:("function"==typeof e.formats&&(t=f(f({},t),e.formats()),r&&delete t["code-token"]),null==e.parent||"scroll"===e.parent.statics.blotName||e.parent.statics.scope!==e.statics.scope?t:w(e.parent,t,r))}O.scope=i.Scope.BLOCK_BLOT},function(e,t,r){"use strict";var n=r(10),o=r.n(n),i=r(18),s=r.n(i),a=r(17),u=r.n(a),c=r(1),l=r.n(c),f=r(2),h=r(6),d=r(16),p=r(4),y=r(8),b=r(7);function g(e){return A(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||w(e)||S()}function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(Object(r),!0).forEach((function(t){E(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function E(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _(e){return _="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_(e)}function O(e,t){return A(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i=[],s=!0,a=!1;try{for(r=r.call(e);!(s=(n=r.next()).done)&&(i.push(n.value),!t||i.length!==t);s=!0);}catch(u){a=!0,o=u}finally{try{s||null==r.return||r.return()}finally{if(a)throw o}}return i}}(e,t)||w(e,t)||S()}function S(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function w(e,t){if(e){if("string"==typeof e)return T(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?T(e,t):void 0}}function T(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function A(e){if(Array.isArray(e))return e}function N(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var P=/^[ -~]*$/,I=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.scroll=t,this.delta=this.getDelta()}var t,r,n;return t=e,r=[{key:"applyDelta",value:function(e){var t=this;this.scroll.update();var r=this.scroll.length();this.scroll.batchStart();var n=function(e){return e.reduce((function(e,t){if("string"==typeof t.insert){var r=t.insert.replace(/\r\n/g,"\n").replace(/\r/g,"\n");return e.insert(r,t.attributes)}return e.push(t)}),new l.a)}(e),o=new l.a;return n.reduce((function(e,n){var i=c.Op.length(n),s=n.attributes||{},a=!1;if(null!=n.insert){if(o.retain(i),"string"==typeof n.insert){var l=n.insert;a=!l.endsWith("\n")&&(r<=e||t.scroll.descendant(p.a,e)[0]),t.scroll.insertAt(e,l);var h=O(t.scroll.line(e),2),d=h[0],y=h[1],b=u()({},Object(p.c)(d));if(d instanceof p.d){var g=O(d.descendant(f.LeafBlot,y),1)[0];b=u()(b,Object(p.c)(g))}s=c.AttributeMap.diff(b,s)||{}}else if("object"===_(n.insert)){var v=Object.keys(n.insert)[0];if(null==v)return e;a=null!=t.scroll.query(v,f.Scope.INLINE)&&(r<=e||t.scroll.descendant(p.a,e)[0]),t.scroll.insertAt(e,v,n.insert[v])}r+=i}else o.push(n);Object.keys(s).forEach((function(r){t.scroll.formatAt(e,i,r,s[r])}));var m=a?1:0;return r+=m,o.delete(m),e+i+m}),0),o.reduce((function(e,r){return"number"==typeof r.delete?(t.scroll.deleteAt(e,r.delete),e):e+c.Op.length(r)}),0),this.scroll.batchEnd(),this.scroll.optimize(),this.update(n)}},{key:"deleteText",value:function(e,t){return this.scroll.deleteAt(e,t),this.update((new l.a).retain(e).delete(t))}},{key:"formatLine",value:function(e,t){var r=this,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.scroll.update(),Object.keys(n).forEach((function(o){r.scroll.lines(e,Math.max(t,1)).forEach((function(e){e.format(o,n[o])}))})),this.scroll.optimize();var i=(new l.a).retain(e).retain(t,o()(n));return this.update(i)}},{key:"formatText",value:function(e,t){var r=this,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};Object.keys(n).forEach((function(o){r.scroll.formatAt(e,t,o,n[o])}));var i=(new l.a).retain(e).retain(t,o()(n));return this.update(i)}},{key:"getContents",value:function(e,t){return this.delta.slice(e,e+t)}},{key:"getDelta",value:function(){return this.scroll.lines().reduce((function(e,t){return e.concat(t.delta())}),new l.a)}},{key:"getFormat",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=[],n=[];0===t?this.scroll.path(e).forEach((function(e){var t=O(e,1)[0];t instanceof p.d?r.push(t):t instanceof f.LeafBlot&&n.push(t)})):(r=this.scroll.lines(e,t),n=this.scroll.descendants(f.LeafBlot,e,t));var o=O([r,n].map((function(e){if(0===e.length)return{};for(var t=Object(p.c)(e.shift());Object.keys(t).length>0;){var r=e.shift();if(null==r)return t;t=j(Object(p.c)(r),t)}return t})),2);return r=o[0],n=o[1],m(m({},r),n)}},{key:"getHTML",value:function(e,t){var r=O(this.scroll.line(e),2),n=r[0],o=r[1];return n.length()>=o+t?C(n,o,t,!0):C(this.scroll,e,t,!0)}},{key:"getText",value:function(e,t){return this.getContents(e,t).filter((function(e){return"string"==typeof e.insert})).map((function(e){return e.insert})).join("")}},{key:"insertEmbed",value:function(e,t,r){return this.scroll.insertAt(e,t,r),this.update((new l.a).retain(e).insert(E({},t,r)))}},{key:"insertText",value:function(e,t){var r=this,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return t=t.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),this.scroll.insertAt(e,t),Object.keys(n).forEach((function(o){r.scroll.formatAt(e,t.length,o,n[o])})),this.update((new l.a).retain(e).insert(t,o()(n)))}},{key:"isBlank",value:function(){if(0===this.scroll.children.length)return!0;if(this.scroll.children.length>1)return!1;var e=this.scroll.children.head;return e.statics.blotName===p.d.blotName&&!(e.children.length>1)&&e.children.head instanceof y.a}},{key:"removeFormat",value:function(e,t){var r=this.getText(e,t),n=O(this.scroll.line(e+t),2),o=n[0],i=n[1],s=0,a=new l.a;null!=o&&(s=o.length()-i,a=o.delta().slice(i,i+s-1).insert("\n"));var u=this.getContents(e,t+s).diff((new l.a).insert(r).concat(a)),c=(new l.a).retain(e).concat(u);return this.applyDelta(c)}},{key:"update",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0,n=this.delta;if(1===t.length&&"characterData"===t[0].type&&t[0].target.data.match(P)&&this.scroll.find(t[0].target)){var o=this.scroll.find(t[0].target),i=Object(p.c)(o),a=o.offset(this.scroll),u=t[0].oldValue.replace(d.a.CONTENTS,""),c=(new l.a).insert(u),f=(new l.a).insert(o.value()),h=r&&{oldRange:x(r.oldRange,-a),newRange:x(r.newRange,-a)};e=(new l.a).retain(a).concat(c.diff(f,h)).reduce((function(e,t){return t.insert?e.insert(t.insert,i):e.push(t)}),new l.a),this.delta=n.compose(e)}else this.delta=this.getDelta(),e&&s()(n.compose(e),this.delta)||(e=n.diff(this.delta,r));return e}}],r&&N(t.prototype,r),n&&N(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();function R(e,t,r){if(0===e.length){var n=O(M(r.pop()),1)[0];return t<=0?"</li></".concat(n,">"):"</li></".concat(n,">").concat(R([],t-1,r))}var o=g(e),i=o[0],s=i.child,a=i.offset,u=i.length,c=i.indent,l=i.type,f=o.slice(1),h=O(M(l),2),d=h[0],p=h[1];if(c>t)return r.push(l),c===t+1?"<".concat(d,"><li").concat(p,">").concat(C(s,a,u)).concat(R(f,c,r)):"<".concat(d,"><li>").concat(R(e,t+1,r));var y=r[r.length-1];if(c===t&&l===y)return"</li><li".concat(p,">").concat(C(s,a,u)).concat(R(f,c,r));var b=O(M(r.pop()),1)[0];return"</li></".concat(b,">").concat(R(e,t-1,r))}function C(e,t,r){var n=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if("function"==typeof e.html)return e.html(t,r);if(e instanceof b.a)return Object(b.b)(e.value().slice(t,t+r));if(e.children){if("list-container"===e.statics.blotName){var o=[];return e.children.forEachAt(t,r,(function(e,t,r){var n=e.formats();o.push({child:e,offset:t,length:r,indent:n.indent||(n["list-level"]?n["list-level"]-1:0),type:n.list})})),R(o,-1,[])}var i=[];if(e.children.forEachAt(t,r,(function(e,t,r){i.push(C(e,t,r))})),n||"list"===e.statics.blotName)return i.join("");var s=e.domNode,a=s.outerHTML,u=s.innerHTML,c=O(a.split(">".concat(u,"<")),2),l=c[0],f=c[1];return"<table"===l?'<table style="border: 1px solid #000;">'.concat(i.join(""),"<").concat(f):"".concat(l,">").concat(i.join(""),"<").concat(f)}return e.domNode.outerHTML}function j(e,t){return Object.keys(t).reduce((function(r,n){return null==e[n]||(t[n]===e[n]?r[n]=t[n]:Array.isArray(t[n])?t[n].indexOf(e[n])<0&&(r[n]=t[n].concat([e[n]])):r[n]=[t[n],e[n]]),r}),{})}function M(e){var t="ordered"===e?"ol":"ul";switch(e){case"checked":return[t,' data-list="checked"'];case"unchecked":return[t,' data-list="unchecked"'];default:return[t,""]}}function x(e,t){var r=e.index,n=e.length;return new h.a(r+t,n)}t.a=I},function(e,t,r){"use strict";r.d(t,"a",(function(){return v})),r.d(t,"b",(function(){return m}));var n=r(2),o=r(10),i=r.n(o),s=r(18),a=r.n(s),u=r(3),c=r(11);function l(e){return function(e){if(Array.isArray(e))return d(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||h(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i=[],s=!0,a=!1;try{for(r=r.call(e);!(s=(n=r.next()).done)&&(i.push(n.value),!t||i.length!==t);s=!0);}catch(u){a=!0,o=u}finally{try{s||null==r.return||r.return()}finally{if(a)throw o}}return i}}(e,t)||h(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(e,t){if(e){if("string"==typeof e)return d(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?d(e,t):void 0}}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function p(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function y(e,t,r){return t&&p(e.prototype,t),r&&p(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function b(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var g=Object(c.a)("quill:selection"),v=y((function e(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;b(this,e),this.index=t,this.length=r})),m=function(){function e(t,r){var n=this;b(this,e),this.emitter=r,this.scroll=t,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=this.scroll.create("cursor",this),this.savedRange=new v(0,0),this.lastRange=this.savedRange,this.lastNative=null,this.handleComposition(),this.handleDragging(),this.emitter.listenDOM("selectionchange",document,(function(){n.mouseDown||setTimeout(n.update.bind(n,u.a.sources.USER),1)})),this.emitter.on(u.a.events.SCROLL_BEFORE_UPDATE,(function(){if(n.hasFocus()){var e=n.getNativeRange();null!=e&&e.start.node!==n.cursor.textNode&&n.emitter.once(u.a.events.SCROLL_UPDATE,(function(){try{n.root.contains(e.start.node)&&n.root.contains(e.end.node)&&n.setNativeRange(e.start.node,e.start.offset,e.end.node,e.end.offset),n.update(u.a.sources.SILENT)}catch(t){}}))}})),this.emitter.on(u.a.events.SCROLL_OPTIMIZE,(function(e,t){if(t.range){var r=t.range,o=r.startNode,i=r.startOffset,s=r.endNode,a=r.endOffset;n.setNativeRange(o,i,s,a),n.update(u.a.sources.SILENT)}})),this.update(u.a.sources.SILENT)}return y(e,[{key:"handleComposition",value:function(){var e=this;this.root.addEventListener("compositionstart",(function(){e.composing=!0})),this.root.addEventListener("compositionend",(function(){if(e.composing=!1,e.cursor.parent){var t=e.cursor.restore();if(!t)return;setTimeout((function(){e.setNativeRange(t.startNode,t.startOffset,t.endNode,t.endOffset)}),1)}}))}},{key:"handleDragging",value:function(){var e=this;this.emitter.listenDOM("mousedown",document.body,(function(){e.mouseDown=!0})),this.emitter.listenDOM("mouseup",document.body,(function(){e.mouseDown=!1,e.update(u.a.sources.USER)}))}},{key:"focus",value:function(){this.hasFocus()||(this.root.focus(),this.setRange(this.savedRange))}},{key:"format",value:function(e,t){this.scroll.update();var r=this.getNativeRange();if(null!=r&&r.native.collapsed&&!this.scroll.query(e,n.Scope.BLOCK)){if(r.start.node!==this.cursor.textNode){var o=this.scroll.find(r.start.node,!1);if(null==o)return;if(o instanceof n.LeafBlot){var i=o.split(r.start.offset);o.parent.insertBefore(this.cursor,i)}else o.insertBefore(this.cursor,r.start.node);this.cursor.attach()}this.cursor.format(e,t),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}},{key:"getBounds",value:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=this.scroll.length();e=Math.min(e,n-1),r=Math.min(e+r,n-1)-e;var o=f(this.scroll.leaf(e),2),i=o[0],s=o[1];if(null==i)return null;var a=f(i.position(s,!0),2);t=a[0],s=a[1];var u=document.createRange();if(r>0){u.setStart(t,s);var c=f(this.scroll.leaf(e+r),2);if(i=c[0],s=c[1],null==i)return null;var l=f(i.position(s,!0),2);return t=l[0],s=l[1],u.setEnd(t,s),u.getBoundingClientRect()}var h,d="left";if(t instanceof Text){if(!t.data.length)return null;s<t.data.length?(u.setStart(t,s),u.setEnd(t,s+1)):(u.setStart(t,s-1),u.setEnd(t,s),d="right"),h=u.getBoundingClientRect()}else h=i.domNode.getBoundingClientRect(),s>0&&(d="right");return{bottom:h.top+h.height,height:h.height,left:h[d],right:h[d],top:h.top,width:0}}},{key:"getNativeRange",value:function(){var e,t=document.getSelection();if(null==t||t.rangeCount<=0)return null;try{e=t.getRangeAt(0)}catch(n){return null}if(null==e)return null;var r=this.normalizeNative(e);return g.info("getNativeRange",r),r}},{key:"getRange",value:function(){var e=this.scroll.domNode;if("isConnected"in e&&!e.isConnected)return[null,null];var t=this.getNativeRange();return null==t?[null,null]:[this.normalizedToRange(t),t]}},{key:"hasFocus",value:function(){return document.activeElement===this.root||E(this.scroll,document.activeElement)}},{key:"normalizedToRange",value:function(e){var t=this,r=[[e.start.node,e.start.offset]];e.native.collapsed||r.push([e.end.node,e.end.offset]);var o=r.map((function(e){var r=f(e,2),o=r[0],i=r[1],s=t.scroll.find(o,!0),a=s.offset(t.scroll);return 0===i?a:s instanceof n.ContainerBlot?a+s.length():a+s.index(o,i)})),i=Math.min(Math.max.apply(Math,l(o)),this.scroll.length()-1),s=Math.min.apply(Math,[i].concat(l(o)));return new v(s,i-s)}},{key:"normalizeNative",value:function(e){if(!E(this.scroll,e.startContainer)||!e.collapsed&&!E(this.scroll,e.endContainer))return null;var t={start:{node:e.startContainer,offset:e.startOffset},end:{node:e.endContainer,offset:e.endOffset},native:e};return[t.start,t.end].forEach((function(e){for(var t=e.node,r=e.offset;!(t instanceof Text)&&t.childNodes.length>0;)if(t.childNodes.length>r)t=t.childNodes[r],r=0;else{if(t.childNodes.length!==r)break;r=(t=t.lastChild)instanceof Text?t.data.length:t.childNodes.length>0?t.childNodes.length:t.childNodes.length+1}e.node=t,e.offset=r})),t}},{key:"rangeToNative",value:function(e){var t=this,r=e.collapsed?[e.index]:[e.index,e.index+e.length],n=[],o=this.scroll.length();return r.forEach((function(e,r){e=Math.min(o-1,e);var i=f(t.scroll.leaf(e),2),s=i[0],a=i[1],u=f(s.position(a,0!==r),2),c=u[0],l=u[1];n.push(c,l)})),n.length<2?n.concat(n):n}},{key:"scrollIntoView",value:function(e){var t=this.lastRange;if(null!=t){var r=this.getBounds(t.index,t.length);if(null!=r){var n=this.scroll.length()-1,o=f(this.scroll.line(Math.min(t.index,n)),1)[0],i=o;if(t.length>0&&(i=f(this.scroll.line(Math.min(t.index+t.length,n)),1)[0]),null!=o&&null!=i){var s=e.getBoundingClientRect();r.top<s.top?e.scrollTop-=s.top-r.top:r.bottom>s.bottom&&(e.scrollTop+=r.bottom-s.bottom)}}}}},{key:"setNativeRange",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:t,o=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(g.info("setNativeRange",e,t,r,n),null==e||null!=this.root.parentNode&&null!=e.parentNode&&null!=r.parentNode){var i=document.getSelection();if(null!=i)if(null!=e){this.hasFocus()||this.root.focus();var s=(this.getNativeRange()||{}).native;if(null==s||o||e!==s.startContainer||t!==s.startOffset||r!==s.endContainer||n!==s.endOffset){"BR"===e.tagName&&(t=Array.from(e.parentNode.childNodes).indexOf(e),e=e.parentNode),"BR"===r.tagName&&(n=Array.from(r.parentNode.childNodes).indexOf(r),r=r.parentNode);var a=document.createRange();a.setStart(e,t),a.setEnd(r,n),i.removeAllRanges(),i.addRange(a)}}else i.removeAllRanges(),this.root.blur()}}},{key:"setRange",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:u.a.sources.API;if("string"==typeof t&&(r=t,t=!1),g.info("setRange",e),null!=e){var n=this.rangeToNative(e);this.setNativeRange.apply(this,l(n).concat([t]))}else this.setNativeRange(null);this.update(r)}},{key:"update",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:u.a.sources.USER,t=this.lastRange,r=f(this.getRange(),2),n=r[0],o=r[1];if(this.lastRange=n,this.lastNative=o,null!=this.lastRange&&(this.savedRange=this.lastRange),!a()(t,this.lastRange)){var s;if(!this.composing&&null!=o&&o.native.collapsed&&o.start.node!==this.cursor.textNode){var c=this.cursor.restore();c&&this.setNativeRange(c.startNode,c.startOffset,c.endNode,c.endOffset)}var l,h=[u.a.events.SELECTION_CHANGE,i()(this.lastRange),i()(t),e];(s=this.emitter).emit.apply(s,[u.a.events.EDITOR_CHANGE].concat(h)),e!==u.a.sources.SILENT&&(l=this.emitter).emit.apply(l,h)}}}]),e}();function E(e,t){try{t.parentNode}catch(n){return!1}if(!e.domNode.contains(t))return!1;var r=e.registry.find(t,!0);return r&&r.scroll===e}},function(e,t,r){"use strict";function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function i(e,t){return i=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},i(e,t)}function s(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,o=a(e);if(t){var i=a(this).constructor;r=Reflect.construct(o,arguments,i)}else r=o.apply(this,arguments);return function(e,t){if(t&&("object"===n(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function a(e){return a=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},a(e)}r.d(t,"a",(function(){return u})),r.d(t,"b",(function(){return c}));var u=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&i(e,t)}(u,e);var t,r,n,a=s(u);function u(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u),a.apply(this,arguments)}return t=u,r&&o(t.prototype,r),n&&o(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}(r(2).TextBlot);function c(e){return e.replace(/[&<>"']/g,(function(e){return{"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"}[e]}))}},function(e,t,r){"use strict";function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function i(e,t){return i=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},i(e,t)}function s(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,o=a(e);if(t){var i=a(this).constructor;r=Reflect.construct(o,arguments,i)}else r=o.apply(this,arguments);return function(e,t){if(t&&("object"===n(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function a(e){return a=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},a(e)}var u=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&i(e,t)}(u,e);var t,r,n,a=s(u);function u(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u),a.apply(this,arguments)}return t=u,n=[{key:"value",value:function(){}}],(r=[{key:"optimize",value:function(){(this.prev||this.next)&&this.remove()}},{key:"length",value:function(){return 0}},{key:"value",value:function(){return""}}])&&o(t.prototype,r),n&&o(t,n),Object.defineProperty(t,"prototype",{writable:!1}),u}(r(2).EmbedBlot);u.blotName="break",u.tagName="BR",t.a=u},function(e,t,r){"use strict";function n(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function o(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}var i=o((function e(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.quill=t,this.options=r}));i.DEFAULTS={},t.a=i},function(e,t,r){(function(e,r){var n="__lodash_hash_undefined__",o=9007199254740991,i="[object Arguments]",s="[object Boolean]",a="[object Date]",u="[object Function]",c="[object GeneratorFunction]",l="[object Map]",f="[object Number]",h="[object Object]",d="[object Promise]",p="[object RegExp]",y="[object Set]",b="[object String]",g="[object Symbol]",v="[object WeakMap]",m="[object ArrayBuffer]",E="[object DataView]",_="[object Float32Array]",O="[object Float64Array]",S="[object Int8Array]",w="[object Int16Array]",T="[object Int32Array]",A="[object Uint8Array]",N="[object Uint8ClampedArray]",P="[object Uint16Array]",I="[object Uint32Array]",R=/\w*$/,C=/^\[object .+?Constructor\]$/,j=/^(?:0|[1-9]\d*)$/,M={};M[i]=M["[object Array]"]=M[m]=M[E]=M[s]=M[a]=M[_]=M[O]=M[S]=M[w]=M[T]=M[l]=M[f]=M[h]=M[p]=M[y]=M[b]=M[g]=M[A]=M[N]=M[P]=M[I]=!0,M["[object Error]"]=M[u]=M[v]=!1;var x="object"==typeof e&&e&&e.Object===Object&&e,D="object"==typeof self&&self&&self.Object===Object&&self,L=x||D||Function("return this")(),k=t&&!t.nodeType&&t,U=k&&"object"==typeof r&&r&&!r.nodeType&&r,F=U&&U.exports===k;function B(e,t){return e.set(t[0],t[1]),e}function q(e,t){return e.add(t),e}function V(e,t,r,n){var o=-1,i=e?e.length:0;for(n&&i&&(r=e[++o]);++o<i;)r=t(r,e[o],o,e);return r}function Y(e){var t=!1;if(null!=e&&"function"!=typeof e.toString)try{t=!!(e+"")}catch(r){}return t}function G(e){var t=-1,r=Array(e.size);return e.forEach((function(e,n){r[++t]=[n,e]})),r}function H(e,t){return function(r){return e(t(r))}}function z(e){var t=-1,r=Array(e.size);return e.forEach((function(e){r[++t]=e})),r}var K,X=Array.prototype,$=Function.prototype,W=Object.prototype,Q=L["__core-js_shared__"],J=(K=/[^.]+$/.exec(Q&&Q.keys&&Q.keys.IE_PROTO||""))?"Symbol(src)_1."+K:"",Z=$.toString,ee=W.hasOwnProperty,te=W.toString,re=RegExp("^"+Z.call(ee).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),ne=F?L.Buffer:void 0,oe=L.Symbol,ie=L.Uint8Array,se=H(Object.getPrototypeOf,Object),ae=Object.create,ue=W.propertyIsEnumerable,ce=X.splice,le=Object.getOwnPropertySymbols,fe=ne?ne.isBuffer:void 0,he=H(Object.keys,Object),de=Ue(L,"DataView"),pe=Ue(L,"Map"),ye=Ue(L,"Promise"),be=Ue(L,"Set"),ge=Ue(L,"WeakMap"),ve=Ue(Object,"create"),me=Ye(de),Ee=Ye(pe),_e=Ye(ye),Oe=Ye(be),Se=Ye(ge),we=oe?oe.prototype:void 0,Te=we?we.valueOf:void 0;function Ae(e){var t=-1,r=e?e.length:0;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}function Ne(e){var t=-1,r=e?e.length:0;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}function Pe(e){var t=-1,r=e?e.length:0;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}function Ie(e){this.__data__=new Ne(e)}function Re(e,t){var r=He(e)||function(e){return function(e){return function(e){return!!e&&"object"==typeof e}(e)&&ze(e)}(e)&&ee.call(e,"callee")&&(!ue.call(e,"callee")||te.call(e)==i)}(e)?function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}(e.length,String):[],n=r.length,o=!!n;for(var s in e)!t&&!ee.call(e,s)||o&&("length"==s||qe(s,n))||r.push(s);return r}function Ce(e,t,r){var n=e[t];ee.call(e,t)&&Ge(n,r)&&(void 0!==r||t in e)||(e[t]=r)}function je(e,t){for(var r=e.length;r--;)if(Ge(e[r][0],t))return r;return-1}function Me(e,t,r,n,o,d,v){var C;if(n&&(C=d?n(e,o,d,v):n(e)),void 0!==C)return C;if(!$e(e))return e;var j=He(e);if(j){if(C=function(e){var t=e.length,r=e.constructor(t);return t&&"string"==typeof e[0]&&ee.call(e,"index")&&(r.index=e.index,r.input=e.input),r}(e),!t)return function(e,t){var r=-1,n=e.length;for(t||(t=Array(n));++r<n;)t[r]=e[r];return t}(e,C)}else{var x=Be(e),D=x==u||x==c;if(Ke(e))return function(e,t){if(t)return e.slice();var r=new e.constructor(e.length);return e.copy(r),r}(e,t);if(x==h||x==i||D&&!d){if(Y(e))return d?e:{};if(C=function(e){return"function"!=typeof e.constructor||Ve(e)?{}:$e(t=se(e))?ae(t):{};var t}(D?{}:e),!t)return function(e,t){return Le(e,Fe(e),t)}(e,function(e,t){return e&&Le(t,We(t),e)}(C,e))}else{if(!M[x])return d?e:{};C=function(e,t,r,n){var o,i=e.constructor;switch(t){case m:return De(e);case s:case a:return new i(+e);case E:return function(e,t){var r=t?De(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.byteLength)}(e,n);case _:case O:case S:case w:case T:case A:case N:case P:case I:return function(e,t){var r=t?De(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.length)}(e,n);case l:return function(e,t,r){var n=t?r(G(e),!0):G(e);return V(n,B,new e.constructor)}(e,n,r);case f:case b:return new i(e);case p:return function(e){var t=new e.constructor(e.source,R.exec(e));return t.lastIndex=e.lastIndex,t}(e);case y:return function(e,t,r){var n=t?r(z(e),!0):z(e);return V(n,q,new e.constructor)}(e,n,r);case g:return o=e,Te?Object(Te.call(o)):{}}}(e,x,Me,t)}}v||(v=new Ie);var L=v.get(e);if(L)return L;if(v.set(e,C),!j)var k=r?function(e){return function(e,t,r){var n=t(e);return He(e)?n:function(e,t){for(var r=-1,n=t.length,o=e.length;++r<n;)e[o+r]=t[r];return e}(n,r(e))}(e,We,Fe)}(e):We(e);return function(e,t){for(var r=-1,n=e?e.length:0;++r<n&&!1!==t(e[r],r,e););}(k||e,(function(o,i){k&&(o=e[i=o]),Ce(C,i,Me(o,t,r,n,i,e,v))})),C}function xe(e){return!(!$e(e)||(t=e,J&&J in t))&&(Xe(e)||Y(e)?re:C).test(Ye(e));var t}function De(e){var t=new e.constructor(e.byteLength);return new ie(t).set(new ie(e)),t}function Le(e,t,r,n){r||(r={});for(var o=-1,i=t.length;++o<i;){var s=t[o],a=n?n(r[s],e[s],s,r,e):void 0;Ce(r,s,void 0===a?e[s]:a)}return r}function ke(e,t){var r,n,o=e.__data__;return("string"==(n=typeof(r=t))||"number"==n||"symbol"==n||"boolean"==n?"__proto__"!==r:null===r)?o["string"==typeof t?"string":"hash"]:o.map}function Ue(e,t){var r=function(e,t){return null==e?void 0:e[t]}(e,t);return xe(r)?r:void 0}Ae.prototype.clear=function(){this.__data__=ve?ve(null):{}},Ae.prototype.delete=function(e){return this.has(e)&&delete this.__data__[e]},Ae.prototype.get=function(e){var t=this.__data__;if(ve){var r=t[e];return r===n?void 0:r}return ee.call(t,e)?t[e]:void 0},Ae.prototype.has=function(e){var t=this.__data__;return ve?void 0!==t[e]:ee.call(t,e)},Ae.prototype.set=function(e,t){return this.__data__[e]=ve&&void 0===t?n:t,this},Ne.prototype.clear=function(){this.__data__=[]},Ne.prototype.delete=function(e){var t=this.__data__,r=je(t,e);return!(r<0||(r==t.length-1?t.pop():ce.call(t,r,1),0))},Ne.prototype.get=function(e){var t=this.__data__,r=je(t,e);return r<0?void 0:t[r][1]},Ne.prototype.has=function(e){return je(this.__data__,e)>-1},Ne.prototype.set=function(e,t){var r=this.__data__,n=je(r,e);return n<0?r.push([e,t]):r[n][1]=t,this},Pe.prototype.clear=function(){this.__data__={hash:new Ae,map:new(pe||Ne),string:new Ae}},Pe.prototype.delete=function(e){return ke(this,e).delete(e)},Pe.prototype.get=function(e){return ke(this,e).get(e)},Pe.prototype.has=function(e){return ke(this,e).has(e)},Pe.prototype.set=function(e,t){return ke(this,e).set(e,t),this},Ie.prototype.clear=function(){this.__data__=new Ne},Ie.prototype.delete=function(e){return this.__data__.delete(e)},Ie.prototype.get=function(e){return this.__data__.get(e)},Ie.prototype.has=function(e){return this.__data__.has(e)},Ie.prototype.set=function(e,t){var r=this.__data__;if(r instanceof Ne){var n=r.__data__;if(!pe||n.length<199)return n.push([e,t]),this;r=this.__data__=new Pe(n)}return r.set(e,t),this};var Fe=le?H(le,Object):function(){return[]},Be=function(e){return te.call(e)};function qe(e,t){return!!(t=null==t?o:t)&&("number"==typeof e||j.test(e))&&e>-1&&e%1==0&&e<t}function Ve(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||W)}function Ye(e){if(null!=e){try{return Z.call(e)}catch(t){}try{return e+""}catch(t){}}return""}function Ge(e,t){return e===t||e!=e&&t!=t}(de&&Be(new de(new ArrayBuffer(1)))!=E||pe&&Be(new pe)!=l||ye&&Be(ye.resolve())!=d||be&&Be(new be)!=y||ge&&Be(new ge)!=v)&&(Be=function(e){var t=te.call(e),r=t==h?e.constructor:void 0,n=r?Ye(r):void 0;if(n)switch(n){case me:return E;case Ee:return l;case _e:return d;case Oe:return y;case Se:return v}return t});var He=Array.isArray;function ze(e){return null!=e&&function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=o}(e.length)&&!Xe(e)}var Ke=fe||function(){return!1};function Xe(e){var t=$e(e)?te.call(e):"";return t==u||t==c}function $e(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function We(e){return ze(e)?Re(e):function(e){if(!Ve(e))return he(e);var t=[];for(var r in Object(e))ee.call(e,r)&&"constructor"!=r&&t.push(r);return t}(e)}r.exports=function(e){return Me(e,!0,!0)}}).call(this,r(39),r(40)(e))},function(e,t,r){"use strict";var n=["error","warn","log","info"],o="warn";function i(e){if(n.indexOf(e)<=n.indexOf(o)){for(var t,r=arguments.length,i=new Array(r>1?r-1:0),s=1;s<r;s++)i[s-1]=arguments[s];(t=console)[e].apply(t,i)}}function s(e){return n.reduce((function(t,r){return t[r]=i.bind(console,r,e),t}),{})}s.level=function(e){o=e},i.level=s.level,t.a=s},function(e,t,r){"use strict";var n=r(2),o=r(8),i=r(7);function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function a(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function u(){return u="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,r){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=f(e)););return e}(e,t);if(n){var o=Object.getOwnPropertyDescriptor(n,t);return o.get?o.get.call(arguments.length<3?e:r):o.value}},u.apply(this,arguments)}function c(e,t){return c=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},c(e,t)}function l(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=f(e);if(t){var o=f(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===s(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function f(e){return f=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},f(e)}var h=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&c(e,t)}(s,e);var t,r,o,i=l(s);function s(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),i.apply(this,arguments)}return t=s,o=[{key:"compare",value:function(e,t){var r=s.order.indexOf(e),n=s.order.indexOf(t);return r>=0||n>=0?r-n:e===t?0:e<t?-1:1}}],(r=[{key:"formatAt",value:function(e,t,r,o){if(s.compare(this.statics.blotName,r)<0&&this.scroll.query(r,n.Scope.BLOT)){var i=this.isolate(e,t);o&&i.wrap(r,o)}else u(f(s.prototype),"formatAt",this).call(this,e,t,r,o)}},{key:"optimize",value:function(e){if(u(f(s.prototype),"optimize",this).call(this,e),this.parent instanceof s&&s.compare(this.statics.blotName,this.parent.statics.blotName)>0){var t=this.parent.isolate(this.offset(),this.length());this.moveChildren(t),t.wrap(this)}}}])&&a(t.prototype,r),o&&a(t,o),Object.defineProperty(t,"prototype",{writable:!1}),s}(n.InlineBlot);h.allowedChildren=[h,o.a,n.EmbedBlot,i.a],h.order=["cursor","inline","link","underline","strike","italic","bold","script","code"],t.a=h},function(e,t,r){"use strict";r.d(t,"a",(function(){return _})),r.d(t,"b",(function(){return m})),r.d(t,"c",(function(){return E}));var n=r(4),o=r(8),i=r(16),s=r(12),a=r(7),u=r(15),c=r(0);function l(e){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l(e)}function f(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function h(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function d(e,t,r){return t&&h(e.prototype,t),r&&h(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function p(){return p="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,r){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=v(e)););return e}(e,t);if(n){var o=Object.getOwnPropertyDescriptor(n,t);return o.get?o.get.call(arguments.length<3?e:r):o.value}},p.apply(this,arguments)}function y(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&b(e,t)}function b(e,t){return b=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},b(e,t)}function g(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=v(e);if(t){var o=v(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===l(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function v(e){return v=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},v(e)}var m=function(e){y(r,e);var t=g(r);function r(){return f(this,r),t.apply(this,arguments)}return d(r,[{key:"code",value:function(e,t){return this.children.map((function(e){return e.length()<=1?"":e.domNode.innerText})).join("\n").slice(e,e+t)}},{key:"html",value:function(e,t){return"<pre>\n".concat(Object(a.b)(this.code(e,t)),"\n</pre>")}}],[{key:"create",value:function(e){var t=p(v(r),"create",this).call(this,e);return t.setAttribute("spellcheck",!1),t}}]),r}(u.a),E=function(e){y(r,e);var t=g(r);function r(){return f(this,r),t.apply(this,arguments)}return d(r,null,[{key:"register",value:function(){c.a.register(m)}}]),r}(n.d),_=function(e){y(r,e);var t=g(r);function r(){return f(this,r),t.apply(this,arguments)}return d(r)}(s.a);_.blotName="code",_.tagName="CODE",E.blotName="code-block",E.className="ql-code-block",E.tagName="DIV",m.blotName="code-block-container",m.className="ql-code-block-container",m.tagName="DIV",m.allowedChildren=[E],E.allowedChildren=[a.a,o.a,i.a],E.requiredContainer=m,E.TAB="  "},function(e,t,r){"use strict";var n=r(2),o=r(3),i=r(4),s=r(8),a=r(15),u=r(32);function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function l(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i=[],s=!0,a=!1;try{for(r=r.call(e);!(s=(n=r.next()).done)&&(i.push(n.value),!t||i.length!==t);s=!0);}catch(u){a=!0,o=u}finally{try{s||null==r.return||r.return()}finally{if(a)throw o}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return f(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?f(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function h(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function d(){return d="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,r){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=b(e)););return e}(e,t);if(n){var o=Object.getOwnPropertyDescriptor(n,t);return o.get?o.get.call(arguments.length<3?e:r):o.value}},d.apply(this,arguments)}function p(e,t){return p=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},p(e,t)}function y(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=b(e);if(t){var o=b(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===c(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function b(e){return b=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},b(e)}function g(e){return e instanceof i.d||e instanceof i.a}var v=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&p(e,t)}(f,e);var t,r,a,c=y(f);function f(e,t,r){var n,o=r.emitter;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,f),(n=c.call(this,e,t)).defaultUpdateAttributes=void 0,n.emitter=o,n.optimize(),n.enable(),n.domNode.addEventListener("dragstart",(function(e){return n.handleDragStart(e)})),n}return t=f,r=[{key:"batchStart",value:function(){this.batch=!0}},{key:"batchEnd",value:function(){this.batch=!1,this.optimize()}},{key:"emitMount",value:function(e){this.emitter.emit(o.a.events.SCROLL_BLOT_MOUNT,e)}},{key:"emitUnmount",value:function(e){this.emitter.emit(o.a.events.SCROLL_BLOT_UNMOUNT,e)}},{key:"deleteAt",value:function(e,t){var r=l(this.line(e),2),n=r[0],o=r[1],a=l(this.line(e+t),1)[0];if(d(b(f.prototype),"deleteAt",this).call(this,e,t),null!=a&&n!==a&&o>0){if(n instanceof i.a||a instanceof i.a)return void this.optimize();var u=a.children.head instanceof s.a?null:a.children.head;n.moveChildren(a,u),n.remove()}this.optimize()}},{key:"enable",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.domNode.setAttribute("contenteditable",e)}},{key:"formatAt",value:function(e,t,r,n){d(b(f.prototype),"formatAt",this).call(this,e,t,r,n),this.optimize()}},{key:"handleDragStart",value:function(e){e.preventDefault()}},{key:"insertAt",value:function(e,t,r){if(e>=this.length())if(null==r||null==this.scroll.query(t,n.Scope.BLOCK)){var o=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(o),null==r&&t.endsWith("\n")?o.insertAt(0,t.slice(0,-1),r):o.insertAt(0,t,r)}else{var i=this.scroll.create(t,r);this.appendChild(i)}else d(b(f.prototype),"insertAt",this).call(this,e,t,r);this.optimize()}},{key:"insertBefore",value:function(e,t){if(e.statics.scope===n.Scope.INLINE_BLOT){var r=this.scroll.create(this.statics.defaultChild.blotName);if(r.appendChild(e),d(b(f.prototype),"insertBefore",this).call(this,r,t),null==this.defaultUpdateAttributes)return;var o=this.defaultUpdateAttributes,i=o.paraAttributes,s=o.charAttributes,a=e.domNode;a.nodeType===Node.TEXT_NODE&&Object.entries(s).forEach((function(e){var t=l(e,2),n=t[0],o=t[1];return r.formatAt(0,a.length,n,o)})),Object.entries(i).forEach((function(e){var t=l(e,2),n=t[0],o=t[1];return r.format(n,o)}))}else d(b(f.prototype),"insertBefore",this).call(this,e,t)}},{key:"isEnabled",value:function(){return"true"===this.domNode.getAttribute("contenteditable")}},{key:"leaf",value:function(e){var t=this.path(e).pop();return t&&t[0]instanceof n.LeafBlot?t:[null,-1]}},{key:"line",value:function(e){return e===this.length()?this.line(e-1):this.descendant(g,e)}},{key:"lines",value:function(){return function e(t,r,o){var i=[],s=o;return t.children.forEachAt(r,o,(function(t,r,o){g(t)?i.push(t):t instanceof n.ContainerBlot&&(i=i.concat(e(t,r,s))),s-=o})),i}(this,arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE)}},{key:"optimize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};!0!==this.batch&&(d(b(f.prototype),"optimize",this).call(this,e,t),e.length>0&&this.emitter.emit(o.a.events.SCROLL_OPTIMIZE,e,t))}},{key:"path",value:function(e){return d(b(f.prototype),"path",this).call(this,e).slice(1)}},{key:"remove",value:function(){}},{key:"update",value:function(e){var t=this;if(!0!==this.batch){var r=o.a.sources.USER;if("string"==typeof e&&(r=e),Array.isArray(e)||(e=this.observer.takeRecords()),e=e.filter((function(e){var r=e.target,n=t.find(r,!0);return n&&n.scroll===t})),1===e.length&&"childList"===e[0].type&&1===e[0].removedNodes.length&&e[0].removedNodes[0].tagName===u.b.tagName&&0===e[0].target.childElementCount){var n=e[0],s=this.registry.find(n.target);if(s instanceof u.a){var a=n.removedNodes[0].cloneNode(!1),c=this.scroll.create(a);return s.appendChild(c),void s.deleteAt(0,c.length())}}e.length>0&&this.emitter.emit(o.a.events.SCROLL_BEFORE_UPDATE,r,e);var l,h=0===this.domNode.childElementCount,p=1===this.domNode.childElementCount&&"BR"===this.domNode.firstElementChild.tagName;if((h||p)&&(l=e.find((function(e){return"childList"===e.type&&e.target===t.domNode&&e.removedNodes.length>0}))),l){var y,g=l.removedNodes[0],v=this.registry.find(g);v&&(v instanceof i.d?y=v.delta():v.children&&v.children.head instanceof i.d&&(y=v.children.head.delta()),y&&y.eachLine((function(e,r){var n=e.ops.length>0&&e.ops[0].attributes||{};return t.defaultUpdateAttributes={charAttributes:n,paraAttributes:r},!1})))}d(b(f.prototype),"update",this).call(this,e.concat([])),e.length>0&&this.emitter.emit(o.a.events.SCROLL_UPDATE,r,e,p&&this.defaultUpdateAttributes?this.defaultUpdateAttributes.charAttributes:null),this.defaultUpdateAttributes&&(this.defaultUpdateAttributes=void 0)}}}],r&&h(t.prototype,r),a&&h(t,a),Object.defineProperty(t,"prototype",{writable:!1}),f}(n.ScrollBlot);v.blotName="scroll",v.className="ql-editor",v.tagName="DIV",v.defaultChild=i.d,v.allowedChildren=[i.d,i.a,a.a],t.a=v},function(e,t,r){"use strict";function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function i(e,t){return i=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},i(e,t)}function s(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,o=a(e);if(t){var i=a(this).constructor;r=Reflect.construct(o,arguments,i)}else r=o.apply(this,arguments);return function(e,t){if(t&&("object"===n(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function a(e){return a=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},a(e)}var u=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&i(e,t)}(u,e);var t,r,n,a=s(u);function u(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u),a.apply(this,arguments)}return t=u,r&&o(t.prototype,r),n&&o(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}(r(2).ContainerBlot);t.a=u},function(e,t,r){"use strict";var n=r(2),o=r(7);function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function s(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function a(){return a="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,r){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=l(e)););return e}(e,t);if(n){var o=Object.getOwnPropertyDescriptor(n,t);return o.get?o.get.call(arguments.length<3?e:r):o.value}},a.apply(this,arguments)}function u(e,t){return u=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},u(e,t)}function c(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=l(e);if(t){var o=l(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===i(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function l(e){return l=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},l(e)}var f=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&u(e,t)}(h,e);var t,r,i,f=c(h);function h(e,t,r){var n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,h),(n=f.call(this,e,t)).selection=r,n.textNode=document.createTextNode(h.CONTENTS),n.domNode.appendChild(n.textNode),n.savedLength=0,n}return t=h,i=[{key:"value",value:function(){}}],(r=[{key:"detach",value:function(){null!=this.parent&&this.parent.removeChild(this)}},{key:"format",value:function(e,t){if(0===this.savedLength){for(var r=this,o=0;null!=r&&r.statics.scope!==n.Scope.BLOCK_BLOT;)o+=r.offset(r.parent),r=r.parent;null!=r&&(this.savedLength=h.CONTENTS.length,r.optimize(),r.formatAt(o,h.CONTENTS.length,e,t),this.savedLength=0)}else a(l(h.prototype),"format",this).call(this,e,t)}},{key:"index",value:function(e,t){return e===this.textNode?0:a(l(h.prototype),"index",this).call(this,e,t)}},{key:"length",value:function(){return this.savedLength}},{key:"position",value:function(){return[this.textNode,this.textNode.data.length]}},{key:"remove",value:function(){a(l(h.prototype),"remove",this).call(this),this.parent=null}},{key:"restore",value:function(){if(this.selection.composing||null==this.parent)return null;for(var e=this.selection.getNativeRange();null!=this.domNode.lastChild&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);var t,r=this.prev instanceof o.a?this.prev:null,n=r?r.length():0,i=this.next instanceof o.a?this.next:null,s=i?i.text:"",a=this.textNode,u=a.data.split(h.CONTENTS).join("");a.data=h.CONTENTS;var c=!1;if(r)t=r,(u||i)&&(r.insertAt(r.length(),u+s),i&&i.remove());else if(i)t=i,i.insertAt(0,u);else{var l=document.createTextNode(u);t=this.scroll.create(l),this.parent.insertBefore(t,this),c=!0}if(this.remove(),e){var f=function(e,t){return r&&e===r.domNode?t:e===a?n+t-1:i&&e===i.domNode?n+u.length+t:null},d=f(e.start.node,e.start.offset),p=f(e.end.node,e.end.offset);if(c&&d===p&&0===d&&e.start.node===e.end.node&&e.start.node===a&&(p=d=t.domNode.data.length),null!==d&&null!==p)return{startNode:t.domNode,startOffset:d,endNode:t.domNode,endOffset:p}}return null}},{key:"update",value:function(e,t){var r=this,n=!1;if(e.forEach((function(e){e.target===r.textNode&&"characterData"===e.type?n=!0:e.target===r.domNode&&"childList"===e.type&&e.addedNodes.forEach((function(e){e.nodeType===Node.ELEMENT_NODE&&"BR"===e.tagName&&r.domNode.removeChild(e)}))})),n){var o=this.restore();o&&(t.range=o)}}},{key:"optimize",value:function(e){a(l(h.prototype),"optimize",this).call(this,e);for(var t=this.parent;t;){if("A"===t.domNode.tagName){this.savedLength=h.CONTENTS.length,t.isolate(this.offset(t),this.length()).unwrap(),this.savedLength=0;break}t=t.parent}}},{key:"value",value:function(){return""}}])&&s(t.prototype,r),i&&s(t,i),Object.defineProperty(t,"prototype",{writable:!1}),h}(n.EmbedBlot);f.blotName="cursor",f.className="ql-cursor",f.tagName="span",f.CONTENTS="\ufeff",t.a=f},function(e,t,r){(function(e,r){var n="__lodash_hash_undefined__",o=9007199254740991,i="[object Arguments]",s="[object AsyncFunction]",a="[object Function]",u="[object GeneratorFunction]",c="[object Null]",l="[object Object]",f="[object Proxy]",h="[object Undefined]",d=/^\[object .+?Constructor\]$/,p=/^(?:0|[1-9]\d*)$/,y={};y["[object Float32Array]"]=y["[object Float64Array]"]=y["[object Int8Array]"]=y["[object Int16Array]"]=y["[object Int32Array]"]=y["[object Uint8Array]"]=y["[object Uint8ClampedArray]"]=y["[object Uint16Array]"]=y["[object Uint32Array]"]=!0,y[i]=y["[object Array]"]=y["[object ArrayBuffer]"]=y["[object Boolean]"]=y["[object DataView]"]=y["[object Date]"]=y["[object Error]"]=y[a]=y["[object Map]"]=y["[object Number]"]=y[l]=y["[object RegExp]"]=y["[object Set]"]=y["[object String]"]=y["[object WeakMap]"]=!1;var b,g,v,m="object"==typeof e&&e&&e.Object===Object&&e,E="object"==typeof self&&self&&self.Object===Object&&self,_=m||E||Function("return this")(),O=t&&!t.nodeType&&t,S=O&&"object"==typeof r&&r&&!r.nodeType&&r,w=S&&S.exports===O,T=w&&m.process,A=function(){try{var e=S&&S.require&&S.require("util").types;return e||T&&T.binding&&T.binding("util")}catch(t){}}(),N=A&&A.isTypedArray,P=Array.prototype,I=Function.prototype,R=Object.prototype,C=_["__core-js_shared__"],j=I.toString,M=R.hasOwnProperty,x=(b=/[^.]+$/.exec(C&&C.keys&&C.keys.IE_PROTO||""))?"Symbol(src)_1."+b:"",D=R.toString,L=j.call(Object),k=RegExp("^"+j.call(M).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),U=w?_.Buffer:void 0,F=_.Symbol,B=_.Uint8Array,q=U?U.allocUnsafe:void 0,V=(g=Object.getPrototypeOf,v=Object,function(e){return g(v(e))}),Y=Object.create,G=R.propertyIsEnumerable,H=P.splice,z=F?F.toStringTag:void 0,K=function(){try{var e=me(Object,"defineProperty");return e({},"",{}),e}catch(t){}}(),X=U?U.isBuffer:void 0,$=Math.max,W=Date.now,Q=me(_,"Map"),J=me(Object,"create"),Z=function(){function e(){}return function(t){if(!Ce(t))return{};if(Y)return Y(t);e.prototype=t;var r=new e;return e.prototype=void 0,r}}();function ee(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}function te(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}function re(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}function ne(e){var t=this.__data__=new te(e);this.size=t.size}function oe(e,t){var r=Ae(e),n=!r&&Te(e),o=!r&&!n&&Pe(e),i=!r&&!n&&!o&&Me(e),s=r||n||o||i,a=s?function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}(e.length,String):[],u=a.length;for(var c in e)!t&&!M.call(e,c)||s&&("length"==c||o&&("offset"==c||"parent"==c)||i&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||Ee(c,u))||a.push(c);return a}function ie(e,t,r){(void 0!==r&&!we(e[t],r)||void 0===r&&!(t in e))&&ue(e,t,r)}function se(e,t,r){var n=e[t];M.call(e,t)&&we(n,r)&&(void 0!==r||t in e)||ue(e,t,r)}function ae(e,t){for(var r=e.length;r--;)if(we(e[r][0],t))return r;return-1}function ue(e,t,r){"__proto__"==t&&K?K(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}ee.prototype.clear=function(){this.__data__=J?J(null):{},this.size=0},ee.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},ee.prototype.get=function(e){var t=this.__data__;if(J){var r=t[e];return r===n?void 0:r}return M.call(t,e)?t[e]:void 0},ee.prototype.has=function(e){var t=this.__data__;return J?void 0!==t[e]:M.call(t,e)},ee.prototype.set=function(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=J&&void 0===t?n:t,this},te.prototype.clear=function(){this.__data__=[],this.size=0},te.prototype.delete=function(e){var t=this.__data__,r=ae(t,e);return!(r<0||(r==t.length-1?t.pop():H.call(t,r,1),--this.size,0))},te.prototype.get=function(e){var t=this.__data__,r=ae(t,e);return r<0?void 0:t[r][1]},te.prototype.has=function(e){return ae(this.__data__,e)>-1},te.prototype.set=function(e,t){var r=this.__data__,n=ae(r,e);return n<0?(++this.size,r.push([e,t])):r[n][1]=t,this},re.prototype.clear=function(){this.size=0,this.__data__={hash:new ee,map:new(Q||te),string:new ee}},re.prototype.delete=function(e){var t=ve(this,e).delete(e);return this.size-=t?1:0,t},re.prototype.get=function(e){return ve(this,e).get(e)},re.prototype.has=function(e){return ve(this,e).has(e)},re.prototype.set=function(e,t){var r=ve(this,e),n=r.size;return r.set(e,t),this.size+=r.size==n?0:1,this},ne.prototype.clear=function(){this.__data__=new te,this.size=0},ne.prototype.delete=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r},ne.prototype.get=function(e){return this.__data__.get(e)},ne.prototype.has=function(e){return this.__data__.has(e)},ne.prototype.set=function(e,t){var r=this.__data__;if(r instanceof te){var n=r.__data__;if(!Q||n.length<199)return n.push([e,t]),this.size=++r.size,this;r=this.__data__=new re(n)}return r.set(e,t),this.size=r.size,this};var ce,le=function(e,t,r){for(var n=-1,o=Object(e),i=r(e),s=i.length;s--;){var a=i[ce?s:++n];if(!1===t(o[a],a,o))break}return e};function fe(e){return null==e?void 0===e?h:c:z&&z in Object(e)?function(e){var t=M.call(e,z),r=e[z];try{e[z]=void 0;var n=!0}catch(i){}var o=D.call(e);return n&&(t?e[z]=r:delete e[z]),o}(e):function(e){return D.call(e)}(e)}function he(e){return je(e)&&fe(e)==i}function de(e){return!(!Ce(e)||function(e){return!!x&&x in e}(e))&&(Ie(e)?k:d).test(function(e){if(null!=e){try{return j.call(e)}catch(t){}try{return e+""}catch(t){}}return""}(e))}function pe(e){if(!Ce(e))return function(e){var t=[];if(null!=e)for(var r in Object(e))t.push(r);return t}(e);var t=_e(e),r=[];for(var n in e)("constructor"!=n||!t&&M.call(e,n))&&r.push(n);return r}function ye(e,t,r,n,o){e!==t&&le(t,(function(i,s){if(o||(o=new ne),Ce(i))!function(e,t,r,n,o,i,s){var a=Oe(e,r),u=Oe(t,r),c=s.get(u);if(c)ie(e,r,c);else{var f,h,d,p,y,b=i?i(a,u,r+"",e,t,s):void 0,g=void 0===b;if(g){var v=Ae(u),m=!v&&Pe(u),E=!v&&!m&&Me(u);b=u,v||m||E?Ae(a)?b=a:je(y=a)&&Ne(y)?b=function(e,t){var r=-1,n=e.length;for(t||(t=Array(n));++r<n;)t[r]=e[r];return t}(a):m?(g=!1,b=function(e,t){if(t)return e.slice();var r=e.length,n=q?q(r):new e.constructor(r);return e.copy(n),n}(u,!0)):E?(g=!1,d=(f=u).buffer,p=new d.constructor(d.byteLength),new B(p).set(new B(d)),h=p,b=new f.constructor(h,f.byteOffset,f.length)):b=[]:function(e){if(!je(e)||fe(e)!=l)return!1;var t=V(e);if(null===t)return!0;var r=M.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&j.call(r)==L}(u)||Te(u)?(b=a,Te(a)?b=function(e){return function(e,t,r,n){var o=!r;r||(r={});for(var i=-1,s=t.length;++i<s;){var a=t[i],u=n?n(r[a],e[a],a,r,e):void 0;void 0===u&&(u=e[a]),o?ue(r,a,u):se(r,a,u)}return r}(e,xe(e))}(a):Ce(a)&&!Ie(a)||(b=function(e){return"function"!=typeof e.constructor||_e(e)?{}:Z(V(e))}(u))):g=!1}g&&(s.set(u,b),o(b,u,n,i,s),s.delete(u)),ie(e,r,b)}}(e,t,s,r,ye,n,o);else{var a=n?n(Oe(e,s),i,s+"",e,t,o):void 0;void 0===a&&(a=i),ie(e,s,a)}}),xe)}function be(e,t){return Se(function(e,t,r){return t=$(void 0===t?e.length-1:t,0),function(){for(var n=arguments,o=-1,i=$(n.length-t,0),s=Array(i);++o<i;)s[o]=n[t+o];o=-1;for(var a=Array(t+1);++o<t;)a[o]=n[o];return a[t]=r(s),function(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}(e,this,a)}}(e,t,ke),e+"")}var ge=K?function(e,t){return K(e,"toString",{configurable:!0,enumerable:!1,value:(r=t,function(){return r}),writable:!0});var r}:ke;function ve(e,t){var r,n,o=e.__data__;return("string"==(n=typeof(r=t))||"number"==n||"symbol"==n||"boolean"==n?"__proto__"!==r:null===r)?o["string"==typeof t?"string":"hash"]:o.map}function me(e,t){var r=function(e,t){return null==e?void 0:e[t]}(e,t);return de(r)?r:void 0}function Ee(e,t){var r=typeof e;return!!(t=null==t?o:t)&&("number"==r||"symbol"!=r&&p.test(e))&&e>-1&&e%1==0&&e<t}function _e(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||R)}function Oe(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]}var Se=function(e){var t=0,r=0;return function(){var n=W(),o=16-(n-r);if(r=n,o>0){if(++t>=800)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}(ge);function we(e,t){return e===t||e!=e&&t!=t}var Te=he(function(){return arguments}())?he:function(e){return je(e)&&M.call(e,"callee")&&!G.call(e,"callee")},Ae=Array.isArray;function Ne(e){return null!=e&&Re(e.length)&&!Ie(e)}var Pe=X||function(){return!1};function Ie(e){if(!Ce(e))return!1;var t=fe(e);return t==a||t==u||t==s||t==f}function Re(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=o}function Ce(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function je(e){return null!=e&&"object"==typeof e}var Me=N?function(e){return function(t){return e(t)}}(N):function(e){return je(e)&&Re(e.length)&&!!y[fe(e)]};function xe(e){return Ne(e)?oe(e,!0):pe(e)}var De,Le=(De=function(e,t,r){ye(e,t,r)},be((function(e,t){var r=-1,n=t.length,o=n>1?t[n-1]:void 0,i=n>2?t[2]:void 0;for(o=De.length>3&&"function"==typeof o?(n--,o):void 0,i&&function(e,t,r){if(!Ce(r))return!1;var n=typeof t;return!!("number"==n?Ne(r)&&Ee(t,r.length):"string"==n&&t in r)&&we(r[t],e)}(t[0],t[1],i)&&(o=n<3?void 0:o,n=1),e=Object(e);++r<n;){var s=t[r];s&&De(e,s,r,o)}return e})));function ke(e){return e}r.exports=Le}).call(this,r(39),r(40)(e))},function(e,t,r){(function(e,r){var n="__lodash_hash_undefined__",o=1,i=2,s=9007199254740991,a="[object Arguments]",u="[object Array]",c="[object AsyncFunction]",l="[object Boolean]",f="[object Date]",h="[object Error]",d="[object Function]",p="[object GeneratorFunction]",y="[object Map]",b="[object Number]",g="[object Null]",v="[object Object]",m="[object Promise]",E="[object Proxy]",_="[object RegExp]",O="[object Set]",S="[object String]",w="[object Symbol]",T="[object Undefined]",A="[object WeakMap]",N="[object ArrayBuffer]",P="[object DataView]",I=/^\[object .+?Constructor\]$/,R=/^(?:0|[1-9]\d*)$/,C={};C["[object Float32Array]"]=C["[object Float64Array]"]=C["[object Int8Array]"]=C["[object Int16Array]"]=C["[object Int32Array]"]=C["[object Uint8Array]"]=C["[object Uint8ClampedArray]"]=C["[object Uint16Array]"]=C["[object Uint32Array]"]=!0,C[a]=C[u]=C[N]=C[l]=C[P]=C[f]=C[h]=C[d]=C[y]=C[b]=C[v]=C[_]=C[O]=C[S]=C[A]=!1;var j="object"==typeof e&&e&&e.Object===Object&&e,M="object"==typeof self&&self&&self.Object===Object&&self,x=j||M||Function("return this")(),D=t&&!t.nodeType&&t,L=D&&"object"==typeof r&&r&&!r.nodeType&&r,k=L&&L.exports===D,U=k&&j.process,F=function(){try{return U&&U.binding&&U.binding("util")}catch(e){}}(),B=F&&F.isTypedArray;function q(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1}function V(e){var t=-1,r=Array(e.size);return e.forEach((function(e,n){r[++t]=[n,e]})),r}function Y(e){var t=-1,r=Array(e.size);return e.forEach((function(e){r[++t]=e})),r}var G,H,z,K=Array.prototype,X=Function.prototype,$=Object.prototype,W=x["__core-js_shared__"],Q=X.toString,J=$.hasOwnProperty,Z=(G=/[^.]+$/.exec(W&&W.keys&&W.keys.IE_PROTO||""))?"Symbol(src)_1."+G:"",ee=$.toString,te=RegExp("^"+Q.call(J).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),re=k?x.Buffer:void 0,ne=x.Symbol,oe=x.Uint8Array,ie=$.propertyIsEnumerable,se=K.splice,ae=ne?ne.toStringTag:void 0,ue=Object.getOwnPropertySymbols,ce=re?re.isBuffer:void 0,le=(H=Object.keys,z=Object,function(e){return H(z(e))}),fe=Fe(x,"DataView"),he=Fe(x,"Map"),de=Fe(x,"Promise"),pe=Fe(x,"Set"),ye=Fe(x,"WeakMap"),be=Fe(Object,"create"),ge=Ye(fe),ve=Ye(he),me=Ye(de),Ee=Ye(pe),_e=Ye(ye),Oe=ne?ne.prototype:void 0,Se=Oe?Oe.valueOf:void 0;function we(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}function Te(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}function Ae(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}function Ne(e){var t=-1,r=null==e?0:e.length;for(this.__data__=new Ae;++t<r;)this.add(e[t])}function Pe(e){var t=this.__data__=new Te(e);this.size=t.size}function Ie(e,t){var r=ze(e),n=!r&&He(e),o=!r&&!n&&Ke(e),i=!r&&!n&&!o&&Je(e),s=r||n||o||i,a=s?function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}(e.length,String):[],u=a.length;for(var c in e)!t&&!J.call(e,c)||s&&("length"==c||o&&("offset"==c||"parent"==c)||i&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||Ve(c,u))||a.push(c);return a}function Re(e,t){for(var r=e.length;r--;)if(Ge(e[r][0],t))return r;return-1}function Ce(e){return null==e?void 0===e?T:g:ae&&ae in Object(e)?function(e){var t=J.call(e,ae),r=e[ae];try{e[ae]=void 0;var n=!0}catch(i){}var o=ee.call(e);return n&&(t?e[ae]=r:delete e[ae]),o}(e):function(e){return ee.call(e)}(e)}function je(e){return Qe(e)&&Ce(e)==a}function Me(e,t,r,n,s){return e===t||(null==e||null==t||!Qe(e)&&!Qe(t)?e!=e&&t!=t:function(e,t,r,n,s,c){var d=ze(e),p=ze(t),g=d?u:qe(e),m=p?u:qe(t),E=(g=g==a?v:g)==v,T=(m=m==a?v:m)==v,A=g==m;if(A&&Ke(e)){if(!Ke(t))return!1;d=!0,E=!1}if(A&&!E)return c||(c=new Pe),d||Je(e)?Le(e,t,r,n,s,c):function(e,t,r,n,s,a,u){switch(r){case P:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case N:return!(e.byteLength!=t.byteLength||!a(new oe(e),new oe(t)));case l:case f:case b:return Ge(+e,+t);case h:return e.name==t.name&&e.message==t.message;case _:case S:return e==t+"";case y:var c=V;case O:var d=n&o;if(c||(c=Y),e.size!=t.size&&!d)return!1;var p=u.get(e);if(p)return p==t;n|=i,u.set(e,t);var g=Le(c(e),c(t),n,s,a,u);return u.delete(e),g;case w:if(Se)return Se.call(e)==Se.call(t)}return!1}(e,t,g,r,n,s,c);if(!(r&o)){var I=E&&J.call(e,"__wrapped__"),R=T&&J.call(t,"__wrapped__");if(I||R){var C=I?e.value():e,j=R?t.value():t;return c||(c=new Pe),s(C,j,r,n,c)}}return!!A&&(c||(c=new Pe),function(e,t,r,n,i,s){var a=r&o,u=ke(e),c=u.length,l=ke(t),f=l.length;if(c!=f&&!a)return!1;for(var h=c;h--;){var d=u[h];if(!(a?d in t:J.call(t,d)))return!1}var p=s.get(e);if(p&&s.get(t))return p==t;var y=!0;s.set(e,t),s.set(t,e);for(var b=a;++h<c;){var g=e[d=u[h]],v=t[d];if(n)var m=a?n(v,g,d,t,e,s):n(g,v,d,e,t,s);if(!(void 0===m?g===v||i(g,v,r,n,s):m)){y=!1;break}b||(b="constructor"==d)}if(y&&!b){var E=e.constructor,_=t.constructor;E==_||!("constructor"in e)||!("constructor"in t)||"function"==typeof E&&E instanceof E&&"function"==typeof _&&_ instanceof _||(y=!1)}return s.delete(e),s.delete(t),y}(e,t,r,n,s,c))}(e,t,r,n,Me,s))}function xe(e){return!(!We(e)||function(e){return!!Z&&Z in e}(e))&&(Xe(e)?te:I).test(Ye(e))}function De(e){if(r=(t=e)&&t.constructor,n="function"==typeof r&&r.prototype||$,t!==n)return le(e);var t,r,n,o=[];for(var i in Object(e))J.call(e,i)&&"constructor"!=i&&o.push(i);return o}function Le(e,t,r,n,s,a){var u=r&o,c=e.length,l=t.length;if(c!=l&&!(u&&l>c))return!1;var f=a.get(e);if(f&&a.get(t))return f==t;var h=-1,d=!0,p=r&i?new Ne:void 0;for(a.set(e,t),a.set(t,e);++h<c;){var y=e[h],b=t[h];if(n)var g=u?n(b,y,h,t,e,a):n(y,b,h,e,t,a);if(void 0!==g){if(g)continue;d=!1;break}if(p){if(!q(t,(function(e,t){if(o=t,!p.has(o)&&(y===e||s(y,e,r,n,a)))return p.push(t);var o}))){d=!1;break}}else if(y!==b&&!s(y,b,r,n,a)){d=!1;break}}return a.delete(e),a.delete(t),d}function ke(e){return function(e,t,r){var n=t(e);return ze(e)?n:function(e,t){for(var r=-1,n=t.length,o=e.length;++r<n;)e[o+r]=t[r];return e}(n,r(e))}(e,Ze,Be)}function Ue(e,t){var r,n,o=e.__data__;return("string"==(n=typeof(r=t))||"number"==n||"symbol"==n||"boolean"==n?"__proto__"!==r:null===r)?o["string"==typeof t?"string":"hash"]:o.map}function Fe(e,t){var r=function(e,t){return null==e?void 0:e[t]}(e,t);return xe(r)?r:void 0}we.prototype.clear=function(){this.__data__=be?be(null):{},this.size=0},we.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},we.prototype.get=function(e){var t=this.__data__;if(be){var r=t[e];return r===n?void 0:r}return J.call(t,e)?t[e]:void 0},we.prototype.has=function(e){var t=this.__data__;return be?void 0!==t[e]:J.call(t,e)},we.prototype.set=function(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=be&&void 0===t?n:t,this},Te.prototype.clear=function(){this.__data__=[],this.size=0},Te.prototype.delete=function(e){var t=this.__data__,r=Re(t,e);return!(r<0||(r==t.length-1?t.pop():se.call(t,r,1),--this.size,0))},Te.prototype.get=function(e){var t=this.__data__,r=Re(t,e);return r<0?void 0:t[r][1]},Te.prototype.has=function(e){return Re(this.__data__,e)>-1},Te.prototype.set=function(e,t){var r=this.__data__,n=Re(r,e);return n<0?(++this.size,r.push([e,t])):r[n][1]=t,this},Ae.prototype.clear=function(){this.size=0,this.__data__={hash:new we,map:new(he||Te),string:new we}},Ae.prototype.delete=function(e){var t=Ue(this,e).delete(e);return this.size-=t?1:0,t},Ae.prototype.get=function(e){return Ue(this,e).get(e)},Ae.prototype.has=function(e){return Ue(this,e).has(e)},Ae.prototype.set=function(e,t){var r=Ue(this,e),n=r.size;return r.set(e,t),this.size+=r.size==n?0:1,this},Ne.prototype.add=Ne.prototype.push=function(e){return this.__data__.set(e,n),this},Ne.prototype.has=function(e){return this.__data__.has(e)},Pe.prototype.clear=function(){this.__data__=new Te,this.size=0},Pe.prototype.delete=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r},Pe.prototype.get=function(e){return this.__data__.get(e)},Pe.prototype.has=function(e){return this.__data__.has(e)},Pe.prototype.set=function(e,t){var r=this.__data__;if(r instanceof Te){var n=r.__data__;if(!he||n.length<199)return n.push([e,t]),this.size=++r.size,this;r=this.__data__=new Ae(n)}return r.set(e,t),this.size=r.size,this};var Be=ue?function(e){return null==e?[]:(e=Object(e),function(e,t){for(var r=-1,n=null==e?0:e.length,o=0,i=[];++r<n;){var s=e[r];t(s,r,e)&&(i[o++]=s)}return i}(ue(e),(function(t){return ie.call(e,t)})))}:function(){return[]},qe=Ce;function Ve(e,t){return!!(t=null==t?s:t)&&("number"==typeof e||R.test(e))&&e>-1&&e%1==0&&e<t}function Ye(e){if(null!=e){try{return Q.call(e)}catch(t){}try{return e+""}catch(t){}}return""}function Ge(e,t){return e===t||e!=e&&t!=t}(fe&&qe(new fe(new ArrayBuffer(1)))!=P||he&&qe(new he)!=y||de&&qe(de.resolve())!=m||pe&&qe(new pe)!=O||ye&&qe(new ye)!=A)&&(qe=function(e){var t=Ce(e),r=t==v?e.constructor:void 0,n=r?Ye(r):"";if(n)switch(n){case ge:return P;case ve:return y;case me:return m;case Ee:return O;case _e:return A}return t});var He=je(function(){return arguments}())?je:function(e){return Qe(e)&&J.call(e,"callee")&&!ie.call(e,"callee")},ze=Array.isArray,Ke=ce||function(){return!1};function Xe(e){if(!We(e))return!1;var t=Ce(e);return t==d||t==p||t==c||t==E}function $e(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=s}function We(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function Qe(e){return null!=e&&"object"==typeof e}var Je=B?function(e){return function(t){return e(t)}}(B):function(e){return Qe(e)&&$e(e.length)&&!!C[Ce(e)]};function Ze(e){return null!=(t=e)&&$e(t.length)&&!Xe(t)?Ie(e):De(e);var t}r.exports=function(e,t){return Me(e,t)}}).call(this,r(39),r(40)(e))},function(e,t,r){"use strict";r.d(t,"b",(function(){return T})),r.d(t,"a",(function(){return w})),r.d(t,"d",(function(){return R})),r.d(t,"c",(function(){return C}));var n=r(10),o=r.n(n),i=r(18),s=r.n(i),a=r(1),u=r.n(a),c=r(2),l=r(0),f=r(11),h=r(9);function d(e){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d(e)}function p(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i=[],s=!0,a=!1;try{for(r=r.call(e);!(s=(n=r.next()).done)&&(i.push(n.value),!t||i.length!==t);s=!0);}catch(u){a=!0,o=u}finally{try{s||null==r.return||r.return()}finally{if(a)throw o}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return y(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?y(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function g(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach((function(t){v(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function v(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function m(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function E(e,t){return E=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},E(e,t)}function _(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=O(e);if(t){var o=O(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===d(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function O(e){return O=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},O(e)}var S=Object(f.a)("quill:keyboard"),w=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey",T=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&E(e,t)}(i,e);var t,r,n,o=_(i);function i(e,t){var r;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),(r=o.call(this,e,t)).bindings={},Object.keys(r.options.bindings).forEach((function(e){r.options.bindings[e]&&r.addBinding(r.options.bindings[e])})),r.addBinding({key:"Enter",shiftKey:null},r.handleEnter),r.addBinding({key:"Enter",metaKey:null,ctrlKey:null,altKey:null},(function(){})),/Firefox/i.test(navigator.userAgent)?(r.addBinding({key:"Backspace"},{collapsed:!0},r.handleBackspace),r.addBinding({key:"Delete"},{collapsed:!0},r.handleDelete)):(r.addBinding({key:"Backspace"},{collapsed:!0,prefix:/^.?$/},r.handleBackspace),r.addBinding({key:"Delete"},{collapsed:!0,suffix:/^.?$/},r.handleDelete)),r.addBinding({key:"Backspace"},{collapsed:!1},r.handleDeleteRange),r.addBinding({key:"Delete"},{collapsed:!1},r.handleDeleteRange),r.addBinding({key:"Backspace",altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},r.handleBackspace),r.listen(),r}return t=i,r=[{key:"addBinding",value:function(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=R(e);null!=o?("function"==typeof r&&(r={handler:r}),"function"==typeof n&&(n={handler:n}),(Array.isArray(o.key)?o.key:[o.key]).forEach((function(e){var i=g(g(g({},o),{},{key:e},r),n);t.bindings[i.key]=t.bindings[i.key]||[],t.bindings[i.key].push(i)}))):S.warn("Attempted to add invalid keyboard binding",o)}},{key:"listen",value:function(){var e=this;this.quill.root.addEventListener("keydown",(function(t){var r="Enter"===t.key&&229===t.which,n=t.isComposing||r;if(!t.defaultPrevented&&!n){var o=(e.bindings[t.key]||[]).concat(e.bindings[t.which]||[]).filter((function(e){return i.match(t,e)}));if(0!==o.length){var a=e.quill.getSelection();if(null!=a&&e.quill.hasFocus()){var u=p(e.quill.getLine(a.index),2),l=u[0],f=u[1],h=p(e.quill.getLeaf(a.index),2),y=h[0],b=h[1],g=p(0===a.length?[y,b]:e.quill.getLeaf(a.index+a.length),2),v=g[0],m=g[1],E=y instanceof c.TextBlot?y.value().slice(0,b):"",_=v instanceof c.TextBlot?v.value().slice(m):"",O={collapsed:0===a.length,empty:0===a.length&&l.length()<=1,format:e.quill.getFormat(a),line:l,offset:f,prefix:E,suffix:_,event:t};o.some((function(t){if(null!=t.collapsed&&t.collapsed!==O.collapsed)return!1;if(null!=t.empty&&t.empty!==O.empty)return!1;if(null!=t.offset&&t.offset!==O.offset)return!1;if(Array.isArray(t.format)){if(t.format.every((function(e){return null==O.format[e]})))return!1}else if("object"===d(t.format)&&!Object.keys(t.format).every((function(e){return!0===t.format[e]?null!=O.format[e]:!1===t.format[e]?null==O.format[e]:s()(t.format[e],O.format[e])})))return!1;return!(null!=t.prefix&&!t.prefix.test(O.prefix)||null!=t.suffix&&!t.suffix.test(O.suffix)||!0===t.handler.call(e,a,O,t))}))&&t.preventDefault()}}}}))}},{key:"handleBackspace",value:function(e,t){var r=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(t.prefix)?2:1;if(!(0===e.index||this.quill.getLength()<=1)){var n={},o=p(this.quill.getLine(e.index),1)[0],i=(new u.a).retain(e.index-r).delete(r);if(0===t.offset){var s=p(this.quill.getLine(e.index-1),1)[0];if(s&&!("block"===s.statics.blotName&&s.length()<=1)){var c=o.formats(),f=this.quill.getFormat(e.index-1,1);if(n=a.AttributeMap.diff(c,f)||{},Object.keys(n).length>0){var h=(new u.a).retain(e.index+o.length()-2).retain(1,n);i=i.compose(h)}}}this.quill.updateContents(i,l.a.sources.USER),this.quill.focus()}}},{key:"handleDelete",value:function(e,t){var r=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(t.suffix)?2:1;if(!(e.index>=this.quill.getLength()-r)){var n={},o=p(this.quill.getLine(e.index),1)[0],i=(new u.a).retain(e.index).delete(r);if(t.offset>=o.length()-1){var s=p(this.quill.getLine(e.index+1),1)[0];if(s){var c=o.formats(),f=this.quill.getFormat(e.index,1);n=a.AttributeMap.diff(c,f)||{},Object.keys(n).length>0&&(i=i.retain(s.length()-1).retain(1,n))}}this.quill.updateContents(i,l.a.sources.USER),this.quill.focus()}}},{key:"handleDeleteRange",value:function(e){C({range:e,quill:this.quill}),this.quill.focus()}},{key:"handleEnter",value:function(e,t){var r=this,n=Object.keys(t.format).reduce((function(e,n){return r.quill.scroll.query(n,c.Scope.BLOCK)&&!Array.isArray(t.format[n])&&(e[n]=t.format[n]),e}),{}),o=(new u.a).retain(e.index).delete(e.length).insert("\n",n);this.quill.updateContents(o,l.a.sources.USER),this.quill.setSelection(e.index+1,l.a.sources.SILENT),this.quill.focus(),Object.keys(t.format).forEach((function(e){null==n[e]&&(Array.isArray(t.format[e])||"code"!==e&&"link"!==e&&r.quill.format(e,t.format[e],l.a.sources.USER))}))}}],n=[{key:"match",value:function(e,t){return!["altKey","ctrlKey","metaKey","shiftKey"].some((function(r){return!!t[r]!==e[r]&&null!==t[r]}))&&(t.key===e.key||t.key===e.which)}}],r&&m(t.prototype,r),n&&m(t,n),Object.defineProperty(t,"prototype",{writable:!1}),i}(h.a);function A(e){return{key:"Tab",shiftKey:!e,format:{"code-block":!0},handler:function(t){var r=this.quill.scroll.query("code-block"),n=0===t.length?this.quill.getLines(t.index,1):this.quill.getLines(t),o=t.index,i=t.length;n.forEach((function(t,n){e?(t.insertAt(0,r.TAB),0===n?o+=r.TAB.length:i+=r.TAB.length):t.domNode.textContent.startsWith(r.TAB)&&(t.deleteAt(0,r.TAB.length),0===n?o-=r.TAB.length:i-=r.TAB.length)})),this.quill.update(l.a.sources.USER),this.quill.setSelection(o,i,l.a.sources.SILENT)}}}function N(e,t){var r;return v(r={key:e,shiftKey:t,altKey:null},"ArrowLeft"===e?"prefix":"suffix",/^$/),v(r,"handler",(function(r){var n=r.index;return"ArrowRight"===e&&(n+=r.length+1),!(p(this.quill.getLeaf(n),1)[0]instanceof c.EmbedBlot&&("ArrowLeft"===e?t?this.quill.setSelection(r.index-1,r.length+1,l.a.sources.USER):this.quill.setSelection(r.index-1,l.a.sources.USER):t?this.quill.setSelection(r.index,r.length+1,l.a.sources.USER):this.quill.setSelection(r.index+r.length+1,l.a.sources.USER),1))})),r}function P(e){return{key:e[0],shortKey:!0,handler:function(t,r){this.quill.format(e,!r.format[e],l.a.sources.USER)}}}function I(e){return{key:e?"ArrowUp":"ArrowDown",collapsed:!0,format:["table"],handler:function(t,r){var n=e?"prev":"next",o=r.line,i=o.parent[n];if(null!=i){if("table-row"===i.statics.blotName){for(var s=i.children.head,a=o;null!=a.prev;)a=a.prev,s=s.next;var u=s.offset(this.quill.scroll)+Math.min(r.offset,s.length()-1);this.quill.setSelection(u,0,l.a.sources.USER)}}else{var c=o.table()[n];null!=c&&(e?this.quill.setSelection(c.offset(this.quill.scroll)+c.length()-1,0,l.a.sources.USER):this.quill.setSelection(c.offset(this.quill.scroll),0,l.a.sources.USER))}return!1}}}function R(e){if("string"==typeof e||"number"==typeof e)e={key:e};else{if("object"!==d(e))return null;e=o()(e)}return e.shortKey&&(e[w]=e.shortKey,delete e.shortKey),e}function C(e){var t=e.quill,r=e.range,n=t.getLines(r),o={};if(n.length>1){var i=n[0].formats(),s=n[n.length-1].formats();o=a.AttributeMap.diff(s,i)||{}}t.deleteText(r,l.a.sources.USER),Object.keys(o).length>0&&t.formatLine(r.index,1,o,l.a.sources.USER),t.setSelection(r.index,l.a.sources.SILENT)}T.DEFAULTS={bindings:{bold:P("bold"),italic:P("italic"),underline:P("underline"),indent:{key:"Tab",format:["blockquote","indent","list"],handler:function(e,t){return!(!t.collapsed||0===t.offset)||(this.quill.format("indent","+1",l.a.sources.USER),!1)}},outdent:{key:"Tab",shiftKey:!0,format:["blockquote","indent","list"],handler:function(e,t){return!(!t.collapsed||0===t.offset)||(this.quill.format("indent","-1",l.a.sources.USER),!1)}},"outdent backspace":{key:"Backspace",collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler:function(e,t){null!=t.format.indent?this.quill.format("indent","-1",l.a.sources.USER):null!=t.format.list&&this.quill.format("list",!1,l.a.sources.USER)}},"indent code-block":A(!0),"outdent code-block":A(!1),"remove tab":{key:"Tab",shiftKey:!0,collapsed:!0,prefix:/\t$/,handler:function(e){this.quill.deleteText(e.index-1,1,l.a.sources.USER)}},tab:{key:"Tab",handler:function(e,t){if(t.format.table)return!0;this.quill.history.cutoff();var r=(new u.a).retain(e.index).delete(e.length).insert("\t");return this.quill.updateContents(r,l.a.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(e.index+1,l.a.sources.SILENT),!1}},"blockquote empty enter":{key:"Enter",collapsed:!0,format:["blockquote"],empty:!0,handler:function(){this.quill.format("blockquote",!1,l.a.sources.USER)}},"list empty enter":{key:"Enter",collapsed:!0,format:["list"],empty:!0,handler:function(e,t){var r={list:!1};t.format.indent&&(r.indent=!1),this.quill.formatLine(e.index,e.length,r,l.a.sources.USER)}},"checklist enter":{key:"Enter",collapsed:!0,format:{list:"checked"},handler:function(e){var t=p(this.quill.getLine(e.index),2),r=t[0],n=t[1],o=g(g({},r.formats()),{},{list:"checked"}),i=(new u.a).retain(e.index).insert("\n",o).retain(r.length()-n-1).retain(1,{list:"unchecked"});this.quill.updateContents(i,l.a.sources.USER),this.quill.setSelection(e.index+1,l.a.sources.SILENT),this.quill.scrollIntoView()}},"header enter":{key:"Enter",collapsed:!0,format:["header"],suffix:/^$/,handler:function(e,t){var r=p(this.quill.getLine(e.index),2),n=r[0],o=r[1],i=(new u.a).retain(e.index).insert("\n",t.format).retain(n.length()-o-1).retain(1,{header:null});this.quill.updateContents(i,l.a.sources.USER),this.quill.setSelection(e.index+1,l.a.sources.SILENT),this.quill.scrollIntoView()}},"table backspace":{key:"Backspace",format:["table"],collapsed:!0,offset:0,handler:function(){}},"table delete":{key:"Delete",format:["table"],collapsed:!0,suffix:/^$/,handler:function(){}},"table enter":{key:"Enter",shiftKey:null,format:["table"],handler:function(e){var t=this.quill.getModule("table");if(t){var r=p(t.getTable(e),4),n=r[0],o=function(e,t,r,n){return null==t.prev&&null==t.next?null==r.prev&&null==r.next?0===n?-1:1:null==r.prev?-1:1:null==t.prev?-1:null==t.next?1:null}(0,r[1],r[2],r[3]);if(null==o)return;var i=n.offset();if(o<0){var s=(new u.a).retain(i).insert("\n");this.quill.updateContents(s,l.a.sources.USER),this.quill.setSelection(e.index+1,e.length,l.a.sources.SILENT)}else if(o>0){i+=n.length();var a=(new u.a).retain(i).insert("\n");this.quill.updateContents(a,l.a.sources.USER),this.quill.setSelection(i,l.a.sources.USER)}}}},"table tab":{key:"Tab",shiftKey:null,format:["table"],handler:function(e,t){var r=t.event,n=t.line,o=n.offset(this.quill.scroll);r.shiftKey?this.quill.setSelection(o-1,l.a.sources.USER):this.quill.setSelection(o+n.length(),l.a.sources.USER)}},"list autofill":{key:" ",shiftKey:null,collapsed:!0,format:{"code-block":!1,blockquote:!1,table:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler:function(e,t){if(null==this.quill.scroll.query("list"))return!0;var r,n=t.prefix.length,o=p(this.quill.getLine(e.index),2),i=o[0],s=o[1];if(s>n)return!0;switch(t.prefix.trim()){case"[]":case"[ ]":r="unchecked";break;case"[x]":r="checked";break;case"-":case"*":r="bullet";break;default:r="ordered"}this.quill.insertText(e.index," ",l.a.sources.USER),this.quill.history.cutoff();var a=(new u.a).retain(e.index-s).delete(n+1).retain(i.length()-2-s).retain(1,{list:r});return this.quill.updateContents(a,l.a.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(e.index-n,l.a.sources.SILENT),!1}},"code exit":{key:"Enter",collapsed:!0,format:["code-block"],prefix:/^$/,suffix:/^\s*$/,handler:function(e){for(var t=p(this.quill.getLine(e.index),2),r=t[0],n=t[1],o=2,i=r;null!=i&&i.length()<=1&&i.formats()["code-block"];)if(i=i.prev,(o-=1)<=0){var s=(new u.a).retain(e.index+r.length()-n-2).retain(1,{"code-block":null}).delete(1);return this.quill.updateContents(s,l.a.sources.USER),this.quill.setSelection(e.index-1,l.a.sources.SILENT),!1}return!0}},"embed left":N("ArrowLeft",!1),"embed left shift":N("ArrowLeft",!0),"embed right":N("ArrowRight",!1),"embed right shift":N("ArrowRight",!0),"table down":I(!1),"table up":I(!0)}}},function(e,t,r){"use strict";function n(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var o=function(){function e(t,r){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.quill=t,this.options=r,this.modules={}}var t,r,o;return t=e,(r=[{key:"init",value:function(){var e=this;Object.keys(this.options.modules).forEach((function(t){null==e.modules[t]&&e.addModule(t)}))}},{key:"addModule",value:function(e){var t=this.quill.constructor.import("modules/".concat(e));return this.modules[e]=new t(this.quill,this.options.modules[e]||{}),this.modules[e]}}])&&n(t.prototype,r),o&&n(t,o),Object.defineProperty(t,"prototype",{writable:!1}),e}();o.DEFAULTS={modules:{}},o.themes={default:o},t.a=o},function(e,t,r){"use strict";r.d(t,"a",(function(){return l})),r.d(t,"b",(function(){return f})),r.d(t,"c",(function(){return h}));var n=r(2);function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function i(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function s(){return s="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,r){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=c(e)););return e}(e,t);if(n){var o=Object.getOwnPropertyDescriptor(n,t);return o.get?o.get.call(arguments.length<3?e:r):o.value}},s.apply(this,arguments)}function a(e,t){return a=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},a(e,t)}function u(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=c(e);if(t){var i=c(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===o(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function c(e){return c=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},c(e)}var l=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&a(e,t)}(l,e);var t,r,n,o=u(l);function l(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),o.apply(this,arguments)}return t=l,(r=[{key:"value",value:function(e){var t=s(c(l.prototype),"value",this).call(this,e);if(!t.startsWith("rgb("))return t;t=t.replace(/^[^\d]+/,"").replace(/[^\d]+$/,"");var r=t.split(",").map((function(e){return"00".concat(parseInt(e,10).toString(16)).slice(-2)})).join("");return"#".concat(r)}}])&&i(t.prototype,r),n&&i(t,n),Object.defineProperty(t,"prototype",{writable:!1}),l}(n.StyleAttributor),f=new n.ClassAttributor("color","ql-color",{scope:n.Scope.INLINE}),h=new l("color","color",{scope:n.Scope.INLINE})},function(e,t,r){"use strict";r.d(t,"a",(function(){return i})),r.d(t,"b",(function(){return s})),r.d(t,"c",(function(){return a}));var n=r(2),o={scope:n.Scope.BLOCK,whitelist:["rtl"]},i=new n.Attributor("direction","dir",o),s=new n.ClassAttributor("direction","ql-direction",o),a=new n.StyleAttributor("direction","direction",o)},,function(e,t,r){"use strict";r.d(t,"a",(function(){return i})),r.d(t,"b",(function(){return s})),r.d(t,"c",(function(){return a}));var n=r(2),o={scope:n.Scope.BLOCK,whitelist:["right","center","justify"]},i=new n.Attributor("align","align",o),s=new n.ClassAttributor("align","ql-align",o),a=new n.StyleAttributor("align","text-align",o)},function(e,t,r){"use strict";t.a=new WeakMap},,,function(e,t,r){"use strict";r.d(t,"a",(function(){return i})),r.d(t,"b",(function(){return s}));var n=r(2),o=r(21),i=new n.ClassAttributor("background","ql-bg",{scope:n.Scope.INLINE}),s=new o.a("background","background-color",{scope:n.Scope.INLINE})},function(e,t,r){"use strict";r.d(t,"b",(function(){return d})),r.d(t,"a",(function(){return f}));var n=r(2);function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function i(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function s(){return s="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,r){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=c(e)););return e}(e,t);if(n){var o=Object.getOwnPropertyDescriptor(n,t);return o.get?o.get.call(arguments.length<3?e:r):o.value}},s.apply(this,arguments)}function a(e,t){return a=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},a(e,t)}function u(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=c(e);if(t){var i=c(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===o(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function c(e){return c=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},c(e)}var l={scope:n.Scope.INLINE,whitelist:["serif","monospace"]},f=new n.ClassAttributor("font","ql-font",l),h=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&a(e,t)}(l,e);var t,r,n,o=u(l);function l(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),o.apply(this,arguments)}return t=l,(r=[{key:"value",value:function(e){return s(c(l.prototype),"value",this).call(this,e).replace(/["']/g,"")}}])&&i(t.prototype,r),n&&i(t,n),Object.defineProperty(t,"prototype",{writable:!1}),l}(n.StyleAttributor),d=new h("font","font-family",l)},function(e,t,r){"use strict";r.d(t,"a",(function(){return o})),r.d(t,"b",(function(){return i}));var n=r(2),o=new n.ClassAttributor("size","ql-size",{scope:n.Scope.INLINE,whitelist:["small","large","huge"]}),i=new n.StyleAttributor("size","font-size",{scope:n.Scope.INLINE,whitelist:["10px","18px","32px"]})},,function(e,t,r){"use strict";r.d(t,"a",(function(){return y})),r.d(t,"b",(function(){return b}));var n=r(4),o=r(15),i=r(0);function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function a(){return a="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,r){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=p(e)););return e}(e,t);if(n){var o=Object.getOwnPropertyDescriptor(n,t);return o.get?o.get.call(arguments.length<3?e:r):o.value}},a.apply(this,arguments)}function u(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function c(e,t,r){return t&&u(e.prototype,t),r&&u(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function l(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function f(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&h(e,t)}function h(e,t){return h=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},h(e,t)}function d(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=p(e);if(t){var o=p(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===s(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function p(e){return p=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},p(e)}var y=function(e){f(r,e);var t=d(r);function r(){return l(this,r),t.apply(this,arguments)}return c(r)}(o.a);y.blotName="list-container",y.tagName="OL";var b=function(e){f(r,e);var t=d(r);function r(e,n){var o;l(this,r),o=t.call(this,e,n);var i=n.ownerDocument.createElement("span"),s=function(t){if(e.isEnabled()){var r=o.statics.formats(n,e);"checked"===r?(o.format("list","unchecked"),t.preventDefault()):"unchecked"===r&&(o.format("list","checked"),t.preventDefault())}};return i.addEventListener("mousedown",s),i.addEventListener("touchstart",s),o.attachUI(i),o}return c(r,[{key:"format",value:function(e,t){e===this.statics.blotName&&t?this.domNode.setAttribute("data-list",t):a(p(r.prototype),"format",this).call(this,e,t)}}],[{key:"create",value:function(e){var t=a(p(r),"create",this).call(this);return t.setAttribute("data-list",e),t}},{key:"formats",value:function(e){return e.getAttribute("data-list")||void 0}},{key:"register",value:function(){i.a.register(y)}}]),r}(n.d);b.blotName="list",b.tagName="LI",y.allowedChildren=[b],b.requiredContainer=y},function(e,t,r){"use strict";r.d(t,"a",(function(){return f})),r.d(t,"b",(function(){return d}));var n=r(2),o=r(0);function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function s(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function a(e,t){return a=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},a(e,t)}function u(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=l(e);if(t){var o=l(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===i(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return c(e)}(this,r)}}function c(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function l(e){return l=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},l(e)}var f=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&a(e,t)}(l,e);var t,r,n,i=u(l);function l(e,t){var r;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),(r=i.call(this,e,t)).lastRecorded=0,r.ignoreChange=!1,r.clear(),r.quill.on(o.a.events.EDITOR_CHANGE,(function(e,t,n,i){e!==o.a.events.TEXT_CHANGE||r.ignoreChange||(r.options.userOnly&&i!==o.a.sources.USER?r.transform(t):r.record(t,n))})),r.quill.keyboard.addBinding({key:"z",shortKey:!0},r.undo.bind(c(r))),r.quill.keyboard.addBinding({key:"z",shortKey:!0,shiftKey:!0},r.redo.bind(c(r))),/Win/i.test(navigator.platform)&&r.quill.keyboard.addBinding({key:"y",shortKey:!0},r.redo.bind(c(r))),r.quill.root.addEventListener("beforeinput",(function(e){"historyUndo"===e.inputType?(r.undo(),e.preventDefault()):"historyRedo"===e.inputType&&(r.redo(),e.preventDefault())})),r}return t=l,(r=[{key:"change",value:function(e,t){if(0!==this.stack[e].length){var r=this.stack[e].pop(),n=this.quill.getContents(),i=r.invert(n);this.stack[t].push(i),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(r,o.a.sources.USER),this.ignoreChange=!1;var s=d(this.quill.scroll,r);this.quill.setSelection(s)}}},{key:"clear",value:function(){this.stack={undo:[],redo:[]}}},{key:"cutoff",value:function(){this.lastRecorded=0}},{key:"record",value:function(e,t){if(0!==e.ops.length){this.stack.redo=[];var r=e.invert(t),n=Date.now();if(this.lastRecorded+this.options.delay>n&&this.stack.undo.length>0){var o=this.stack.undo.pop();r=r.compose(o)}else this.lastRecorded=n;0!==r.length()&&(this.stack.undo.push(r),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift())}}},{key:"redo",value:function(){this.change("redo","undo")}},{key:"transform",value:function(e){h(this.stack.undo,e),h(this.stack.redo,e)}},{key:"undo",value:function(){this.change("undo","redo")}}])&&s(t.prototype,r),n&&s(t,n),Object.defineProperty(t,"prototype",{writable:!1}),l}(r(9).a);function h(e,t){for(var r=t,n=e.length-1;n>=0;n-=1){var o=e[n];e[n]=r.transform(o,!0),r=o.transform(r),0===e[n].length()&&e.splice(n,1)}}function d(e,t){var r=t.reduce((function(e,t){return e+(t.delete||0)}),0),o=t.length()-r;return function(e,t){var r=t.ops[t.ops.length-1];return null!=r&&(null!=r.insert?"string"==typeof r.insert&&r.insert.endsWith("\n"):null!=r.attributes&&Object.keys(r.attributes).some((function(t){return null!=e.query(t,n.Scope.BLOCK)})))}(e,t)&&(o-=1),o}f.DEFAULTS={delay:1e3,maxStack:100,userOnly:!1}},,,function(e,t,r){"use strict";r.r(t);var n=r(0),o=r(4),i=r(8),s=r(15),a=r(16),u=r(37),c=r(12),l=r(14),f=r(7),h=r(38),d=r(33),p=r(19),y=r(1),b=r.n(y),g=r(3);function v(e){return v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},v(e)}function m(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function E(e,t){return E=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},E(e,t)}function _(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=O(e);if(t){var o=O(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===v(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function O(e){return O=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},O(e)}var S=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&E(e,t)}(i,e);var t,r,n,o=_(i);function i(e,t){var r;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),r=o.call(this,e,t),e.root.addEventListener("drop",(function(t){var n;if(t.preventDefault(),document.caretRangeFromPoint)n=document.caretRangeFromPoint(t.clientX,t.clientY);else{if(!document.caretPositionFromPoint)return;var o=document.caretPositionFromPoint(t.clientX,t.clientY);(n=document.createRange()).setStart(o.offsetNode,o.offset),n.setEnd(o.offsetNode,o.offset)}var i=e.selection.normalizeNative(n),s=e.selection.normalizedToRange(i);r.upload(s,t.dataTransfer.files)})),r}return t=i,(r=[{key:"upload",value:function(e,t){var r=this,n=[];Array.from(t).forEach((function(e){e&&r.options.mimetypes.includes(e.type)&&n.push(e)})),n.length>0&&this.options.handler.call(this,e,n)}}])&&m(t.prototype,r),n&&m(t,n),Object.defineProperty(t,"prototype",{writable:!1}),i}(r(9).a);S.DEFAULTS={mimetypes:["image/png","image/jpeg"],handler:function(e,t){var r=this,n=t.map((function(e){return new Promise((function(t){var r=new FileReader;r.onload=function(e){t(e.target.result)},r.readAsDataURL(e)}))}));Promise.all(n).then((function(t){var n=t.reduce((function(e,t){return e.insert({image:t})}),(new b.a).retain(e.index).delete(e.length));r.quill.updateContents(n,g.a.sources.USER),r.quill.setSelection(e.index+t.length,g.a.sources.SILENT)}))}};var w=S;n.a.register({"blots/block":o.d,"blots/block/embed":o.a,"blots/break":i.a,"blots/container":s.a,"blots/cursor":a.a,"blots/embed":u.a,"blots/inline":c.a,"blots/scroll":l.a,"blots/text":f.a,"modules/clipboard":h.a,"modules/history":d.a,"modules/keyboard":p.b,"modules/uploader":w}),t.default={Quill:n.a}},function(e,t,r){"use strict";var n=r(2),o=r(7);function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function s(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function a(){return a="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,r){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=l(e)););return e}(e,t);if(n){var o=Object.getOwnPropertyDescriptor(n,t);return o.get?o.get.call(arguments.length<3?e:r):o.value}},a.apply(this,arguments)}function u(e,t){return u=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},u(e,t)}function c(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=l(e);if(t){var o=l(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===i(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function l(e){return l=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},l(e)}var f="\ufeff",h=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&u(e,t)}(h,e);var t,r,n,i=c(h);function h(e,t){var r;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,h),(r=i.call(this,e,t)).contentNode=document.createElement("span"),r.contentNode.setAttribute("contenteditable",!1),Array.from(r.domNode.childNodes).forEach((function(e){r.contentNode.appendChild(e)})),r.leftGuard=document.createTextNode(f),r.rightGuard=document.createTextNode(f),r.domNode.appendChild(r.leftGuard),r.domNode.appendChild(r.contentNode),r.domNode.appendChild(r.rightGuard),r}return t=h,(r=[{key:"index",value:function(e,t){return e===this.leftGuard?0:e===this.rightGuard?1:a(l(h.prototype),"index",this).call(this,e,t)}},{key:"restore",value:function(e){var t,r,n=e.data.split(f).join("");if(e===this.leftGuard)if(this.prev instanceof o.a){var i=this.prev.length();this.prev.insertAt(i,n),t={startNode:this.prev.domNode,startOffset:i+n.length}}else r=document.createTextNode(n),this.parent.insertBefore(this.scroll.create(r),this),t={startNode:r,startOffset:n.length};else e===this.rightGuard&&(this.next instanceof o.a?(this.next.insertAt(0,n),t={startNode:this.next.domNode,startOffset:n.length}):(r=document.createTextNode(n),this.parent.insertBefore(this.scroll.create(r),this.next),t={startNode:r,startOffset:n.length}));return e.data=f,t}},{key:"update",value:function(e,t){var r=this;e.forEach((function(e){if("characterData"===e.type&&(e.target===r.leftGuard||e.target===r.rightGuard)){var n=r.restore(e.target);n&&(t.range=n)}}))}}])&&s(t.prototype,r),n&&s(t,n),Object.defineProperty(t,"prototype",{writable:!1}),h}(n.EmbedBlot);t.a=h},function(e,t,r){"use strict";r.d(t,"a",(function(){return M})),r.d(t,"b",(function(){return F}));var n=r(1),o=r.n(n),i=r(2),s=r(4),a=r(0),u=r(11),c=r(9),l=r(24),f=r(28),h=r(13),d=r(21),p=r(22),y=r(29),b=r(30),g=r(19);function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(Object(r),!0).forEach((function(t){_(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function E(e){return E="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},E(e)}function _(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function O(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i=[],s=!0,a=!1;try{for(r=r.call(e);!(s=(n=r.next()).done)&&(i.push(n.value),!t||i.length!==t);s=!0);}catch(u){a=!0,o=u}finally{try{s||null==r.return||r.return()}finally{if(a)throw o}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return S(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?S(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function S(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function w(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function T(e,t){return T=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},T(e,t)}function A(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=P(e);if(t){var o=P(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===E(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return N(e)}(this,r)}}function N(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function P(e){return P=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},P(e)}var I=Object(u.a)("quill:clipboard"),R=[[Node.TEXT_NODE,function(e,t){var r=e.data;if("O:P"===e.parentNode.tagName)return t.insert(r.trim());if(0===r.trim().length&&r.includes("\n"))return t;if(!U(e)){var n=function(e,t){var r=t.replace(/[^\u00a0]/g,"");return r.length<1&&e?" ":r};r=(r=r.replace(/\r\n/g," ").replace(/\n/g," ")).replace(/\s\s+/g,n.bind(n,!0)),(null==e.previousSibling&&L(e.parentNode)||null!=e.previousSibling&&L(e.previousSibling))&&(r=r.replace(/^\s+/,n.bind(n,!1))),(null==e.nextSibling&&L(e.parentNode)||null!=e.nextSibling&&L(e.nextSibling))&&(r=r.replace(/\s+$/,n.bind(n,!1)))}return t.insert(r)}],[Node.TEXT_NODE,q],["br",function(e,t){return D(t,"\n")||t.insert("\n"),t}],[Node.ELEMENT_NODE,q],[Node.ELEMENT_NODE,function(e,t,r){var n=r.query(e);if(null==n)return t;if(n.prototype instanceof i.EmbedBlot){var s={},a=n.value(e);if(null!=a)return s[n.blotName]=a,(new o.a).insert(s,n.formats(e,r))}else if(n.prototype instanceof i.BlockBlot&&!D(t,"\n")&&t.insert("\n"),"function"==typeof n.formats)return x(t,n.blotName,n.formats(e,r));return t}],[Node.ELEMENT_NODE,function(e,t,r){var n=i.Attributor.keys(e),o=i.ClassAttributor.keys(e),s=i.StyleAttributor.keys(e),a={};return n.concat(o).concat(s).forEach((function(t){var n=r.query(t,i.Scope.ATTRIBUTE);null!=n&&(a[n.attrName]=n.value(e),a[n.attrName])||(null==(n=C[t])||n.attrName!==t&&n.keyName!==t||(a[n.attrName]=n.value(e)||void 0),null==(n=j[t])||n.attrName!==t&&n.keyName!==t||(n=j[t],a[n.attrName]=n.value(e)||void 0))})),Object.keys(a).length>0?x(t,a):t}],[Node.ELEMENT_NODE,function(e,t){var r={},n=e.style||{};return"italic"===n.fontStyle&&(r.italic=!0),"underline"===n.textDecoration&&(r.underline=!0),"line-through"===n.textDecoration&&(r.strike=!0),(n.fontWeight.startsWith("bold")||parseInt(n.fontWeight,10)>=700)&&(r.bold=!0),Object.keys(r).length>0&&(t=x(t,r)),parseFloat(n.textIndent||0)>0?(new o.a).insert("\t").concat(t):t}],["li",function(e,t,r){var n=r.query(e);if(null==n||"list"!==n.blotName||!D(t,"\n"))return t;for(var i=-1,s=e.parentNode;null!=s;)["OL","UL"].includes(s.tagName)&&(i+=1),s=s.parentNode;return i<=0?t:t.reduce((function(e,t){return t.attributes&&"number"==typeof t.attributes.indent?e.push(t):e.insert(t.insert,m({indent:i},t.attributes||{}))}),new o.a)}],["ol, ul",function(e,t){var r="OL"===e.tagName?"ordered":"bullet";return x(t,"list",r)}],["pre",function(e,t,r){var n=r.query("code-block"),o=!n||n.formats(e,r);return x(t,"code-block",o)}],["tr",function(e,t){var r="TABLE"===e.parentNode.tagName?e.parentNode:e.parentNode.parentNode,n=Array.from(r.querySelectorAll("tr")).indexOf(e)+1;return x(t,"table",n)}],["b",B.bind(B,"bold")],["i",B.bind(B,"italic")],["strike",B.bind(B,"strike")],["style",function(){return new o.a}]],C=[l.a,p.a].reduce((function(e,t){return e[t.keyName]=t,e}),{}),j=[l.c,f.b,d.c,p.c,y.b,b.b].reduce((function(e,t){return e[t.keyName]=t,e}),{}),M=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&T(e,t)}(s,e);var t,r,n,i=A(s);function s(e,t){var r;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),(r=i.call(this,e,t)).quill.root.addEventListener("copy",(function(e){return r.onCaptureCopy(e,!1)})),r.quill.root.addEventListener("cut",(function(e){return r.onCaptureCopy(e,!0)})),r.quill.root.addEventListener("paste",r.onCapturePaste.bind(N(r))),r.matchers=[],R.concat(r.options.matchers).forEach((function(e){var t=O(e,2),n=t[0],o=t[1];r.addMatcher(n,o)})),r}return t=s,r=[{key:"addMatcher",value:function(e,t){this.matchers.push([e,t])}},{key:"convert",value:function(e){var t=e.html,r=e.text,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(n[h.c.blotName])return(new o.a).insert(r,_({},h.c.blotName,n[h.c.blotName]));if(!t)return(new o.a).insert(r||"");var i=this.convertHTML(t);return D(i,"\n")&&(null==i.ops[i.ops.length-1].attributes||n.table)?i.compose((new o.a).retain(i.length()-1).delete(1)):i}},{key:"convertHTML",value:function(e){var t=(new DOMParser).parseFromString(e,"text/html").body,r=new WeakMap,n=O(this.prepareMatching(t,r),2),o=n[0],i=n[1];return F(this.quill.scroll,t,o,i,r)}},{key:"dangerouslyPasteHTML",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:a.a.sources.API;if("string"==typeof e){var n=this.convert({html:e,text:""});this.quill.setContents(n,t),this.quill.setSelection(0,a.a.sources.SILENT)}else{var i=this.convert({html:t,text:""});this.quill.updateContents((new o.a).retain(e).concat(i),r),this.quill.setSelection(e+i.length(),a.a.sources.SILENT)}}},{key:"onCaptureCopy",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e.defaultPrevented){e.preventDefault();var r=O(this.quill.selection.getRange(),1)[0];if(null!=r){var n=this.onCopy(r,t),o=n.html,i=n.text;e.clipboardData.setData("text/plain",i),e.clipboardData.setData("text/html",o),t&&Object(g.c)({range:r,quill:this.quill})}}}},{key:"onCapturePaste",value:function(e){if(!e.defaultPrevented&&this.quill.isEnabled()){e.preventDefault();var t=this.quill.getSelection(!0);if(null!=t){var r=e.clipboardData.getData("text/html"),n=e.clipboardData.getData("text/plain"),o=Array.from(e.clipboardData.files||[]);if(!r&&o.length>0)this.quill.uploader.upload(t,o);else{if(r&&o.length>0){var i=(new DOMParser).parseFromString(r,"text/html");if(1===i.body.childElementCount&&"IMG"===i.body.firstElementChild.tagName)return void this.quill.uploader.upload(t,o)}this.onPaste(t,{html:r,text:n})}}}}},{key:"onCopy",value:function(e){var t=this.quill.getText(e);return{html:this.quill.getSemanticHTML(e),text:t}}},{key:"onPaste",value:function(e,t){var r=t.text,n=t.html,i=this.quill.getFormat(e.index),s=this.convert({text:r,html:n},i);I.log("onPaste",s,{text:r,html:n});var u=(new o.a).retain(e.index).delete(e.length).concat(s);this.quill.updateContents(u,a.a.sources.USER),this.quill.setSelection(u.length()-e.length,a.a.sources.SILENT),this.quill.scrollIntoView()}},{key:"prepareMatching",value:function(e,t){var r=[],n=[];return this.matchers.forEach((function(o){var i=O(o,2),s=i[0],a=i[1];switch(s){case Node.TEXT_NODE:n.push(a);break;case Node.ELEMENT_NODE:r.push(a);break;default:Array.from(e.querySelectorAll(s)).forEach((function(e){t.has(e)?t.get(e).push(a):t.set(e,[a])}))}})),[r,n]}}],r&&w(t.prototype,r),n&&w(t,n),Object.defineProperty(t,"prototype",{writable:!1}),s}(c.a);function x(e,t,r){return"object"===E(t)?Object.keys(t).reduce((function(e,r){return x(e,r,t[r])}),e):e.reduce((function(e,n){if(n.attributes&&n.attributes[t])return e.push(n);var o=r?_({},t,r):{};return e.insert(n.insert,m(m({},o),n.attributes))}),new o.a)}function D(e,t){for(var r="",n=e.ops.length-1;n>=0&&r.length<t.length;--n){var o=e.ops[n];if("string"!=typeof o.insert)break;r=o.insert+r}return r.slice(-1*t.length)===t}function L(e){return 0!==e.childNodes.length&&["address","article","blockquote","canvas","dd","div","dl","dt","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","iframe","li","main","nav","ol","output","p","pre","section","table","td","tr","ul","video"].includes(e.tagName.toLowerCase())}M.DEFAULTS={matchers:[]};var k=new WeakMap;function U(e){return null!=e&&(k.has(e)||("PRE"===e.tagName?k.set(e,!0):k.set(e,U(e.parentNode))),k.get(e))}function F(e,t,r,n,i){return t.nodeType===t.TEXT_NODE?n.reduce((function(r,n){return n(t,r,e)}),new o.a):t.nodeType===t.ELEMENT_NODE?Array.from(t.childNodes||[]).reduce((function(o,s){var a=F(e,s,r,n,i);return s.nodeType===t.ELEMENT_NODE&&(a=r.reduce((function(t,r){return r(s,t,e)}),a),a=(i.get(s)||[]).reduce((function(t,r){return r(s,t,e)}),a)),o.concat(a)}),new o.a):new o.a}function B(e,t,r){return x(r,e,!0)}function q(e,t,r){if(!D(t,"\n")){if(L(e))return t.insert("\n");if(t.length()>0&&e.nextSibling)for(var n=e.nextSibling;null!=n;){if(L(n))return t.insert("\n");var o=r.query(n);if(o&&o.prototype instanceof s.a)return t.insert("\n");n=n.firstChild}}return t}},function(e,t){var r;r=function(){return this}();try{r=r||new Function("return this")()}catch(n){"object"==typeof window&&(r=window)}e.exports=r},function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},function(e,t){},function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(r(52));!function(e){e.iterator=function(e){return new i.default(e)},e.length=function(e){return"number"==typeof e.delete?e.delete:"number"==typeof e.retain?e.retain:"string"==typeof e.insert?e.insert.length:1}}(o||(o={})),t.default=o},function(e,t){"use strict";var r=Object.prototype.hasOwnProperty,n="~";function o(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function s(e,t,r,o,s){if("function"!=typeof r)throw new TypeError("The listener must be a function");var a=new i(r,o||e,s),u=n?n+t:t;return e._events[u]?e._events[u].fn?e._events[u]=[e._events[u],a]:e._events[u].push(a):(e._events[u]=a,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new o:delete e._events[t]}function u(){this._events=new o,this._eventsCount=0}Object.create&&(o.prototype=Object.create(null),(new o).__proto__||(n=!1)),u.prototype.eventNames=function(){var e,t,o=[];if(0===this._eventsCount)return o;for(t in e=this._events)r.call(e,t)&&o.push(n?t.slice(1):t);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(e)):o},u.prototype.listeners=function(e){var t=n?n+e:e,r=this._events[t];if(!r)return[];if(r.fn)return[r.fn];for(var o=0,i=r.length,s=new Array(i);o<i;o++)s[o]=r[o].fn;return s},u.prototype.listenerCount=function(e){var t=n?n+e:e,r=this._events[t];return r?r.fn?1:r.length:0},u.prototype.emit=function(e,t,r,o,i,s){var a=n?n+e:e;if(!this._events[a])return!1;var u,c,l=this._events[a],f=arguments.length;if(l.fn){switch(l.once&&this.removeListener(e,l.fn,void 0,!0),f){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,t),!0;case 3:return l.fn.call(l.context,t,r),!0;case 4:return l.fn.call(l.context,t,r,o),!0;case 5:return l.fn.call(l.context,t,r,o,i),!0;case 6:return l.fn.call(l.context,t,r,o,i,s),!0}for(c=1,u=new Array(f-1);c<f;c++)u[c-1]=arguments[c];l.fn.apply(l.context,u)}else{var h,d=l.length;for(c=0;c<d;c++)switch(l[c].once&&this.removeListener(e,l[c].fn,void 0,!0),f){case 1:l[c].fn.call(l[c].context);break;case 2:l[c].fn.call(l[c].context,t);break;case 3:l[c].fn.call(l[c].context,t,r);break;case 4:l[c].fn.call(l[c].context,t,r,o);break;default:if(!u)for(h=1,u=new Array(f-1);h<f;h++)u[h-1]=arguments[h];l[c].fn.apply(l[c].context,u)}}return!0},u.prototype.on=function(e,t,r){return s(this,e,t,r,!1)},u.prototype.once=function(e,t,r){return s(this,e,t,r,!0)},u.prototype.removeListener=function(e,t,r,o){var i=n?n+e:e;if(!this._events[i])return this;if(!t)return a(this,i),this;var s=this._events[i];if(s.fn)s.fn!==t||o&&!s.once||r&&s.context!==r||a(this,i);else{for(var u=0,c=[],l=s.length;u<l;u++)(s[u].fn!==t||o&&!s[u].once||r&&s[u].context!==r)&&c.push(s[u]);c.length?this._events[i]=1===c.length?c[0]:c:a(this,i)}return this},u.prototype.removeAllListeners=function(e){var t;return e?(t=n?n+e:e,this._events[t]&&a(this,t)):(this._events=new o,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=n,u.EventEmitter=u,void 0!==e&&(e.exports=u)},,,,,,,function(e,t){var r=-1,n=1,o=0;function i(e,t,l,f){if(e===t)return e?[[o,e]]:[];if(null!=l){var h=function(e,t,r){var n="number"==typeof r?{index:r,length:0}:r.oldRange,o="number"==typeof r?null:r.newRange,i=e.length,s=t.length;if(0===n.length&&(null===o||0===o.length)){var a=n.index,u=e.slice(0,a),c=e.slice(a),l=o?o.index:null,f=a+s-i;if((null===l||l===f)&&!(f<0||f>s)){var h=t.slice(0,f);if((b=t.slice(f))===c){var d=Math.min(a,f);if((v=u.slice(0,d))===(E=h.slice(0,d)))return p(v,u.slice(d),h.slice(d),c)}}if(null===l||l===a){var y=a,b=(h=t.slice(0,y),t.slice(y));if(h===u){var g=Math.min(i-y,s-y);if((m=c.slice(c.length-g))===(_=b.slice(b.length-g)))return p(u,c.slice(0,c.length-g),b.slice(0,b.length-g),m)}}}if(n.length>0&&o&&0===o.length){var v=e.slice(0,n.index),m=e.slice(n.index+n.length);if(!(s<(d=v.length)+(g=m.length))){var E=t.slice(0,d),_=t.slice(s-g);if(v===E&&m===_)return p(v,e.slice(d,i-g),t.slice(d,s-g),m)}}return null}(e,t,l);if(h)return h}var d=a(e,t),y=e.substring(0,d);d=u(e=e.substring(d),t=t.substring(d));var b=e.substring(e.length-d),g=function(e,t){var c;if(!e)return[[n,t]];if(!t)return[[r,e]];var l=e.length>t.length?e:t,f=e.length>t.length?t:e,h=l.indexOf(f);if(-1!==h)return c=[[n,l.substring(0,h)],[o,f],[n,l.substring(h+f.length)]],e.length>t.length&&(c[0][0]=c[2][0]=r),c;if(1===f.length)return[[r,e],[n,t]];var d=function(e,t){var r=e.length>t.length?e:t,n=e.length>t.length?t:e;if(r.length<4||2*n.length<r.length)return null;function o(e,t,r){for(var n,o,i,s,c=e.substring(r,r+Math.floor(e.length/4)),l=-1,f="";-1!==(l=t.indexOf(c,l+1));){var h=a(e.substring(r),t.substring(l)),d=u(e.substring(0,r),t.substring(0,l));f.length<d+h&&(f=t.substring(l-d,l)+t.substring(l,l+h),n=e.substring(0,r-d),o=e.substring(r+h),i=t.substring(0,l-d),s=t.substring(l+h))}return 2*f.length>=e.length?[n,o,i,s,f]:null}var i,s,c,l,f,h=o(r,n,Math.ceil(r.length/4)),d=o(r,n,Math.ceil(r.length/2));if(!h&&!d)return null;i=d?h&&h[4].length>d[4].length?h:d:h,e.length>t.length?(s=i[0],c=i[1],l=i[2],f=i[3]):(l=i[0],f=i[1],s=i[2],c=i[3]);var p=i[4];return[s,c,l,f,p]}(e,t);if(d){var p=d[0],y=d[1],b=d[2],g=d[3],v=d[4],m=i(p,b),E=i(y,g);return m.concat([[o,v]],E)}return function(e,t){for(var o=e.length,i=t.length,a=Math.ceil((o+i)/2),u=a,c=2*a,l=new Array(c),f=new Array(c),h=0;h<c;h++)l[h]=-1,f[h]=-1;l[u+1]=0,f[u+1]=0;for(var d=o-i,p=d%2!=0,y=0,b=0,g=0,v=0,m=0;m<a;m++){for(var E=-m+y;E<=m-b;E+=2){for(var _=u+E,O=(N=E===-m||E!==m&&l[_-1]<l[_+1]?l[_+1]:l[_-1]+1)-E;N<o&&O<i&&e.charAt(N)===t.charAt(O);)N++,O++;if(l[_]=N,N>o)b+=2;else if(O>i)y+=2;else if(p&&(T=u+d-E)>=0&&T<c&&-1!==f[T]&&N>=(w=o-f[T]))return s(e,t,N,O)}for(var S=-m+g;S<=m-v;S+=2){for(var w,T=u+S,A=(w=S===-m||S!==m&&f[T-1]<f[T+1]?f[T+1]:f[T-1]+1)-S;w<o&&A<i&&e.charAt(o-w-1)===t.charAt(i-A-1);)w++,A++;if(f[T]=w,w>o)v+=2;else if(A>i)g+=2;else if(!p){var N;if((_=u+d-S)>=0&&_<c&&-1!==l[_])if(O=u+(N=l[_])-_,N>=(w=o-w))return s(e,t,N,O)}}}return[[r,e],[n,t]]}(e,t)}(e=e.substring(0,e.length-d),t=t.substring(0,t.length-d));return y&&g.unshift([o,y]),b&&g.push([o,b]),c(g,f),g}function s(e,t,r,n){var o=e.substring(0,r),s=t.substring(0,n),a=e.substring(r),u=t.substring(n),c=i(o,s),l=i(a,u);return c.concat(l)}function a(e,t){if(!e||!t||e.charAt(0)!==t.charAt(0))return 0;for(var r=0,n=Math.min(e.length,t.length),o=n,i=0;r<o;)e.substring(i,o)==t.substring(i,o)?i=r=o:n=o,o=Math.floor((n-r)/2+r);return l(e.charCodeAt(o-1))&&o--,o}function u(e,t){if(!e||!t||e.slice(-1)!==t.slice(-1))return 0;for(var r=0,n=Math.min(e.length,t.length),o=n,i=0;r<o;)e.substring(e.length-o,e.length-i)==t.substring(t.length-o,t.length-i)?i=r=o:n=o,o=Math.floor((n-r)/2+r);return f(e.charCodeAt(e.length-o))&&o--,o}function c(e,t){e.push([o,""]);for(var i,s=0,l=0,f=0,p="",y="";s<e.length;)if(s<e.length-1&&!e[s][1])e.splice(s,1);else switch(e[s][0]){case n:f++,y+=e[s][1],s++;break;case r:l++,p+=e[s][1],s++;break;case o:var b=s-f-l-1;if(t){if(b>=0&&d(e[b][1])){var g=e[b][1].slice(-1);if(e[b][1]=e[b][1].slice(0,-1),p=g+p,y=g+y,!e[b][1]){e.splice(b,1),s--;var v=b-1;e[v]&&e[v][0]===n&&(f++,y=e[v][1]+y,v--),e[v]&&e[v][0]===r&&(l++,p=e[v][1]+p,v--),b=v}}h(e[s][1])&&(g=e[s][1].charAt(0),e[s][1]=e[s][1].slice(1),p+=g,y+=g)}if(s<e.length-1&&!e[s][1]){e.splice(s,1);break}if(p.length>0||y.length>0){p.length>0&&y.length>0&&(0!==(i=a(y,p))&&(b>=0?e[b][1]+=y.substring(0,i):(e.splice(0,0,[o,y.substring(0,i)]),s++),y=y.substring(i),p=p.substring(i)),0!==(i=u(y,p))&&(e[s][1]=y.substring(y.length-i)+e[s][1],y=y.substring(0,y.length-i),p=p.substring(0,p.length-i)));var m=f+l;0===p.length&&0===y.length?(e.splice(s-m,m),s-=m):0===p.length?(e.splice(s-m,m,[n,y]),s=s-m+1):0===y.length?(e.splice(s-m,m,[r,p]),s=s-m+1):(e.splice(s-m,m,[r,p],[n,y]),s=s-m+2)}0!==s&&e[s-1][0]===o?(e[s-1][1]+=e[s][1],e.splice(s,1)):s++,f=0,l=0,p="",y=""}""===e[e.length-1][1]&&e.pop();var E=!1;for(s=1;s<e.length-1;)e[s-1][0]===o&&e[s+1][0]===o&&(e[s][1].substring(e[s][1].length-e[s-1][1].length)===e[s-1][1]?(e[s][1]=e[s-1][1]+e[s][1].substring(0,e[s][1].length-e[s-1][1].length),e[s+1][1]=e[s-1][1]+e[s+1][1],e.splice(s-1,1),E=!0):e[s][1].substring(0,e[s+1][1].length)==e[s+1][1]&&(e[s-1][1]+=e[s+1][1],e[s][1]=e[s][1].substring(e[s+1][1].length)+e[s+1][1],e.splice(s+1,1),E=!0)),s++;E&&c(e,t)}function l(e){return e>=55296&&e<=56319}function f(e){return e>=56320&&e<=57343}function h(e){return f(e.charCodeAt(0))}function d(e){return l(e.charCodeAt(e.length-1))}function p(e,t,i,s){return d(e)||h(s)?null:function(e){for(var t=[],r=0;r<e.length;r++)e[r][1].length>0&&t.push(e[r]);return t}([[o,e],[r,t],[n,i],[o,s]])}function y(e,t,r){return i(e,t,r,!0)}y.INSERT=n,y.DELETE=r,y.EQUAL=o,e.exports=y},function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(r(10)),s=n(r(18));!function(e){e.compose=function(e,t,r){void 0===e&&(e={}),void 0===t&&(t={}),"object"!=typeof e&&(e={}),"object"!=typeof t&&(t={});var n=i.default(t);for(var o in r||(n=Object.keys(n).reduce((function(e,t){return null!=n[t]&&(e[t]=n[t]),e}),{})),e)void 0!==e[o]&&void 0===t[o]&&(n[o]=e[o]);return Object.keys(n).length>0?n:void 0},e.diff=function(e,t){void 0===e&&(e={}),void 0===t&&(t={}),"object"!=typeof e&&(e={}),"object"!=typeof t&&(t={});var r=Object.keys(e).concat(Object.keys(t)).reduce((function(r,n){return s.default(e[n],t[n])||(r[n]=void 0===t[n]?null:t[n]),r}),{});return Object.keys(r).length>0?r:void 0},e.invert=function(e,t){void 0===e&&(e={}),void 0===t&&(t={}),e=e||{};var r=Object.keys(t).reduce((function(r,n){return t[n]!==e[n]&&void 0!==e[n]&&(r[n]=t[n]),r}),{});return Object.keys(e).reduce((function(r,n){return e[n]!==t[n]&&void 0===t[n]&&(r[n]=null),r}),r)},e.transform=function(e,t,r){if(void 0===r&&(r=!1),"object"!=typeof e)return t;if("object"==typeof t){if(!r)return t;var n=Object.keys(t).reduce((function(r,n){return void 0===e[n]&&(r[n]=t[n]),r}),{});return Object.keys(n).length>0?n:void 0}}}(o||(o={})),t.default=o},function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=n(r(42)),i=function(){function e(e){this.ops=e,this.index=0,this.offset=0}return e.prototype.hasNext=function(){return this.peekLength()<1/0},e.prototype.next=function(e){e||(e=1/0);var t=this.ops[this.index];if(t){var r=this.offset,n=o.default.length(t);if(e>=n-r?(e=n-r,this.index+=1,this.offset=0):this.offset+=e,"number"==typeof t.delete)return{delete:e};var i={};return t.attributes&&(i.attributes=t.attributes),"number"==typeof t.retain?i.retain=e:"string"==typeof t.insert?i.insert=t.insert.substr(r,e):i.insert=t.insert,i}return{retain:1/0}},e.prototype.peek=function(){return this.ops[this.index]},e.prototype.peekLength=function(){return this.ops[this.index]?o.default.length(this.ops[this.index])-this.offset:1/0},e.prototype.peekType=function(){return this.ops[this.index]?"number"==typeof this.ops[this.index].delete?"delete":"number"==typeof this.ops[this.index].retain?"retain":"insert":"retain"},e.prototype.rest=function(){if(this.hasNext()){if(0===this.offset)return this.ops.slice(this.index);var e=this.offset,t=this.index,r=this.next(),n=this.ops.slice(this.index);return this.offset=e,this.index=t,[r].concat(n)}return[]},e}();t.default=i},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,r){e.exports=r(36)}]).default},e.exports=t()},928662:(e,t,r)=>{"use strict";r.d(t,{l:()=>o});var n=r(977305);function o(e){return e&&"function"==typeof e[n.s]}},929305:(e,t,r)=>{"use strict";r.d(t,{b:()=>n.b});r(178665);var n=r(444191)},933573:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(11585),o=r(881800);function i(e){var t=(0,o.A)(e,"string");return"symbol"==(0,n.A)(t)?t:t+""}},936791:(e,t,r)=>{"use strict";r.d(t,{k:()=>n});var n=function(){function e(){return Error.call(this),this.message="argument out of range",this.name="ArgumentOutOfRangeError",this}return e.prototype=Object.create(Error.prototype),e}()},937815:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(845212);const o=r(408406);function i(e,t){"function"==typeof e?e(t):e.current=t}function s(e,t){const r=e.ref;return o("string"!=typeof r,"Cannot connect React DnD to an element with an existing string ref. Please convert it to use a callback ref instead, or wrap it into a <span> or <div>. Read more: https://facebook.github.io/react/docs/more-about-refs.html#the-ref-callback-attribute"),r?(0,n.cloneElement)(e,{ref:e=>{i(t,e),r&&i(r,e)}}):(0,n.cloneElement)(e,{ref:t})}},949339:(e,t,r)=>{"use strict";r.d(t,{D:()=>n});class n{constructor(){this.isDisposed=!1}getDisposable(){return this.current}setDisposable(e){const t=this.isDisposed;if(!t){const t=this.current;this.current=e,t&&t.dispose()}t&&e&&e.dispose()}dispose(){if(!this.isDisposed){this.isDisposed=!0;const e=this.current;this.current=void 0,e&&e.dispose()}}}},949359:(e,t,r)=>{"use strict";r.d(t,{b:()=>o});var n=r(237157),o=new(r(799214).q)(n.R)},976373:(e,t,r)=>{"use strict";r.d(t,{B:()=>s});var n=r(998083),o=r(335679),i=r(949359);function s(e,t){return void 0===t&&(t=i.b),function(r){return r.lift(new a(e,t))}}var a=function(){function e(e,t){this.dueTime=e,this.scheduler=t}return e.prototype.call=function(e,t){return t.subscribe(new u(e,this.dueTime,this.scheduler))},e}(),u=function(e){function t(t,r,n){var o=e.call(this,t)||this;return o.dueTime=r,o.scheduler=n,o.debouncedSubscription=null,o.lastValue=null,o.hasValue=!1,o}return n.C6(t,e),t.prototype._next=function(e){this.clearDebounce(),this.lastValue=e,this.hasValue=!0,this.add(this.debouncedSubscription=this.scheduler.schedule(c,this.dueTime,this))},t.prototype._complete=function(){this.debouncedNext(),this.destination.complete()},t.prototype.debouncedNext=function(){if(this.clearDebounce(),this.hasValue){var e=this.lastValue;this.lastValue=null,this.hasValue=!1,this.destination.next(e)}},t.prototype.clearDebounce=function(){var e=this.debouncedSubscription;null!==e&&(this.remove(e),e.unsubscribe(),this.debouncedSubscription=null)},t}(o.v);function c(e){e.debouncedNext()}},977305:(e,t,r)=>{"use strict";r.d(t,{s:()=>n});var n=function(){return"function"==typeof Symbol&&Symbol.observable||"@@observable"}()},983923:(e,t,r)=>{"use strict";r.r(t),r.d(t,{FILE:()=>n,TEXT:()=>i,URL:()=>o});const n="__NATIVE_FILE__",o="__NATIVE_URL__",i="__NATIVE_TEXT__"},990352:(e,t,r)=>{"use strict";r.d(t,{A:()=>f});var n=r(352754),o=r(206040),i=r(178665),s=r(69858);const a=r(408406),u=r(631998);function c(e){switch(e[0]){case"S":return i.z.SOURCE;case"T":return i.z.TARGET;default:a(!1,`Cannot parse handler ID: ${e}`)}}function l(e,t){const r=e.entries();let n=!1;do{const{done:e,value:[,o]}=r.next();if(o===t)return!0;n=e}while(!n);return!1}class f{constructor(e){this.store=e,this.types=new Map,this.dragSources=new Map,this.dropTargets=new Map,this.pinnedSourceId=null,this.pinnedSource=null}addSource(e,t){(0,s.v2)(e),(0,s.Mv)(t);const r=this.addHandler(i.z.SOURCE,e,t);return this.store.dispatch((0,n.pM)(r)),r}addTarget(e,t){(0,s.v2)(e,!0),(0,s.kV)(t);const r=this.addHandler(i.z.TARGET,e,t);return this.store.dispatch((0,n.z9)(r)),r}containsHandler(e){return l(this.dragSources,e)||l(this.dropTargets,e)}getSource(e,t=!1){a(this.isSourceId(e),"Expected a valid source ID.");return t&&e===this.pinnedSourceId?this.pinnedSource:this.dragSources.get(e)}getTarget(e){return a(this.isTargetId(e),"Expected a valid target ID."),this.dropTargets.get(e)}getSourceType(e){return a(this.isSourceId(e),"Expected a valid source ID."),this.types.get(e)}getTargetType(e){return a(this.isTargetId(e),"Expected a valid target ID."),this.types.get(e)}isSourceId(e){return c(e)===i.z.SOURCE}isTargetId(e){return c(e)===i.z.TARGET}removeSource(e){a(this.getSource(e),"Expected an existing source."),this.store.dispatch((0,n.sn)(e)),u((()=>{this.dragSources.delete(e),this.types.delete(e)}))}removeTarget(e){a(this.getTarget(e),"Expected an existing target."),this.store.dispatch((0,n.iS)(e)),this.dropTargets.delete(e),this.types.delete(e)}pinSource(e){const t=this.getSource(e);a(t,"Expected an existing source."),this.pinnedSourceId=e,this.pinnedSource=t}unpinSource(){a(this.pinnedSource,"No source is pinned at the time."),this.pinnedSourceId=null,this.pinnedSource=null}addHandler(e,t,r){const n=function(e){const t=(0,o.A)().toString();switch(e){case i.z.SOURCE:return`S${t}`;case i.z.TARGET:return`T${t}`;default:throw new Error(`Unknown Handler Role: ${e}`)}}(e);return this.types.set(n,t),e===i.z.SOURCE?this.dragSources.set(n,r):e===i.z.TARGET&&this.dropTargets.set(n,r),n}}},997752:(e,t,r)=>{"use strict";r.d(t,{y:()=>a});var n=r(375527),o=r(47911),i=r(387904),s=r(818334),a=function(){function e(e){this.closed=!1,this._parentOrParents=null,this._subscriptions=null,e&&(this._ctorUnsubscribe=!0,this._unsubscribe=e)}var t;return e.prototype.unsubscribe=function(){var t;if(!this.closed){var r=this,a=r._parentOrParents,c=r._ctorUnsubscribe,l=r._unsubscribe,f=r._subscriptions;if(this.closed=!0,this._parentOrParents=null,this._subscriptions=null,a instanceof e)a.remove(this);else if(null!==a)for(var h=0;h<a.length;++h){a[h].remove(this)}if((0,i.T)(l)){c&&(this._unsubscribe=void 0);try{l.call(this)}catch(y){t=y instanceof s.Z?u(y.errors):[y]}}if((0,n.c)(f)){h=-1;for(var d=f.length;++h<d;){var p=f[h];if((0,o.G)(p))try{p.unsubscribe()}catch(y){t=t||[],y instanceof s.Z?t=t.concat(u(y.errors)):t.push(y)}}}if(t)throw new s.Z(t)}},e.prototype.add=function(t){var r=t;if(!t)return e.EMPTY;switch(typeof t){case"function":r=new e(t);case"object":if(r===this||r.closed||"function"!=typeof r.unsubscribe)return r;if(this.closed)return r.unsubscribe(),r;if(!(r instanceof e)){var n=r;(r=new e)._subscriptions=[n]}break;default:throw new Error("unrecognized teardown "+t+" added to Subscription.")}var o=r._parentOrParents;if(null===o)r._parentOrParents=this;else if(o instanceof e){if(o===this)return r;r._parentOrParents=[o,this]}else{if(-1!==o.indexOf(this))return r;o.push(this)}var i=this._subscriptions;return null===i?this._subscriptions=[r]:i.push(r),r},e.prototype.remove=function(e){var t=this._subscriptions;if(t){var r=t.indexOf(e);-1!==r&&t.splice(r,1)}},e.EMPTY=((t=new e).closed=!0,t),e}();function u(e){return e.reduce((function(e,t){return e.concat(t instanceof s.Z?t.errors:t)}),[])}},998083:(e,t,r)=>{"use strict";r.d(t,{C6:()=>o});var n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},n(e,t)};function o(e,t){function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}}}]);
//# sourceMappingURL=sourcemaps/06e16e094c4f1a7c.vendor.js.map