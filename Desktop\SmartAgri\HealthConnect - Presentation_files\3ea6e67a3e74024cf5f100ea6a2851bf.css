@font-face {unicode-range: U+0-2138,U+213a-fffd; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: normal; src: url(https://font-public.canva.com/_fb/0/05.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/05.woff) format("woff"), url(https://font-public.canva.com/_fb/0/05.ttf) format("truetype")}
@font-face {unicode-range: U+0-2138,U+213a-fffd; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: normal; src: url(https://font-public.canva.com/_fb/0/02.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/02.woff) format("woff"), url(https://font-public.canva.com/_fb/0/02.ttf) format("truetype")}
@font-face {unicode-range: U+0-2138,U+213a-fffd; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: italic; src: url(https://font-public.canva.com/_fb/0/04.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/04.woff) format("woff"), url(https://font-public.canva.com/_fb/0/04.ttf) format("truetype")}
@font-face {unicode-range: U+0-2138,U+213a-fffd; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: italic; src: url(https://font-public.canva.com/_fb/0/03.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/03.woff) format("woff"), url(https://font-public.canva.com/_fb/0/03.ttf) format("truetype")}
@font-face {unicode-range: U+0-2138,U+213a-fffd; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: normal; src: url(https://font-public.canva.com/_fb/0/05.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/05.woff) format("woff"), url(https://font-public.canva.com/_fb/0/05.ttf) format("truetype")}
@font-face {unicode-range: U+0-2138,U+213a-fffd; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: italic; src: url(https://font-public.canva.com/_fb/0/04.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/04.woff) format("woff"), url(https://font-public.canva.com/_fb/0/04.ttf) format("truetype")}
@font-face {unicode-range: U+0-2138,U+213a-fffd; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: normal; src: url(https://font-public.canva.com/_fb/0/05.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/05.woff) format("woff"), url(https://font-public.canva.com/_fb/0/05.ttf) format("truetype")}
@font-face {unicode-range: U+0-2138,U+213a-fffd; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: italic; src: url(https://font-public.canva.com/_fb/0/04.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/04.woff) format("woff"), url(https://font-public.canva.com/_fb/0/04.ttf) format("truetype")}
@font-face {unicode-range: U+0-2138,U+213a-fffd; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: normal; src: url(https://font-public.canva.com/_fb/0/05.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/05.woff) format("woff"), url(https://font-public.canva.com/_fb/0/05.ttf) format("truetype")}
@font-face {unicode-range: U+0-2138,U+213a-fffd; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: italic; src: url(https://font-public.canva.com/_fb/0/04.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/04.woff) format("woff"), url(https://font-public.canva.com/_fb/0/04.ttf) format("truetype")}
@font-face {unicode-range: U+0-2138,U+213a-fffd; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: normal; src: url(https://font-public.canva.com/_fb/0/05.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/05.woff) format("woff"), url(https://font-public.canva.com/_fb/0/05.ttf) format("truetype")}
@font-face {unicode-range: U+0-2138,U+213a-fffd; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: italic; src: url(https://font-public.canva.com/_fb/0/04.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/04.woff) format("woff"), url(https://font-public.canva.com/_fb/0/04.ttf) format("truetype")}
@font-face {unicode-range: U+0-2138,U+213a-fffd; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: normal; src: url(https://font-public.canva.com/_fb/0/02.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/02.woff) format("woff"), url(https://font-public.canva.com/_fb/0/02.ttf) format("truetype")}
@font-face {unicode-range: U+0-2138,U+213a-fffd; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: italic; src: url(https://font-public.canva.com/_fb/0/03.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/03.woff) format("woff"), url(https://font-public.canva.com/_fb/0/03.ttf) format("truetype")}
@font-face {unicode-range: U+0-2138,U+213a-fffd; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: normal; src: url(https://font-public.canva.com/_fb/0/02.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/02.woff) format("woff"), url(https://font-public.canva.com/_fb/0/02.ttf) format("truetype")}
@font-face {unicode-range: U+0-2138,U+213a-fffd; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: italic; src: url(https://font-public.canva.com/_fb/0/03.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/03.woff) format("woff"), url(https://font-public.canva.com/_fb/0/03.ttf) format("truetype")}
@font-face {unicode-range: U+0-2138,U+213a-fffd; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: normal; src: url(https://font-public.canva.com/_fb/0/02.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/02.woff) format("woff"), url(https://font-public.canva.com/_fb/0/02.ttf) format("truetype")}
@font-face {unicode-range: U+0-2138,U+213a-fffd; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: italic; src: url(https://font-public.canva.com/_fb/0/03.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/03.woff) format("woff"), url(https://font-public.canva.com/_fb/0/03.ttf) format("truetype")}
@font-face {unicode-range: U+2190-2b1a; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: normal; src: url(https://font-public.canva.com/_fb/0/7E.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/7E.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7E-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7E.otf) format("truetype")}
@font-face {unicode-range: U+2190-2b1a; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: normal; src: url(https://font-public.canva.com/_fb/0/7D.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/7D.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7D-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7D.otf) format("truetype")}
@font-face {unicode-range: U+2190-2b1a; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: italic; src: url(https://font-public.canva.com/_fb/0/7E.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/7E.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7E-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7E.otf) format("truetype")}
@font-face {unicode-range: U+2190-2b1a; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: italic; src: url(https://font-public.canva.com/_fb/0/7D.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/7D.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7D-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7D.otf) format("truetype")}
@font-face {unicode-range: U+2190-2b1a; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: normal; src: url(https://font-public.canva.com/_fb/0/7E.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/7E.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7E-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7E.otf) format("truetype")}
@font-face {unicode-range: U+2190-2b1a; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: italic; src: url(https://font-public.canva.com/_fb/0/7E.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/7E.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7E-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7E.otf) format("truetype")}
@font-face {unicode-range: U+2190-2b1a; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: normal; src: url(https://font-public.canva.com/_fb/0/7E.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/7E.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7E-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7E.otf) format("truetype")}
@font-face {unicode-range: U+2190-2b1a; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: italic; src: url(https://font-public.canva.com/_fb/0/7E.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/7E.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7E-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7E.otf) format("truetype")}
@font-face {unicode-range: U+2190-2b1a; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: normal; src: url(https://font-public.canva.com/_fb/0/7E.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/7E.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7E-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7E.otf) format("truetype")}
@font-face {unicode-range: U+2190-2b1a; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: italic; src: url(https://font-public.canva.com/_fb/0/7E.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/7E.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7E-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7E.otf) format("truetype")}
@font-face {unicode-range: U+2190-2b1a; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: normal; src: url(https://font-public.canva.com/_fb/0/7E.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/7E.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7E-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7E.otf) format("truetype")}
@font-face {unicode-range: U+2190-2b1a; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: italic; src: url(https://font-public.canva.com/_fb/0/7E.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/7E.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7E-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7E.otf) format("truetype")}
@font-face {unicode-range: U+2190-2b1a; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: normal; src: url(https://font-public.canva.com/_fb/0/7D.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/7D.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7D-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7D.otf) format("truetype")}
@font-face {unicode-range: U+2190-2b1a; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: italic; src: url(https://font-public.canva.com/_fb/0/7D.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/7D.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7D-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7D.otf) format("truetype")}
@font-face {unicode-range: U+2190-2b1a; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: normal; src: url(https://font-public.canva.com/_fb/0/7D.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/7D.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7D-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7D.otf) format("truetype")}
@font-face {unicode-range: U+2190-2b1a; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: italic; src: url(https://font-public.canva.com/_fb/0/7D.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/7D.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7D-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7D.otf) format("truetype")}
@font-face {unicode-range: U+2190-2b1a; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: normal; src: url(https://font-public.canva.com/_fb/0/7D.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/7D.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7D-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7D.otf) format("truetype")}
@font-face {unicode-range: U+2190-2b1a; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: italic; src: url(https://font-public.canva.com/_fb/0/7D.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/7D.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7D-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/7D.otf) format("truetype")}
@font-face {unicode-range: U+1100-11ff,U+2002-2003,U+20dd-20de,U+2160-217b,U+2e80-9fd0,U+a960-a97c,U+ac00-fa6d,U+fe10-fe19,U+fe30-fe6b,U+ff01-ffee,U+1f100-2f9f4; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: normal; src: url(https://font-public.canva.com/_fb/0/17.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/17.woff) format("woff"), url(https://font-public.canva.com/_fb/0/17-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/17.otf) format("truetype")}
@font-face {unicode-range: U+1100-11ff,U+2002-2003,U+20dd-20de,U+2160-217b,U+2e80-9fd0,U+a960-a97c,U+ac00-fa6d,U+fe10-fe19,U+fe30-fe6b,U+ff01-ffee,U+1f100-2f9f4; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: normal; src: url(https://font-public.canva.com/_fb/0/16.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/16.woff) format("woff"), url(https://font-public.canva.com/_fb/0/16-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/16.otf) format("truetype")}
@font-face {unicode-range: U+1100-11ff,U+2002-2003,U+20dd-20de,U+2160-217b,U+2e80-9fd0,U+a960-a97c,U+ac00-fa6d,U+fe10-fe19,U+fe30-fe6b,U+ff01-ffee,U+1f100-2f9f4; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: italic; src: url(https://font-public.canva.com/_fb/0/17.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/17.woff) format("woff"), url(https://font-public.canva.com/_fb/0/17-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/17.otf) format("truetype")}
@font-face {unicode-range: U+1100-11ff,U+2002-2003,U+20dd-20de,U+2160-217b,U+2e80-9fd0,U+a960-a97c,U+ac00-fa6d,U+fe10-fe19,U+fe30-fe6b,U+ff01-ffee,U+1f100-2f9f4; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: italic; src: url(https://font-public.canva.com/_fb/0/16.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/16.woff) format("woff"), url(https://font-public.canva.com/_fb/0/16-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/16.otf) format("truetype")}
@font-face {unicode-range: U+1100-11ff,U+2002-2003,U+20dd-20de,U+2160-217b,U+2e80-9fd0,U+a960-a97c,U+ac00-fa6d,U+fe10-fe19,U+fe30-fe6b,U+ff01-ffee,U+1f100-2f9f4; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: normal; src: url(https://font-public.canva.com/_fb/0/17.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/17.woff) format("woff"), url(https://font-public.canva.com/_fb/0/17-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/17.otf) format("truetype")}
@font-face {unicode-range: U+1100-11ff,U+2002-2003,U+20dd-20de,U+2160-217b,U+2e80-9fd0,U+a960-a97c,U+ac00-fa6d,U+fe10-fe19,U+fe30-fe6b,U+ff01-ffee,U+1f100-2f9f4; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: italic; src: url(https://font-public.canva.com/_fb/0/17.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/17.woff) format("woff"), url(https://font-public.canva.com/_fb/0/17-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/17.otf) format("truetype")}
@font-face {unicode-range: U+1100-11ff,U+2002-2003,U+20dd-20de,U+2160-217b,U+2e80-9fd0,U+a960-a97c,U+ac00-fa6d,U+fe10-fe19,U+fe30-fe6b,U+ff01-ffee,U+1f100-2f9f4; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: normal; src: url(https://font-public.canva.com/_fb/0/17.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/17.woff) format("woff"), url(https://font-public.canva.com/_fb/0/17-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/17.otf) format("truetype")}
@font-face {unicode-range: U+1100-11ff,U+2002-2003,U+20dd-20de,U+2160-217b,U+2e80-9fd0,U+a960-a97c,U+ac00-fa6d,U+fe10-fe19,U+fe30-fe6b,U+ff01-ffee,U+1f100-2f9f4; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: italic; src: url(https://font-public.canva.com/_fb/0/17.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/17.woff) format("woff"), url(https://font-public.canva.com/_fb/0/17-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/17.otf) format("truetype")}
@font-face {unicode-range: U+1100-11ff,U+2002-2003,U+20dd-20de,U+2160-217b,U+2e80-9fd0,U+a960-a97c,U+ac00-fa6d,U+fe10-fe19,U+fe30-fe6b,U+ff01-ffee,U+1f100-2f9f4; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: normal; src: url(https://font-public.canva.com/_fb/0/17.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/17.woff) format("woff"), url(https://font-public.canva.com/_fb/0/17-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/17.otf) format("truetype")}
@font-face {unicode-range: U+1100-11ff,U+2002-2003,U+20dd-20de,U+2160-217b,U+2e80-9fd0,U+a960-a97c,U+ac00-fa6d,U+fe10-fe19,U+fe30-fe6b,U+ff01-ffee,U+1f100-2f9f4; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: italic; src: url(https://font-public.canva.com/_fb/0/17.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/17.woff) format("woff"), url(https://font-public.canva.com/_fb/0/17-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/17.otf) format("truetype")}
@font-face {unicode-range: U+1100-11ff,U+2002-2003,U+20dd-20de,U+2160-217b,U+2e80-9fd0,U+a960-a97c,U+ac00-fa6d,U+fe10-fe19,U+fe30-fe6b,U+ff01-ffee,U+1f100-2f9f4; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: normal; src: url(https://font-public.canva.com/_fb/0/17.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/17.woff) format("woff"), url(https://font-public.canva.com/_fb/0/17-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/17.otf) format("truetype")}
@font-face {unicode-range: U+1100-11ff,U+2002-2003,U+20dd-20de,U+2160-217b,U+2e80-9fd0,U+a960-a97c,U+ac00-fa6d,U+fe10-fe19,U+fe30-fe6b,U+ff01-ffee,U+1f100-2f9f4; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: italic; src: url(https://font-public.canva.com/_fb/0/17.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/17.woff) format("woff"), url(https://font-public.canva.com/_fb/0/17-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/17.otf) format("truetype")}
@font-face {unicode-range: U+1100-11ff,U+2002-2003,U+20dd-20de,U+2160-217b,U+2e80-9fd0,U+a960-a97c,U+ac00-fa6d,U+fe10-fe19,U+fe30-fe6b,U+ff01-ffee,U+1f100-2f9f4; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: normal; src: url(https://font-public.canva.com/_fb/0/16.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/16.woff) format("woff"), url(https://font-public.canva.com/_fb/0/16-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/16.otf) format("truetype")}
@font-face {unicode-range: U+1100-11ff,U+2002-2003,U+20dd-20de,U+2160-217b,U+2e80-9fd0,U+a960-a97c,U+ac00-fa6d,U+fe10-fe19,U+fe30-fe6b,U+ff01-ffee,U+1f100-2f9f4; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: italic; src: url(https://font-public.canva.com/_fb/0/16.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/16.woff) format("woff"), url(https://font-public.canva.com/_fb/0/16-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/16.otf) format("truetype")}
@font-face {unicode-range: U+1100-11ff,U+2002-2003,U+20dd-20de,U+2160-217b,U+2e80-9fd0,U+a960-a97c,U+ac00-fa6d,U+fe10-fe19,U+fe30-fe6b,U+ff01-ffee,U+1f100-2f9f4; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: normal; src: url(https://font-public.canva.com/_fb/0/16.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/16.woff) format("woff"), url(https://font-public.canva.com/_fb/0/16-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/16.otf) format("truetype")}
@font-face {unicode-range: U+1100-11ff,U+2002-2003,U+20dd-20de,U+2160-217b,U+2e80-9fd0,U+a960-a97c,U+ac00-fa6d,U+fe10-fe19,U+fe30-fe6b,U+ff01-ffee,U+1f100-2f9f4; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: italic; src: url(https://font-public.canva.com/_fb/0/16.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/16.woff) format("woff"), url(https://font-public.canva.com/_fb/0/16-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/16.otf) format("truetype")}
@font-face {unicode-range: U+1100-11ff,U+2002-2003,U+20dd-20de,U+2160-217b,U+2e80-9fd0,U+a960-a97c,U+ac00-fa6d,U+fe10-fe19,U+fe30-fe6b,U+ff01-ffee,U+1f100-2f9f4; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: normal; src: url(https://font-public.canva.com/_fb/0/16.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/16.woff) format("woff"), url(https://font-public.canva.com/_fb/0/16-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/16.otf) format("truetype")}
@font-face {unicode-range: U+1100-11ff,U+2002-2003,U+20dd-20de,U+2160-217b,U+2e80-9fd0,U+a960-a97c,U+ac00-fa6d,U+fe10-fe19,U+fe30-fe6b,U+ff01-ffee,U+1f100-2f9f4; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: italic; src: url(https://font-public.canva.com/_fb/0/16.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/16.woff) format("woff"), url(https://font-public.canva.com/_fb/0/16-quadratic.woff) format("woff"), url(https://font-public.canva.com/_fb/0/16.otf) format("truetype")}
@font-face {unicode-range: U+d82-df4,U+111e1-111f4; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: normal; src: url(https://font-public.canva.com/_fb/0/61.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/61.woff) format("woff"), url(https://font-public.canva.com/_fb/0/61.ttf) format("truetype")}
@font-face {unicode-range: U+d82-df4,U+111e1-111f4; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: normal; src: url(https://font-public.canva.com/_fb/0/60.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/60.woff) format("woff"), url(https://font-public.canva.com/_fb/0/60.ttf) format("truetype")}
@font-face {unicode-range: U+d82-df4,U+111e1-111f4; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: italic; src: url(https://font-public.canva.com/_fb/0/61.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/61.woff) format("woff"), url(https://font-public.canva.com/_fb/0/61.ttf) format("truetype")}
@font-face {unicode-range: U+d82-df4,U+111e1-111f4; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: italic; src: url(https://font-public.canva.com/_fb/0/60.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/60.woff) format("woff"), url(https://font-public.canva.com/_fb/0/60.ttf) format("truetype")}
@font-face {unicode-range: U+d82-df4,U+111e1-111f4; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: normal; src: url(https://font-public.canva.com/_fb/0/61.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/61.woff) format("woff"), url(https://font-public.canva.com/_fb/0/61.ttf) format("truetype")}
@font-face {unicode-range: U+d82-df4,U+111e1-111f4; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: italic; src: url(https://font-public.canva.com/_fb/0/61.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/61.woff) format("woff"), url(https://font-public.canva.com/_fb/0/61.ttf) format("truetype")}
@font-face {unicode-range: U+d82-df4,U+111e1-111f4; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: normal; src: url(https://font-public.canva.com/_fb/0/61.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/61.woff) format("woff"), url(https://font-public.canva.com/_fb/0/61.ttf) format("truetype")}
@font-face {unicode-range: U+d82-df4,U+111e1-111f4; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: italic; src: url(https://font-public.canva.com/_fb/0/61.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/61.woff) format("woff"), url(https://font-public.canva.com/_fb/0/61.ttf) format("truetype")}
@font-face {unicode-range: U+d82-df4,U+111e1-111f4; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: normal; src: url(https://font-public.canva.com/_fb/0/61.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/61.woff) format("woff"), url(https://font-public.canva.com/_fb/0/61.ttf) format("truetype")}
@font-face {unicode-range: U+d82-df4,U+111e1-111f4; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: italic; src: url(https://font-public.canva.com/_fb/0/61.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/61.woff) format("woff"), url(https://font-public.canva.com/_fb/0/61.ttf) format("truetype")}
@font-face {unicode-range: U+d82-df4,U+111e1-111f4; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: normal; src: url(https://font-public.canva.com/_fb/0/61.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/61.woff) format("woff"), url(https://font-public.canva.com/_fb/0/61.ttf) format("truetype")}
@font-face {unicode-range: U+d82-df4,U+111e1-111f4; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: italic; src: url(https://font-public.canva.com/_fb/0/61.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/61.woff) format("woff"), url(https://font-public.canva.com/_fb/0/61.ttf) format("truetype")}
@font-face {unicode-range: U+d82-df4,U+111e1-111f4; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: normal; src: url(https://font-public.canva.com/_fb/0/60.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/60.woff) format("woff"), url(https://font-public.canva.com/_fb/0/60.ttf) format("truetype")}
@font-face {unicode-range: U+d82-df4,U+111e1-111f4; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: italic; src: url(https://font-public.canva.com/_fb/0/60.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/60.woff) format("woff"), url(https://font-public.canva.com/_fb/0/60.ttf) format("truetype")}
@font-face {unicode-range: U+d82-df4,U+111e1-111f4; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: normal; src: url(https://font-public.canva.com/_fb/0/60.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/60.woff) format("woff"), url(https://font-public.canva.com/_fb/0/60.ttf) format("truetype")}
@font-face {unicode-range: U+d82-df4,U+111e1-111f4; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: italic; src: url(https://font-public.canva.com/_fb/0/60.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/60.woff) format("woff"), url(https://font-public.canva.com/_fb/0/60.ttf) format("truetype")}
@font-face {unicode-range: U+d82-df4,U+111e1-111f4; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: normal; src: url(https://font-public.canva.com/_fb/0/60.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/60.woff) format("woff"), url(https://font-public.canva.com/_fb/0/60.ttf) format("truetype")}
@font-face {unicode-range: U+d82-df4,U+111e1-111f4; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: italic; src: url(https://font-public.canva.com/_fb/0/60.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/60.woff) format("woff"), url(https://font-public.canva.com/_fb/0/60.ttf) format("truetype")}
@font-face {unicode-range: U+900-97f,U+1cd0-1cf9,U+a830-a8fd; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: normal; src: url(https://font-public.canva.com/_fb/0/24.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/24.woff) format("woff"), url(https://font-public.canva.com/_fb/0/24.ttf) format("truetype")}
@font-face {unicode-range: U+900-97f,U+1cd0-1cf9,U+a830-a8fd; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: normal; src: url(https://font-public.canva.com/_fb/0/23.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/23.woff) format("woff"), url(https://font-public.canva.com/_fb/0/23.ttf) format("truetype")}
@font-face {unicode-range: U+900-97f,U+1cd0-1cf9,U+a830-a8fd; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: italic; src: url(https://font-public.canva.com/_fb/0/24.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/24.woff) format("woff"), url(https://font-public.canva.com/_fb/0/24.ttf) format("truetype")}
@font-face {unicode-range: U+900-97f,U+1cd0-1cf9,U+a830-a8fd; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: italic; src: url(https://font-public.canva.com/_fb/0/23.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/23.woff) format("woff"), url(https://font-public.canva.com/_fb/0/23.ttf) format("truetype")}
@font-face {unicode-range: U+900-97f,U+1cd0-1cf9,U+a830-a8fd; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: normal; src: url(https://font-public.canva.com/_fb/0/24.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/24.woff) format("woff"), url(https://font-public.canva.com/_fb/0/24.ttf) format("truetype")}
@font-face {unicode-range: U+900-97f,U+1cd0-1cf9,U+a830-a8fd; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: italic; src: url(https://font-public.canva.com/_fb/0/24.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/24.woff) format("woff"), url(https://font-public.canva.com/_fb/0/24.ttf) format("truetype")}
@font-face {unicode-range: U+900-97f,U+1cd0-1cf9,U+a830-a8fd; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: normal; src: url(https://font-public.canva.com/_fb/0/24.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/24.woff) format("woff"), url(https://font-public.canva.com/_fb/0/24.ttf) format("truetype")}
@font-face {unicode-range: U+900-97f,U+1cd0-1cf9,U+a830-a8fd; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: italic; src: url(https://font-public.canva.com/_fb/0/24.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/24.woff) format("woff"), url(https://font-public.canva.com/_fb/0/24.ttf) format("truetype")}
@font-face {unicode-range: U+900-97f,U+1cd0-1cf9,U+a830-a8fd; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: normal; src: url(https://font-public.canva.com/_fb/0/24.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/24.woff) format("woff"), url(https://font-public.canva.com/_fb/0/24.ttf) format("truetype")}
@font-face {unicode-range: U+900-97f,U+1cd0-1cf9,U+a830-a8fd; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: italic; src: url(https://font-public.canva.com/_fb/0/24.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/24.woff) format("woff"), url(https://font-public.canva.com/_fb/0/24.ttf) format("truetype")}
@font-face {unicode-range: U+900-97f,U+1cd0-1cf9,U+a830-a8fd; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: normal; src: url(https://font-public.canva.com/_fb/0/24.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/24.woff) format("woff"), url(https://font-public.canva.com/_fb/0/24.ttf) format("truetype")}
@font-face {unicode-range: U+900-97f,U+1cd0-1cf9,U+a830-a8fd; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: italic; src: url(https://font-public.canva.com/_fb/0/24.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/24.woff) format("woff"), url(https://font-public.canva.com/_fb/0/24.ttf) format("truetype")}
@font-face {unicode-range: U+900-97f,U+1cd0-1cf9,U+a830-a8fd; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: normal; src: url(https://font-public.canva.com/_fb/0/23.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/23.woff) format("woff"), url(https://font-public.canva.com/_fb/0/23.ttf) format("truetype")}
@font-face {unicode-range: U+900-97f,U+1cd0-1cf9,U+a830-a8fd; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: italic; src: url(https://font-public.canva.com/_fb/0/23.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/23.woff) format("woff"), url(https://font-public.canva.com/_fb/0/23.ttf) format("truetype")}
@font-face {unicode-range: U+900-97f,U+1cd0-1cf9,U+a830-a8fd; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: normal; src: url(https://font-public.canva.com/_fb/0/23.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/23.woff) format("woff"), url(https://font-public.canva.com/_fb/0/23.ttf) format("truetype")}
@font-face {unicode-range: U+900-97f,U+1cd0-1cf9,U+a830-a8fd; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: italic; src: url(https://font-public.canva.com/_fb/0/23.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/23.woff) format("woff"), url(https://font-public.canva.com/_fb/0/23.ttf) format("truetype")}
@font-face {unicode-range: U+900-97f,U+1cd0-1cf9,U+a830-a8fd; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: normal; src: url(https://font-public.canva.com/_fb/0/23.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/23.woff) format("woff"), url(https://font-public.canva.com/_fb/0/23.ttf) format("truetype")}
@font-face {unicode-range: U+900-97f,U+1cd0-1cf9,U+a830-a8fd; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: italic; src: url(https://font-public.canva.com/_fb/0/23.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/23.woff) format("woff"), url(https://font-public.canva.com/_fb/0/23.ttf) format("truetype")}
@font-face {unicode-range: U+1000-109f,U+a9e0-aa7f; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: normal; src: url(https://font-public.canva.com/_fb/0/4C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/4C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/4C.ttf) format("truetype")}
@font-face {unicode-range: U+1000-109f,U+a9e0-aa7f; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: normal; src: url(https://font-public.canva.com/_fb/0/4B.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/4B.woff) format("woff"), url(https://font-public.canva.com/_fb/0/4B.ttf) format("truetype")}
@font-face {unicode-range: U+1000-109f,U+a9e0-aa7f; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: italic; src: url(https://font-public.canva.com/_fb/0/4C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/4C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/4C.ttf) format("truetype")}
@font-face {unicode-range: U+1000-109f,U+a9e0-aa7f; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: italic; src: url(https://font-public.canva.com/_fb/0/4B.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/4B.woff) format("woff"), url(https://font-public.canva.com/_fb/0/4B.ttf) format("truetype")}
@font-face {unicode-range: U+1000-109f,U+a9e0-aa7f; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: normal; src: url(https://font-public.canva.com/_fb/0/4C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/4C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/4C.ttf) format("truetype")}
@font-face {unicode-range: U+1000-109f,U+a9e0-aa7f; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: italic; src: url(https://font-public.canva.com/_fb/0/4C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/4C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/4C.ttf) format("truetype")}
@font-face {unicode-range: U+1000-109f,U+a9e0-aa7f; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: normal; src: url(https://font-public.canva.com/_fb/0/4C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/4C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/4C.ttf) format("truetype")}
@font-face {unicode-range: U+1000-109f,U+a9e0-aa7f; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: italic; src: url(https://font-public.canva.com/_fb/0/4C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/4C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/4C.ttf) format("truetype")}
@font-face {unicode-range: U+1000-109f,U+a9e0-aa7f; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: normal; src: url(https://font-public.canva.com/_fb/0/4C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/4C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/4C.ttf) format("truetype")}
@font-face {unicode-range: U+1000-109f,U+a9e0-aa7f; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: italic; src: url(https://font-public.canva.com/_fb/0/4C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/4C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/4C.ttf) format("truetype")}
@font-face {unicode-range: U+1000-109f,U+a9e0-aa7f; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: normal; src: url(https://font-public.canva.com/_fb/0/4C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/4C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/4C.ttf) format("truetype")}
@font-face {unicode-range: U+1000-109f,U+a9e0-aa7f; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: italic; src: url(https://font-public.canva.com/_fb/0/4C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/4C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/4C.ttf) format("truetype")}
@font-face {unicode-range: U+1000-109f,U+a9e0-aa7f; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: normal; src: url(https://font-public.canva.com/_fb/0/4B.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/4B.woff) format("woff"), url(https://font-public.canva.com/_fb/0/4B.ttf) format("truetype")}
@font-face {unicode-range: U+1000-109f,U+a9e0-aa7f; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: italic; src: url(https://font-public.canva.com/_fb/0/4B.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/4B.woff) format("woff"), url(https://font-public.canva.com/_fb/0/4B.ttf) format("truetype")}
@font-face {unicode-range: U+1000-109f,U+a9e0-aa7f; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: normal; src: url(https://font-public.canva.com/_fb/0/4B.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/4B.woff) format("woff"), url(https://font-public.canva.com/_fb/0/4B.ttf) format("truetype")}
@font-face {unicode-range: U+1000-109f,U+a9e0-aa7f; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: italic; src: url(https://font-public.canva.com/_fb/0/4B.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/4B.woff) format("woff"), url(https://font-public.canva.com/_fb/0/4B.ttf) format("truetype")}
@font-face {unicode-range: U+1000-109f,U+a9e0-aa7f; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: normal; src: url(https://font-public.canva.com/_fb/0/4B.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/4B.woff) format("woff"), url(https://font-public.canva.com/_fb/0/4B.ttf) format("truetype")}
@font-face {unicode-range: U+1000-109f,U+a9e0-aa7f; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: italic; src: url(https://font-public.canva.com/_fb/0/4B.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/4B.woff) format("woff"), url(https://font-public.canva.com/_fb/0/4B.ttf) format("truetype")}
@font-face {unicode-range: U+600-8ff,U+fb50-fdfd,U+fe70-fefc; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: normal; src: url(https://font-public.canva.com/_fb/0/0A.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/0A.woff) format("woff"), url(https://font-public.canva.com/_fb/0/0A.ttf) format("truetype")}
@font-face {unicode-range: U+600-8ff,U+fb50-fdfd,U+fe70-fefc; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: normal; src: url(https://font-public.canva.com/_fb/0/09.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/09.woff) format("woff"), url(https://font-public.canva.com/_fb/0/09.ttf) format("truetype")}
@font-face {unicode-range: U+600-8ff,U+fb50-fdfd,U+fe70-fefc; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: italic; src: url(https://font-public.canva.com/_fb/0/0A.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/0A.woff) format("woff"), url(https://font-public.canva.com/_fb/0/0A.ttf) format("truetype")}
@font-face {unicode-range: U+600-8ff,U+fb50-fdfd,U+fe70-fefc; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: italic; src: url(https://font-public.canva.com/_fb/0/09.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/09.woff) format("woff"), url(https://font-public.canva.com/_fb/0/09.ttf) format("truetype")}
@font-face {unicode-range: U+600-8ff,U+fb50-fdfd,U+fe70-fefc; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: normal; src: url(https://font-public.canva.com/_fb/0/0A.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/0A.woff) format("woff"), url(https://font-public.canva.com/_fb/0/0A.ttf) format("truetype")}
@font-face {unicode-range: U+600-8ff,U+fb50-fdfd,U+fe70-fefc; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: italic; src: url(https://font-public.canva.com/_fb/0/0A.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/0A.woff) format("woff"), url(https://font-public.canva.com/_fb/0/0A.ttf) format("truetype")}
@font-face {unicode-range: U+600-8ff,U+fb50-fdfd,U+fe70-fefc; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: normal; src: url(https://font-public.canva.com/_fb/0/0A.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/0A.woff) format("woff"), url(https://font-public.canva.com/_fb/0/0A.ttf) format("truetype")}
@font-face {unicode-range: U+600-8ff,U+fb50-fdfd,U+fe70-fefc; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: italic; src: url(https://font-public.canva.com/_fb/0/0A.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/0A.woff) format("woff"), url(https://font-public.canva.com/_fb/0/0A.ttf) format("truetype")}
@font-face {unicode-range: U+600-8ff,U+fb50-fdfd,U+fe70-fefc; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: normal; src: url(https://font-public.canva.com/_fb/0/0A.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/0A.woff) format("woff"), url(https://font-public.canva.com/_fb/0/0A.ttf) format("truetype")}
@font-face {unicode-range: U+600-8ff,U+fb50-fdfd,U+fe70-fefc; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: italic; src: url(https://font-public.canva.com/_fb/0/0A.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/0A.woff) format("woff"), url(https://font-public.canva.com/_fb/0/0A.ttf) format("truetype")}
@font-face {unicode-range: U+600-8ff,U+fb50-fdfd,U+fe70-fefc; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: normal; src: url(https://font-public.canva.com/_fb/0/0A.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/0A.woff) format("woff"), url(https://font-public.canva.com/_fb/0/0A.ttf) format("truetype")}
@font-face {unicode-range: U+600-8ff,U+fb50-fdfd,U+fe70-fefc; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: italic; src: url(https://font-public.canva.com/_fb/0/0A.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/0A.woff) format("woff"), url(https://font-public.canva.com/_fb/0/0A.ttf) format("truetype")}
@font-face {unicode-range: U+600-8ff,U+fb50-fdfd,U+fe70-fefc; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: normal; src: url(https://font-public.canva.com/_fb/0/09.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/09.woff) format("woff"), url(https://font-public.canva.com/_fb/0/09.ttf) format("truetype")}
@font-face {unicode-range: U+600-8ff,U+fb50-fdfd,U+fe70-fefc; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: italic; src: url(https://font-public.canva.com/_fb/0/09.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/09.woff) format("woff"), url(https://font-public.canva.com/_fb/0/09.ttf) format("truetype")}
@font-face {unicode-range: U+600-8ff,U+fb50-fdfd,U+fe70-fefc; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: normal; src: url(https://font-public.canva.com/_fb/0/09.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/09.woff) format("woff"), url(https://font-public.canva.com/_fb/0/09.ttf) format("truetype")}
@font-face {unicode-range: U+600-8ff,U+fb50-fdfd,U+fe70-fefc; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: italic; src: url(https://font-public.canva.com/_fb/0/09.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/09.woff) format("woff"), url(https://font-public.canva.com/_fb/0/09.ttf) format("truetype")}
@font-face {unicode-range: U+600-8ff,U+fb50-fdfd,U+fe70-fefc; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: normal; src: url(https://font-public.canva.com/_fb/0/09.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/09.woff) format("woff"), url(https://font-public.canva.com/_fb/0/09.ttf) format("truetype")}
@font-face {unicode-range: U+600-8ff,U+fb50-fdfd,U+fe70-fefc; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: italic; src: url(https://font-public.canva.com/_fb/0/09.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/09.woff) format("woff"), url(https://font-public.canva.com/_fb/0/09.ttf) format("truetype")}
@font-face {unicode-range: U+a81-af1; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: normal; src: url(https://font-public.canva.com/_fb/0/2D.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2D.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2D.ttf) format("truetype")}
@font-face {unicode-range: U+a81-af1; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: normal; src: url(https://font-public.canva.com/_fb/0/2C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2C.ttf) format("truetype")}
@font-face {unicode-range: U+a81-af1; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: italic; src: url(https://font-public.canva.com/_fb/0/2D.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2D.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2D.ttf) format("truetype")}
@font-face {unicode-range: U+a81-af1; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: italic; src: url(https://font-public.canva.com/_fb/0/2C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2C.ttf) format("truetype")}
@font-face {unicode-range: U+a81-af1; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: normal; src: url(https://font-public.canva.com/_fb/0/2D.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2D.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2D.ttf) format("truetype")}
@font-face {unicode-range: U+a81-af1; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: italic; src: url(https://font-public.canva.com/_fb/0/2D.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2D.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2D.ttf) format("truetype")}
@font-face {unicode-range: U+a81-af1; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: normal; src: url(https://font-public.canva.com/_fb/0/2D.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2D.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2D.ttf) format("truetype")}
@font-face {unicode-range: U+a81-af1; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: italic; src: url(https://font-public.canva.com/_fb/0/2D.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2D.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2D.ttf) format("truetype")}
@font-face {unicode-range: U+a81-af1; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: normal; src: url(https://font-public.canva.com/_fb/0/2D.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2D.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2D.ttf) format("truetype")}
@font-face {unicode-range: U+a81-af1; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: italic; src: url(https://font-public.canva.com/_fb/0/2D.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2D.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2D.ttf) format("truetype")}
@font-face {unicode-range: U+a81-af1; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: normal; src: url(https://font-public.canva.com/_fb/0/2D.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2D.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2D.ttf) format("truetype")}
@font-face {unicode-range: U+a81-af1; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: italic; src: url(https://font-public.canva.com/_fb/0/2D.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2D.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2D.ttf) format("truetype")}
@font-face {unicode-range: U+a81-af1; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: normal; src: url(https://font-public.canva.com/_fb/0/2C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2C.ttf) format("truetype")}
@font-face {unicode-range: U+a81-af1; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: italic; src: url(https://font-public.canva.com/_fb/0/2C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2C.ttf) format("truetype")}
@font-face {unicode-range: U+a81-af1; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: normal; src: url(https://font-public.canva.com/_fb/0/2C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2C.ttf) format("truetype")}
@font-face {unicode-range: U+a81-af1; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: italic; src: url(https://font-public.canva.com/_fb/0/2C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2C.ttf) format("truetype")}
@font-face {unicode-range: U+a81-af1; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: normal; src: url(https://font-public.canva.com/_fb/0/2C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2C.ttf) format("truetype")}
@font-face {unicode-range: U+a81-af1; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: italic; src: url(https://font-public.canva.com/_fb/0/2C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2C.ttf) format("truetype")}
@font-face {unicode-range: U+951-952,U+964-965,U+980-9fb; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: normal; src: url(https://font-public.canva.com/_fb/0/12.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/12.woff) format("woff"), url(https://font-public.canva.com/_fb/0/12.ttf) format("truetype")}
@font-face {unicode-range: U+951-952,U+964-965,U+980-9fb; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: normal; src: url(https://font-public.canva.com/_fb/0/11.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/11.woff) format("woff"), url(https://font-public.canva.com/_fb/0/11.ttf) format("truetype")}
@font-face {unicode-range: U+951-952,U+964-965,U+980-9fb; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: italic; src: url(https://font-public.canva.com/_fb/0/12.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/12.woff) format("woff"), url(https://font-public.canva.com/_fb/0/12.ttf) format("truetype")}
@font-face {unicode-range: U+951-952,U+964-965,U+980-9fb; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: italic; src: url(https://font-public.canva.com/_fb/0/11.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/11.woff) format("woff"), url(https://font-public.canva.com/_fb/0/11.ttf) format("truetype")}
@font-face {unicode-range: U+951-952,U+964-965,U+980-9fb; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: normal; src: url(https://font-public.canva.com/_fb/0/12.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/12.woff) format("woff"), url(https://font-public.canva.com/_fb/0/12.ttf) format("truetype")}
@font-face {unicode-range: U+951-952,U+964-965,U+980-9fb; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: italic; src: url(https://font-public.canva.com/_fb/0/12.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/12.woff) format("woff"), url(https://font-public.canva.com/_fb/0/12.ttf) format("truetype")}
@font-face {unicode-range: U+951-952,U+964-965,U+980-9fb; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: normal; src: url(https://font-public.canva.com/_fb/0/12.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/12.woff) format("woff"), url(https://font-public.canva.com/_fb/0/12.ttf) format("truetype")}
@font-face {unicode-range: U+951-952,U+964-965,U+980-9fb; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: italic; src: url(https://font-public.canva.com/_fb/0/12.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/12.woff) format("woff"), url(https://font-public.canva.com/_fb/0/12.ttf) format("truetype")}
@font-face {unicode-range: U+951-952,U+964-965,U+980-9fb; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: normal; src: url(https://font-public.canva.com/_fb/0/12.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/12.woff) format("woff"), url(https://font-public.canva.com/_fb/0/12.ttf) format("truetype")}
@font-face {unicode-range: U+951-952,U+964-965,U+980-9fb; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: italic; src: url(https://font-public.canva.com/_fb/0/12.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/12.woff) format("woff"), url(https://font-public.canva.com/_fb/0/12.ttf) format("truetype")}
@font-face {unicode-range: U+951-952,U+964-965,U+980-9fb; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: normal; src: url(https://font-public.canva.com/_fb/0/12.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/12.woff) format("woff"), url(https://font-public.canva.com/_fb/0/12.ttf) format("truetype")}
@font-face {unicode-range: U+951-952,U+964-965,U+980-9fb; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: italic; src: url(https://font-public.canva.com/_fb/0/12.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/12.woff) format("woff"), url(https://font-public.canva.com/_fb/0/12.ttf) format("truetype")}
@font-face {unicode-range: U+951-952,U+964-965,U+980-9fb; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: normal; src: url(https://font-public.canva.com/_fb/0/11.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/11.woff) format("woff"), url(https://font-public.canva.com/_fb/0/11.ttf) format("truetype")}
@font-face {unicode-range: U+951-952,U+964-965,U+980-9fb; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: italic; src: url(https://font-public.canva.com/_fb/0/11.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/11.woff) format("woff"), url(https://font-public.canva.com/_fb/0/11.ttf) format("truetype")}
@font-face {unicode-range: U+951-952,U+964-965,U+980-9fb; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: normal; src: url(https://font-public.canva.com/_fb/0/11.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/11.woff) format("woff"), url(https://font-public.canva.com/_fb/0/11.ttf) format("truetype")}
@font-face {unicode-range: U+951-952,U+964-965,U+980-9fb; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: italic; src: url(https://font-public.canva.com/_fb/0/11.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/11.woff) format("woff"), url(https://font-public.canva.com/_fb/0/11.ttf) format("truetype")}
@font-face {unicode-range: U+951-952,U+964-965,U+980-9fb; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: normal; src: url(https://font-public.canva.com/_fb/0/11.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/11.woff) format("woff"), url(https://font-public.canva.com/_fb/0/11.ttf) format("truetype")}
@font-face {unicode-range: U+951-952,U+964-965,U+980-9fb; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: italic; src: url(https://font-public.canva.com/_fb/0/11.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/11.woff) format("woff"), url(https://font-public.canva.com/_fb/0/11.ttf) format("truetype")}
@font-face {unicode-range: U+c01-c7f; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: normal; src: url(https://font-public.canva.com/_fb/0/72.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/72.woff) format("woff"), url(https://font-public.canva.com/_fb/0/72.ttf) format("truetype")}
@font-face {unicode-range: U+c01-c7f; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: normal; src: url(https://font-public.canva.com/_fb/0/71.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/71.woff) format("woff"), url(https://font-public.canva.com/_fb/0/71.ttf) format("truetype")}
@font-face {unicode-range: U+c01-c7f; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: italic; src: url(https://font-public.canva.com/_fb/0/72.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/72.woff) format("woff"), url(https://font-public.canva.com/_fb/0/72.ttf) format("truetype")}
@font-face {unicode-range: U+c01-c7f; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: italic; src: url(https://font-public.canva.com/_fb/0/71.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/71.woff) format("woff"), url(https://font-public.canva.com/_fb/0/71.ttf) format("truetype")}
@font-face {unicode-range: U+c01-c7f; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: normal; src: url(https://font-public.canva.com/_fb/0/72.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/72.woff) format("woff"), url(https://font-public.canva.com/_fb/0/72.ttf) format("truetype")}
@font-face {unicode-range: U+c01-c7f; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: italic; src: url(https://font-public.canva.com/_fb/0/72.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/72.woff) format("woff"), url(https://font-public.canva.com/_fb/0/72.ttf) format("truetype")}
@font-face {unicode-range: U+c01-c7f; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: normal; src: url(https://font-public.canva.com/_fb/0/72.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/72.woff) format("woff"), url(https://font-public.canva.com/_fb/0/72.ttf) format("truetype")}
@font-face {unicode-range: U+c01-c7f; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: italic; src: url(https://font-public.canva.com/_fb/0/72.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/72.woff) format("woff"), url(https://font-public.canva.com/_fb/0/72.ttf) format("truetype")}
@font-face {unicode-range: U+c01-c7f; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: normal; src: url(https://font-public.canva.com/_fb/0/72.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/72.woff) format("woff"), url(https://font-public.canva.com/_fb/0/72.ttf) format("truetype")}
@font-face {unicode-range: U+c01-c7f; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: italic; src: url(https://font-public.canva.com/_fb/0/72.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/72.woff) format("woff"), url(https://font-public.canva.com/_fb/0/72.ttf) format("truetype")}
@font-face {unicode-range: U+c01-c7f; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: normal; src: url(https://font-public.canva.com/_fb/0/72.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/72.woff) format("woff"), url(https://font-public.canva.com/_fb/0/72.ttf) format("truetype")}
@font-face {unicode-range: U+c01-c7f; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: italic; src: url(https://font-public.canva.com/_fb/0/72.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/72.woff) format("woff"), url(https://font-public.canva.com/_fb/0/72.ttf) format("truetype")}
@font-face {unicode-range: U+c01-c7f; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: normal; src: url(https://font-public.canva.com/_fb/0/71.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/71.woff) format("woff"), url(https://font-public.canva.com/_fb/0/71.ttf) format("truetype")}
@font-face {unicode-range: U+c01-c7f; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: italic; src: url(https://font-public.canva.com/_fb/0/71.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/71.woff) format("woff"), url(https://font-public.canva.com/_fb/0/71.ttf) format("truetype")}
@font-face {unicode-range: U+c01-c7f; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: normal; src: url(https://font-public.canva.com/_fb/0/71.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/71.woff) format("woff"), url(https://font-public.canva.com/_fb/0/71.ttf) format("truetype")}
@font-face {unicode-range: U+c01-c7f; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: italic; src: url(https://font-public.canva.com/_fb/0/71.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/71.woff) format("woff"), url(https://font-public.canva.com/_fb/0/71.ttf) format("truetype")}
@font-face {unicode-range: U+c01-c7f; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: normal; src: url(https://font-public.canva.com/_fb/0/71.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/71.woff) format("woff"), url(https://font-public.canva.com/_fb/0/71.ttf) format("truetype")}
@font-face {unicode-range: U+c01-c7f; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: italic; src: url(https://font-public.canva.com/_fb/0/71.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/71.woff) format("woff"), url(https://font-public.canva.com/_fb/0/71.ttf) format("truetype")}
@font-face {unicode-range: U+b01-b77; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: normal; src: url(https://font-public.canva.com/_fb/0/56.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/56.woff) format("woff"), url(https://font-public.canva.com/_fb/0/56.ttf) format("truetype")}
@font-face {unicode-range: U+b01-b77; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: normal; src: url(https://font-public.canva.com/_fb/0/55.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/55.woff) format("woff"), url(https://font-public.canva.com/_fb/0/55.ttf) format("truetype")}
@font-face {unicode-range: U+b01-b77; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: italic; src: url(https://font-public.canva.com/_fb/0/56.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/56.woff) format("woff"), url(https://font-public.canva.com/_fb/0/56.ttf) format("truetype")}
@font-face {unicode-range: U+b01-b77; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: italic; src: url(https://font-public.canva.com/_fb/0/55.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/55.woff) format("woff"), url(https://font-public.canva.com/_fb/0/55.ttf) format("truetype")}
@font-face {unicode-range: U+b01-b77; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: normal; src: url(https://font-public.canva.com/_fb/0/56.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/56.woff) format("woff"), url(https://font-public.canva.com/_fb/0/56.ttf) format("truetype")}
@font-face {unicode-range: U+b01-b77; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: italic; src: url(https://font-public.canva.com/_fb/0/56.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/56.woff) format("woff"), url(https://font-public.canva.com/_fb/0/56.ttf) format("truetype")}
@font-face {unicode-range: U+b01-b77; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: normal; src: url(https://font-public.canva.com/_fb/0/56.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/56.woff) format("woff"), url(https://font-public.canva.com/_fb/0/56.ttf) format("truetype")}
@font-face {unicode-range: U+b01-b77; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: italic; src: url(https://font-public.canva.com/_fb/0/56.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/56.woff) format("woff"), url(https://font-public.canva.com/_fb/0/56.ttf) format("truetype")}
@font-face {unicode-range: U+b01-b77; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: normal; src: url(https://font-public.canva.com/_fb/0/56.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/56.woff) format("woff"), url(https://font-public.canva.com/_fb/0/56.ttf) format("truetype")}
@font-face {unicode-range: U+b01-b77; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: italic; src: url(https://font-public.canva.com/_fb/0/56.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/56.woff) format("woff"), url(https://font-public.canva.com/_fb/0/56.ttf) format("truetype")}
@font-face {unicode-range: U+b01-b77; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: normal; src: url(https://font-public.canva.com/_fb/0/56.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/56.woff) format("woff"), url(https://font-public.canva.com/_fb/0/56.ttf) format("truetype")}
@font-face {unicode-range: U+b01-b77; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: italic; src: url(https://font-public.canva.com/_fb/0/56.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/56.woff) format("woff"), url(https://font-public.canva.com/_fb/0/56.ttf) format("truetype")}
@font-face {unicode-range: U+b01-b77; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: normal; src: url(https://font-public.canva.com/_fb/0/55.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/55.woff) format("woff"), url(https://font-public.canva.com/_fb/0/55.ttf) format("truetype")}
@font-face {unicode-range: U+b01-b77; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: italic; src: url(https://font-public.canva.com/_fb/0/55.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/55.woff) format("woff"), url(https://font-public.canva.com/_fb/0/55.ttf) format("truetype")}
@font-face {unicode-range: U+b01-b77; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: normal; src: url(https://font-public.canva.com/_fb/0/55.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/55.woff) format("woff"), url(https://font-public.canva.com/_fb/0/55.ttf) format("truetype")}
@font-face {unicode-range: U+b01-b77; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: italic; src: url(https://font-public.canva.com/_fb/0/55.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/55.woff) format("woff"), url(https://font-public.canva.com/_fb/0/55.ttf) format("truetype")}
@font-face {unicode-range: U+b01-b77; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: normal; src: url(https://font-public.canva.com/_fb/0/55.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/55.woff) format("woff"), url(https://font-public.canva.com/_fb/0/55.ttf) format("truetype")}
@font-face {unicode-range: U+b01-b77; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: italic; src: url(https://font-public.canva.com/_fb/0/55.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/55.woff) format("woff"), url(https://font-public.canva.com/_fb/0/55.ttf) format("truetype")}
@font-face {unicode-range: U+c82-cf2; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: normal; src: url(https://font-public.canva.com/_fb/0/39.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/39.woff) format("woff"), url(https://font-public.canva.com/_fb/0/39.ttf) format("truetype")}
@font-face {unicode-range: U+c82-cf2; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: normal; src: url(https://font-public.canva.com/_fb/0/38.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/38.woff) format("woff"), url(https://font-public.canva.com/_fb/0/38.ttf) format("truetype")}
@font-face {unicode-range: U+c82-cf2; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: italic; src: url(https://font-public.canva.com/_fb/0/39.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/39.woff) format("woff"), url(https://font-public.canva.com/_fb/0/39.ttf) format("truetype")}
@font-face {unicode-range: U+c82-cf2; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: italic; src: url(https://font-public.canva.com/_fb/0/38.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/38.woff) format("woff"), url(https://font-public.canva.com/_fb/0/38.ttf) format("truetype")}
@font-face {unicode-range: U+c82-cf2; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: normal; src: url(https://font-public.canva.com/_fb/0/39.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/39.woff) format("woff"), url(https://font-public.canva.com/_fb/0/39.ttf) format("truetype")}
@font-face {unicode-range: U+c82-cf2; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: italic; src: url(https://font-public.canva.com/_fb/0/39.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/39.woff) format("woff"), url(https://font-public.canva.com/_fb/0/39.ttf) format("truetype")}
@font-face {unicode-range: U+c82-cf2; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: normal; src: url(https://font-public.canva.com/_fb/0/39.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/39.woff) format("woff"), url(https://font-public.canva.com/_fb/0/39.ttf) format("truetype")}
@font-face {unicode-range: U+c82-cf2; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: italic; src: url(https://font-public.canva.com/_fb/0/39.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/39.woff) format("woff"), url(https://font-public.canva.com/_fb/0/39.ttf) format("truetype")}
@font-face {unicode-range: U+c82-cf2; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: normal; src: url(https://font-public.canva.com/_fb/0/39.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/39.woff) format("woff"), url(https://font-public.canva.com/_fb/0/39.ttf) format("truetype")}
@font-face {unicode-range: U+c82-cf2; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: italic; src: url(https://font-public.canva.com/_fb/0/39.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/39.woff) format("woff"), url(https://font-public.canva.com/_fb/0/39.ttf) format("truetype")}
@font-face {unicode-range: U+c82-cf2; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: normal; src: url(https://font-public.canva.com/_fb/0/39.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/39.woff) format("woff"), url(https://font-public.canva.com/_fb/0/39.ttf) format("truetype")}
@font-face {unicode-range: U+c82-cf2; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: italic; src: url(https://font-public.canva.com/_fb/0/39.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/39.woff) format("woff"), url(https://font-public.canva.com/_fb/0/39.ttf) format("truetype")}
@font-face {unicode-range: U+c82-cf2; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: normal; src: url(https://font-public.canva.com/_fb/0/38.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/38.woff) format("woff"), url(https://font-public.canva.com/_fb/0/38.ttf) format("truetype")}
@font-face {unicode-range: U+c82-cf2; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: italic; src: url(https://font-public.canva.com/_fb/0/38.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/38.woff) format("woff"), url(https://font-public.canva.com/_fb/0/38.ttf) format("truetype")}
@font-face {unicode-range: U+c82-cf2; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: normal; src: url(https://font-public.canva.com/_fb/0/38.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/38.woff) format("woff"), url(https://font-public.canva.com/_fb/0/38.ttf) format("truetype")}
@font-face {unicode-range: U+c82-cf2; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: italic; src: url(https://font-public.canva.com/_fb/0/38.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/38.woff) format("woff"), url(https://font-public.canva.com/_fb/0/38.ttf) format("truetype")}
@font-face {unicode-range: U+c82-cf2; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: normal; src: url(https://font-public.canva.com/_fb/0/38.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/38.woff) format("woff"), url(https://font-public.canva.com/_fb/0/38.ttf) format("truetype")}
@font-face {unicode-range: U+c82-cf2; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: italic; src: url(https://font-public.canva.com/_fb/0/38.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/38.woff) format("woff"), url(https://font-public.canva.com/_fb/0/38.ttf) format("truetype")}
@font-face {unicode-range: U+d02-d7f; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: normal; src: url(https://font-public.canva.com/_fb/0/47.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/47.woff) format("woff"), url(https://font-public.canva.com/_fb/0/47.ttf) format("truetype")}
@font-face {unicode-range: U+d02-d7f; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: normal; src: url(https://font-public.canva.com/_fb/0/46.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/46.woff) format("woff"), url(https://font-public.canva.com/_fb/0/46.ttf) format("truetype")}
@font-face {unicode-range: U+d02-d7f; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: italic; src: url(https://font-public.canva.com/_fb/0/47.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/47.woff) format("woff"), url(https://font-public.canva.com/_fb/0/47.ttf) format("truetype")}
@font-face {unicode-range: U+d02-d7f; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: italic; src: url(https://font-public.canva.com/_fb/0/46.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/46.woff) format("woff"), url(https://font-public.canva.com/_fb/0/46.ttf) format("truetype")}
@font-face {unicode-range: U+d02-d7f; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: normal; src: url(https://font-public.canva.com/_fb/0/47.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/47.woff) format("woff"), url(https://font-public.canva.com/_fb/0/47.ttf) format("truetype")}
@font-face {unicode-range: U+d02-d7f; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: italic; src: url(https://font-public.canva.com/_fb/0/47.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/47.woff) format("woff"), url(https://font-public.canva.com/_fb/0/47.ttf) format("truetype")}
@font-face {unicode-range: U+d02-d7f; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: normal; src: url(https://font-public.canva.com/_fb/0/47.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/47.woff) format("woff"), url(https://font-public.canva.com/_fb/0/47.ttf) format("truetype")}
@font-face {unicode-range: U+d02-d7f; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: italic; src: url(https://font-public.canva.com/_fb/0/47.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/47.woff) format("woff"), url(https://font-public.canva.com/_fb/0/47.ttf) format("truetype")}
@font-face {unicode-range: U+d02-d7f; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: normal; src: url(https://font-public.canva.com/_fb/0/47.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/47.woff) format("woff"), url(https://font-public.canva.com/_fb/0/47.ttf) format("truetype")}
@font-face {unicode-range: U+d02-d7f; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: italic; src: url(https://font-public.canva.com/_fb/0/47.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/47.woff) format("woff"), url(https://font-public.canva.com/_fb/0/47.ttf) format("truetype")}
@font-face {unicode-range: U+d02-d7f; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: normal; src: url(https://font-public.canva.com/_fb/0/47.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/47.woff) format("woff"), url(https://font-public.canva.com/_fb/0/47.ttf) format("truetype")}
@font-face {unicode-range: U+d02-d7f; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: italic; src: url(https://font-public.canva.com/_fb/0/47.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/47.woff) format("woff"), url(https://font-public.canva.com/_fb/0/47.ttf) format("truetype")}
@font-face {unicode-range: U+d02-d7f; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: normal; src: url(https://font-public.canva.com/_fb/0/46.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/46.woff) format("woff"), url(https://font-public.canva.com/_fb/0/46.ttf) format("truetype")}
@font-face {unicode-range: U+d02-d7f; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: italic; src: url(https://font-public.canva.com/_fb/0/46.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/46.woff) format("woff"), url(https://font-public.canva.com/_fb/0/46.ttf) format("truetype")}
@font-face {unicode-range: U+d02-d7f; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: normal; src: url(https://font-public.canva.com/_fb/0/46.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/46.woff) format("woff"), url(https://font-public.canva.com/_fb/0/46.ttf) format("truetype")}
@font-face {unicode-range: U+d02-d7f; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: italic; src: url(https://font-public.canva.com/_fb/0/46.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/46.woff) format("woff"), url(https://font-public.canva.com/_fb/0/46.ttf) format("truetype")}
@font-face {unicode-range: U+d02-d7f; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: normal; src: url(https://font-public.canva.com/_fb/0/46.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/46.woff) format("woff"), url(https://font-public.canva.com/_fb/0/46.ttf) format("truetype")}
@font-face {unicode-range: U+d02-d7f; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: italic; src: url(https://font-public.canva.com/_fb/0/46.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/46.woff) format("woff"), url(https://font-public.canva.com/_fb/0/46.ttf) format("truetype")}
@font-face {unicode-range: U+b82-bfa,U+1cda,U+2212,U+11303-1133c; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: normal; src: url(https://font-public.canva.com/_fb/0/70.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/70.woff) format("woff"), url(https://font-public.canva.com/_fb/0/70.ttf) format("truetype")}
@font-face {unicode-range: U+b82-bfa,U+1cda,U+2212,U+11303-1133c; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: normal; src: url(https://font-public.canva.com/_fb/0/6F.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/6F.woff) format("woff"), url(https://font-public.canva.com/_fb/0/6F.ttf) format("truetype")}
@font-face {unicode-range: U+b82-bfa,U+1cda,U+2212,U+11303-1133c; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: italic; src: url(https://font-public.canva.com/_fb/0/70.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/70.woff) format("woff"), url(https://font-public.canva.com/_fb/0/70.ttf) format("truetype")}
@font-face {unicode-range: U+b82-bfa,U+1cda,U+2212,U+11303-1133c; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: italic; src: url(https://font-public.canva.com/_fb/0/6F.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/6F.woff) format("woff"), url(https://font-public.canva.com/_fb/0/6F.ttf) format("truetype")}
@font-face {unicode-range: U+b82-bfa,U+1cda,U+2212,U+11303-1133c; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: normal; src: url(https://font-public.canva.com/_fb/0/70.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/70.woff) format("woff"), url(https://font-public.canva.com/_fb/0/70.ttf) format("truetype")}
@font-face {unicode-range: U+b82-bfa,U+1cda,U+2212,U+11303-1133c; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: italic; src: url(https://font-public.canva.com/_fb/0/70.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/70.woff) format("woff"), url(https://font-public.canva.com/_fb/0/70.ttf) format("truetype")}
@font-face {unicode-range: U+b82-bfa,U+1cda,U+2212,U+11303-1133c; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: normal; src: url(https://font-public.canva.com/_fb/0/70.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/70.woff) format("woff"), url(https://font-public.canva.com/_fb/0/70.ttf) format("truetype")}
@font-face {unicode-range: U+b82-bfa,U+1cda,U+2212,U+11303-1133c; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: italic; src: url(https://font-public.canva.com/_fb/0/70.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/70.woff) format("woff"), url(https://font-public.canva.com/_fb/0/70.ttf) format("truetype")}
@font-face {unicode-range: U+b82-bfa,U+1cda,U+2212,U+11303-1133c; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: normal; src: url(https://font-public.canva.com/_fb/0/70.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/70.woff) format("woff"), url(https://font-public.canva.com/_fb/0/70.ttf) format("truetype")}
@font-face {unicode-range: U+b82-bfa,U+1cda,U+2212,U+11303-1133c; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: italic; src: url(https://font-public.canva.com/_fb/0/70.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/70.woff) format("woff"), url(https://font-public.canva.com/_fb/0/70.ttf) format("truetype")}
@font-face {unicode-range: U+b82-bfa,U+1cda,U+2212,U+11303-1133c; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: normal; src: url(https://font-public.canva.com/_fb/0/70.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/70.woff) format("woff"), url(https://font-public.canva.com/_fb/0/70.ttf) format("truetype")}
@font-face {unicode-range: U+b82-bfa,U+1cda,U+2212,U+11303-1133c; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: italic; src: url(https://font-public.canva.com/_fb/0/70.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/70.woff) format("woff"), url(https://font-public.canva.com/_fb/0/70.ttf) format("truetype")}
@font-face {unicode-range: U+b82-bfa,U+1cda,U+2212,U+11303-1133c; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: normal; src: url(https://font-public.canva.com/_fb/0/6F.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/6F.woff) format("woff"), url(https://font-public.canva.com/_fb/0/6F.ttf) format("truetype")}
@font-face {unicode-range: U+b82-bfa,U+1cda,U+2212,U+11303-1133c; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: italic; src: url(https://font-public.canva.com/_fb/0/6F.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/6F.woff) format("woff"), url(https://font-public.canva.com/_fb/0/6F.ttf) format("truetype")}
@font-face {unicode-range: U+b82-bfa,U+1cda,U+2212,U+11303-1133c; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: normal; src: url(https://font-public.canva.com/_fb/0/6F.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/6F.woff) format("woff"), url(https://font-public.canva.com/_fb/0/6F.ttf) format("truetype")}
@font-face {unicode-range: U+b82-bfa,U+1cda,U+2212,U+11303-1133c; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: italic; src: url(https://font-public.canva.com/_fb/0/6F.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/6F.woff) format("woff"), url(https://font-public.canva.com/_fb/0/6F.ttf) format("truetype")}
@font-face {unicode-range: U+b82-bfa,U+1cda,U+2212,U+11303-1133c; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: normal; src: url(https://font-public.canva.com/_fb/0/6F.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/6F.woff) format("woff"), url(https://font-public.canva.com/_fb/0/6F.ttf) format("truetype")}
@font-face {unicode-range: U+b82-bfa,U+1cda,U+2212,U+11303-1133c; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: italic; src: url(https://font-public.canva.com/_fb/0/6F.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/6F.woff) format("woff"), url(https://font-public.canva.com/_fb/0/6F.ttf) format("truetype")}
@font-face {unicode-range: U+1780-19ff; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: normal; src: url(https://font-public.canva.com/_fb/0/3D.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3D.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3D.ttf) format("truetype")}
@font-face {unicode-range: U+1780-19ff; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: normal; src: url(https://font-public.canva.com/_fb/0/3C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3C.ttf) format("truetype")}
@font-face {unicode-range: U+1780-19ff; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: italic; src: url(https://font-public.canva.com/_fb/0/3D.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3D.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3D.ttf) format("truetype")}
@font-face {unicode-range: U+1780-19ff; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: italic; src: url(https://font-public.canva.com/_fb/0/3C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3C.ttf) format("truetype")}
@font-face {unicode-range: U+1780-19ff; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: normal; src: url(https://font-public.canva.com/_fb/0/3D.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3D.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3D.ttf) format("truetype")}
@font-face {unicode-range: U+1780-19ff; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: italic; src: url(https://font-public.canva.com/_fb/0/3D.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3D.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3D.ttf) format("truetype")}
@font-face {unicode-range: U+1780-19ff; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: normal; src: url(https://font-public.canva.com/_fb/0/3D.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3D.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3D.ttf) format("truetype")}
@font-face {unicode-range: U+1780-19ff; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: italic; src: url(https://font-public.canva.com/_fb/0/3D.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3D.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3D.ttf) format("truetype")}
@font-face {unicode-range: U+1780-19ff; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: normal; src: url(https://font-public.canva.com/_fb/0/3D.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3D.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3D.ttf) format("truetype")}
@font-face {unicode-range: U+1780-19ff; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: italic; src: url(https://font-public.canva.com/_fb/0/3D.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3D.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3D.ttf) format("truetype")}
@font-face {unicode-range: U+1780-19ff; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: normal; src: url(https://font-public.canva.com/_fb/0/3D.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3D.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3D.ttf) format("truetype")}
@font-face {unicode-range: U+1780-19ff; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: italic; src: url(https://font-public.canva.com/_fb/0/3D.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3D.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3D.ttf) format("truetype")}
@font-face {unicode-range: U+1780-19ff; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: normal; src: url(https://font-public.canva.com/_fb/0/3C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3C.ttf) format("truetype")}
@font-face {unicode-range: U+1780-19ff; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: italic; src: url(https://font-public.canva.com/_fb/0/3C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3C.ttf) format("truetype")}
@font-face {unicode-range: U+1780-19ff; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: normal; src: url(https://font-public.canva.com/_fb/0/3C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3C.ttf) format("truetype")}
@font-face {unicode-range: U+1780-19ff; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: italic; src: url(https://font-public.canva.com/_fb/0/3C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3C.ttf) format("truetype")}
@font-face {unicode-range: U+1780-19ff; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: normal; src: url(https://font-public.canva.com/_fb/0/3C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3C.ttf) format("truetype")}
@font-face {unicode-range: U+1780-19ff; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: italic; src: url(https://font-public.canva.com/_fb/0/3C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3C.ttf) format("truetype")}
@font-face {unicode-range: U+10a0-10ff,U+2d00-2d2d; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: normal; src: url(https://font-public.canva.com/_fb/0/29.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/29.woff) format("woff"), url(https://font-public.canva.com/_fb/0/29.ttf) format("truetype")}
@font-face {unicode-range: U+10a0-10ff,U+2d00-2d2d; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: normal; src: url(https://font-public.canva.com/_fb/0/28.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/28.woff) format("woff"), url(https://font-public.canva.com/_fb/0/28.ttf) format("truetype")}
@font-face {unicode-range: U+10a0-10ff,U+2d00-2d2d; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: italic; src: url(https://font-public.canva.com/_fb/0/29.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/29.woff) format("woff"), url(https://font-public.canva.com/_fb/0/29.ttf) format("truetype")}
@font-face {unicode-range: U+10a0-10ff,U+2d00-2d2d; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: italic; src: url(https://font-public.canva.com/_fb/0/28.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/28.woff) format("woff"), url(https://font-public.canva.com/_fb/0/28.ttf) format("truetype")}
@font-face {unicode-range: U+10a0-10ff,U+2d00-2d2d; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: normal; src: url(https://font-public.canva.com/_fb/0/29.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/29.woff) format("woff"), url(https://font-public.canva.com/_fb/0/29.ttf) format("truetype")}
@font-face {unicode-range: U+10a0-10ff,U+2d00-2d2d; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: italic; src: url(https://font-public.canva.com/_fb/0/29.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/29.woff) format("woff"), url(https://font-public.canva.com/_fb/0/29.ttf) format("truetype")}
@font-face {unicode-range: U+10a0-10ff,U+2d00-2d2d; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: normal; src: url(https://font-public.canva.com/_fb/0/29.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/29.woff) format("woff"), url(https://font-public.canva.com/_fb/0/29.ttf) format("truetype")}
@font-face {unicode-range: U+10a0-10ff,U+2d00-2d2d; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: italic; src: url(https://font-public.canva.com/_fb/0/29.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/29.woff) format("woff"), url(https://font-public.canva.com/_fb/0/29.ttf) format("truetype")}
@font-face {unicode-range: U+10a0-10ff,U+2d00-2d2d; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: normal; src: url(https://font-public.canva.com/_fb/0/29.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/29.woff) format("woff"), url(https://font-public.canva.com/_fb/0/29.ttf) format("truetype")}
@font-face {unicode-range: U+10a0-10ff,U+2d00-2d2d; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: italic; src: url(https://font-public.canva.com/_fb/0/29.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/29.woff) format("woff"), url(https://font-public.canva.com/_fb/0/29.ttf) format("truetype")}
@font-face {unicode-range: U+10a0-10ff,U+2d00-2d2d; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: normal; src: url(https://font-public.canva.com/_fb/0/29.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/29.woff) format("woff"), url(https://font-public.canva.com/_fb/0/29.ttf) format("truetype")}
@font-face {unicode-range: U+10a0-10ff,U+2d00-2d2d; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: italic; src: url(https://font-public.canva.com/_fb/0/29.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/29.woff) format("woff"), url(https://font-public.canva.com/_fb/0/29.ttf) format("truetype")}
@font-face {unicode-range: U+10a0-10ff,U+2d00-2d2d; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: normal; src: url(https://font-public.canva.com/_fb/0/28.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/28.woff) format("woff"), url(https://font-public.canva.com/_fb/0/28.ttf) format("truetype")}
@font-face {unicode-range: U+10a0-10ff,U+2d00-2d2d; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: italic; src: url(https://font-public.canva.com/_fb/0/28.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/28.woff) format("woff"), url(https://font-public.canva.com/_fb/0/28.ttf) format("truetype")}
@font-face {unicode-range: U+10a0-10ff,U+2d00-2d2d; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: normal; src: url(https://font-public.canva.com/_fb/0/28.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/28.woff) format("woff"), url(https://font-public.canva.com/_fb/0/28.ttf) format("truetype")}
@font-face {unicode-range: U+10a0-10ff,U+2d00-2d2d; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: italic; src: url(https://font-public.canva.com/_fb/0/28.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/28.woff) format("woff"), url(https://font-public.canva.com/_fb/0/28.ttf) format("truetype")}
@font-face {unicode-range: U+10a0-10ff,U+2d00-2d2d; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: normal; src: url(https://font-public.canva.com/_fb/0/28.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/28.woff) format("woff"), url(https://font-public.canva.com/_fb/0/28.ttf) format("truetype")}
@font-face {unicode-range: U+10a0-10ff,U+2d00-2d2d; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: italic; src: url(https://font-public.canva.com/_fb/0/28.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/28.woff) format("woff"), url(https://font-public.canva.com/_fb/0/28.ttf) format("truetype")}
@font-face {unicode-range: U+e01-e5b; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: normal; src: url(https://font-public.canva.com/_fb/0/76.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/76.woff) format("woff"), url(https://font-public.canva.com/_fb/0/76.ttf) format("truetype")}
@font-face {unicode-range: U+e01-e5b; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: normal; src: url(https://font-public.canva.com/_fb/0/75.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/75.woff) format("woff"), url(https://font-public.canva.com/_fb/0/75.ttf) format("truetype")}
@font-face {unicode-range: U+e01-e5b; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: italic; src: url(https://font-public.canva.com/_fb/0/76.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/76.woff) format("woff"), url(https://font-public.canva.com/_fb/0/76.ttf) format("truetype")}
@font-face {unicode-range: U+e01-e5b; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: italic; src: url(https://font-public.canva.com/_fb/0/75.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/75.woff) format("woff"), url(https://font-public.canva.com/_fb/0/75.ttf) format("truetype")}
@font-face {unicode-range: U+e01-e5b; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: normal; src: url(https://font-public.canva.com/_fb/0/76.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/76.woff) format("woff"), url(https://font-public.canva.com/_fb/0/76.ttf) format("truetype")}
@font-face {unicode-range: U+e01-e5b; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: italic; src: url(https://font-public.canva.com/_fb/0/76.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/76.woff) format("woff"), url(https://font-public.canva.com/_fb/0/76.ttf) format("truetype")}
@font-face {unicode-range: U+e01-e5b; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: normal; src: url(https://font-public.canva.com/_fb/0/76.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/76.woff) format("woff"), url(https://font-public.canva.com/_fb/0/76.ttf) format("truetype")}
@font-face {unicode-range: U+e01-e5b; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: italic; src: url(https://font-public.canva.com/_fb/0/76.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/76.woff) format("woff"), url(https://font-public.canva.com/_fb/0/76.ttf) format("truetype")}
@font-face {unicode-range: U+e01-e5b; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: normal; src: url(https://font-public.canva.com/_fb/0/76.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/76.woff) format("woff"), url(https://font-public.canva.com/_fb/0/76.ttf) format("truetype")}
@font-face {unicode-range: U+e01-e5b; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: italic; src: url(https://font-public.canva.com/_fb/0/76.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/76.woff) format("woff"), url(https://font-public.canva.com/_fb/0/76.ttf) format("truetype")}
@font-face {unicode-range: U+e01-e5b; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: normal; src: url(https://font-public.canva.com/_fb/0/76.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/76.woff) format("woff"), url(https://font-public.canva.com/_fb/0/76.ttf) format("truetype")}
@font-face {unicode-range: U+e01-e5b; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: italic; src: url(https://font-public.canva.com/_fb/0/76.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/76.woff) format("woff"), url(https://font-public.canva.com/_fb/0/76.ttf) format("truetype")}
@font-face {unicode-range: U+e01-e5b; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: normal; src: url(https://font-public.canva.com/_fb/0/75.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/75.woff) format("woff"), url(https://font-public.canva.com/_fb/0/75.ttf) format("truetype")}
@font-face {unicode-range: U+e01-e5b; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: italic; src: url(https://font-public.canva.com/_fb/0/75.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/75.woff) format("woff"), url(https://font-public.canva.com/_fb/0/75.ttf) format("truetype")}
@font-face {unicode-range: U+e01-e5b; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: normal; src: url(https://font-public.canva.com/_fb/0/75.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/75.woff) format("woff"), url(https://font-public.canva.com/_fb/0/75.ttf) format("truetype")}
@font-face {unicode-range: U+e01-e5b; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: italic; src: url(https://font-public.canva.com/_fb/0/75.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/75.woff) format("woff"), url(https://font-public.canva.com/_fb/0/75.ttf) format("truetype")}
@font-face {unicode-range: U+e01-e5b; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: normal; src: url(https://font-public.canva.com/_fb/0/75.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/75.woff) format("woff"), url(https://font-public.canva.com/_fb/0/75.ttf) format("truetype")}
@font-face {unicode-range: U+e01-e5b; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: italic; src: url(https://font-public.canva.com/_fb/0/75.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/75.woff) format("woff"), url(https://font-public.canva.com/_fb/0/75.ttf) format("truetype")}
@font-face {unicode-range: U+e81-edf,U+200b-200d; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: normal; src: url(https://font-public.canva.com/_fb/0/3F.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3F.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3F.ttf) format("truetype")}
@font-face {unicode-range: U+e81-edf,U+200b-200d; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: normal; src: url(https://font-public.canva.com/_fb/0/3E.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3E.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3E.ttf) format("truetype")}
@font-face {unicode-range: U+e81-edf,U+200b-200d; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: italic; src: url(https://font-public.canva.com/_fb/0/3F.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3F.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3F.ttf) format("truetype")}
@font-face {unicode-range: U+e81-edf,U+200b-200d; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: italic; src: url(https://font-public.canva.com/_fb/0/3E.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3E.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3E.ttf) format("truetype")}
@font-face {unicode-range: U+e81-edf,U+200b-200d; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: normal; src: url(https://font-public.canva.com/_fb/0/3F.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3F.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3F.ttf) format("truetype")}
@font-face {unicode-range: U+e81-edf,U+200b-200d; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: italic; src: url(https://font-public.canva.com/_fb/0/3F.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3F.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3F.ttf) format("truetype")}
@font-face {unicode-range: U+e81-edf,U+200b-200d; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: normal; src: url(https://font-public.canva.com/_fb/0/3F.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3F.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3F.ttf) format("truetype")}
@font-face {unicode-range: U+e81-edf,U+200b-200d; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: italic; src: url(https://font-public.canva.com/_fb/0/3F.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3F.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3F.ttf) format("truetype")}
@font-face {unicode-range: U+e81-edf,U+200b-200d; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: normal; src: url(https://font-public.canva.com/_fb/0/3F.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3F.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3F.ttf) format("truetype")}
@font-face {unicode-range: U+e81-edf,U+200b-200d; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: italic; src: url(https://font-public.canva.com/_fb/0/3F.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3F.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3F.ttf) format("truetype")}
@font-face {unicode-range: U+e81-edf,U+200b-200d; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: normal; src: url(https://font-public.canva.com/_fb/0/3F.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3F.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3F.ttf) format("truetype")}
@font-face {unicode-range: U+e81-edf,U+200b-200d; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: italic; src: url(https://font-public.canva.com/_fb/0/3F.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3F.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3F.ttf) format("truetype")}
@font-face {unicode-range: U+e81-edf,U+200b-200d; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: normal; src: url(https://font-public.canva.com/_fb/0/3E.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3E.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3E.ttf) format("truetype")}
@font-face {unicode-range: U+e81-edf,U+200b-200d; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: italic; src: url(https://font-public.canva.com/_fb/0/3E.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3E.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3E.ttf) format("truetype")}
@font-face {unicode-range: U+e81-edf,U+200b-200d; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: normal; src: url(https://font-public.canva.com/_fb/0/3E.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3E.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3E.ttf) format("truetype")}
@font-face {unicode-range: U+e81-edf,U+200b-200d; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: italic; src: url(https://font-public.canva.com/_fb/0/3E.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3E.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3E.ttf) format("truetype")}
@font-face {unicode-range: U+e81-edf,U+200b-200d; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: normal; src: url(https://font-public.canva.com/_fb/0/3E.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3E.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3E.ttf) format("truetype")}
@font-face {unicode-range: U+e81-edf,U+200b-200d; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: italic; src: url(https://font-public.canva.com/_fb/0/3E.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/3E.woff) format("woff"), url(https://font-public.canva.com/_fb/0/3E.ttf) format("truetype")}
@font-face {unicode-range: U+531-58f,U+fb13-fb17; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: normal; src: url(https://font-public.canva.com/_fb/0/0C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/0C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/0C.ttf) format("truetype")}
@font-face {unicode-range: U+531-58f,U+fb13-fb17; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: normal; src: url(https://font-public.canva.com/_fb/0/0B.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/0B.woff) format("woff"), url(https://font-public.canva.com/_fb/0/0B.ttf) format("truetype")}
@font-face {unicode-range: U+531-58f,U+fb13-fb17; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: italic; src: url(https://font-public.canva.com/_fb/0/0C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/0C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/0C.ttf) format("truetype")}
@font-face {unicode-range: U+531-58f,U+fb13-fb17; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: italic; src: url(https://font-public.canva.com/_fb/0/0B.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/0B.woff) format("woff"), url(https://font-public.canva.com/_fb/0/0B.ttf) format("truetype")}
@font-face {unicode-range: U+531-58f,U+fb13-fb17; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: normal; src: url(https://font-public.canva.com/_fb/0/0C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/0C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/0C.ttf) format("truetype")}
@font-face {unicode-range: U+531-58f,U+fb13-fb17; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: italic; src: url(https://font-public.canva.com/_fb/0/0C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/0C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/0C.ttf) format("truetype")}
@font-face {unicode-range: U+531-58f,U+fb13-fb17; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: normal; src: url(https://font-public.canva.com/_fb/0/0C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/0C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/0C.ttf) format("truetype")}
@font-face {unicode-range: U+531-58f,U+fb13-fb17; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: italic; src: url(https://font-public.canva.com/_fb/0/0C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/0C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/0C.ttf) format("truetype")}
@font-face {unicode-range: U+531-58f,U+fb13-fb17; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: normal; src: url(https://font-public.canva.com/_fb/0/0C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/0C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/0C.ttf) format("truetype")}
@font-face {unicode-range: U+531-58f,U+fb13-fb17; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: italic; src: url(https://font-public.canva.com/_fb/0/0C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/0C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/0C.ttf) format("truetype")}
@font-face {unicode-range: U+531-58f,U+fb13-fb17; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: normal; src: url(https://font-public.canva.com/_fb/0/0C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/0C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/0C.ttf) format("truetype")}
@font-face {unicode-range: U+531-58f,U+fb13-fb17; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: italic; src: url(https://font-public.canva.com/_fb/0/0C.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/0C.woff) format("woff"), url(https://font-public.canva.com/_fb/0/0C.ttf) format("truetype")}
@font-face {unicode-range: U+531-58f,U+fb13-fb17; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: normal; src: url(https://font-public.canva.com/_fb/0/0B.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/0B.woff) format("woff"), url(https://font-public.canva.com/_fb/0/0B.ttf) format("truetype")}
@font-face {unicode-range: U+531-58f,U+fb13-fb17; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: italic; src: url(https://font-public.canva.com/_fb/0/0B.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/0B.woff) format("woff"), url(https://font-public.canva.com/_fb/0/0B.ttf) format("truetype")}
@font-face {unicode-range: U+531-58f,U+fb13-fb17; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: normal; src: url(https://font-public.canva.com/_fb/0/0B.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/0B.woff) format("woff"), url(https://font-public.canva.com/_fb/0/0B.ttf) format("truetype")}
@font-face {unicode-range: U+531-58f,U+fb13-fb17; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: italic; src: url(https://font-public.canva.com/_fb/0/0B.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/0B.woff) format("woff"), url(https://font-public.canva.com/_fb/0/0B.ttf) format("truetype")}
@font-face {unicode-range: U+531-58f,U+fb13-fb17; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: normal; src: url(https://font-public.canva.com/_fb/0/0B.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/0B.woff) format("woff"), url(https://font-public.canva.com/_fb/0/0B.ttf) format("truetype")}
@font-face {unicode-range: U+531-58f,U+fb13-fb17; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: italic; src: url(https://font-public.canva.com/_fb/0/0B.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/0B.woff) format("woff"), url(https://font-public.canva.com/_fb/0/0B.ttf) format("truetype")}
@font-face {unicode-range: U+591-5f4,U+200c-200f,U+25cc,U+fb1d-fb4f; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: normal; src: url(https://font-public.canva.com/_fb/0/32.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/32.woff) format("woff"), url(https://font-public.canva.com/_fb/0/32.ttf) format("truetype")}
@font-face {unicode-range: U+591-5f4,U+200c-200f,U+25cc,U+fb1d-fb4f; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: normal; src: url(https://font-public.canva.com/_fb/0/31.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/31.woff) format("woff"), url(https://font-public.canva.com/_fb/0/31.ttf) format("truetype")}
@font-face {unicode-range: U+591-5f4,U+200c-200f,U+25cc,U+fb1d-fb4f; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: italic; src: url(https://font-public.canva.com/_fb/0/32.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/32.woff) format("woff"), url(https://font-public.canva.com/_fb/0/32.ttf) format("truetype")}
@font-face {unicode-range: U+591-5f4,U+200c-200f,U+25cc,U+fb1d-fb4f; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: italic; src: url(https://font-public.canva.com/_fb/0/31.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/31.woff) format("woff"), url(https://font-public.canva.com/_fb/0/31.ttf) format("truetype")}
@font-face {unicode-range: U+591-5f4,U+200c-200f,U+25cc,U+fb1d-fb4f; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: normal; src: url(https://font-public.canva.com/_fb/0/32.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/32.woff) format("woff"), url(https://font-public.canva.com/_fb/0/32.ttf) format("truetype")}
@font-face {unicode-range: U+591-5f4,U+200c-200f,U+25cc,U+fb1d-fb4f; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: italic; src: url(https://font-public.canva.com/_fb/0/32.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/32.woff) format("woff"), url(https://font-public.canva.com/_fb/0/32.ttf) format("truetype")}
@font-face {unicode-range: U+591-5f4,U+200c-200f,U+25cc,U+fb1d-fb4f; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: normal; src: url(https://font-public.canva.com/_fb/0/32.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/32.woff) format("woff"), url(https://font-public.canva.com/_fb/0/32.ttf) format("truetype")}
@font-face {unicode-range: U+591-5f4,U+200c-200f,U+25cc,U+fb1d-fb4f; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: italic; src: url(https://font-public.canva.com/_fb/0/32.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/32.woff) format("woff"), url(https://font-public.canva.com/_fb/0/32.ttf) format("truetype")}
@font-face {unicode-range: U+591-5f4,U+200c-200f,U+25cc,U+fb1d-fb4f; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: normal; src: url(https://font-public.canva.com/_fb/0/32.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/32.woff) format("woff"), url(https://font-public.canva.com/_fb/0/32.ttf) format("truetype")}
@font-face {unicode-range: U+591-5f4,U+200c-200f,U+25cc,U+fb1d-fb4f; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: italic; src: url(https://font-public.canva.com/_fb/0/32.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/32.woff) format("woff"), url(https://font-public.canva.com/_fb/0/32.ttf) format("truetype")}
@font-face {unicode-range: U+591-5f4,U+200c-200f,U+25cc,U+fb1d-fb4f; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: normal; src: url(https://font-public.canva.com/_fb/0/32.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/32.woff) format("woff"), url(https://font-public.canva.com/_fb/0/32.ttf) format("truetype")}
@font-face {unicode-range: U+591-5f4,U+200c-200f,U+25cc,U+fb1d-fb4f; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: italic; src: url(https://font-public.canva.com/_fb/0/32.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/32.woff) format("woff"), url(https://font-public.canva.com/_fb/0/32.ttf) format("truetype")}
@font-face {unicode-range: U+591-5f4,U+200c-200f,U+25cc,U+fb1d-fb4f; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: normal; src: url(https://font-public.canva.com/_fb/0/31.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/31.woff) format("woff"), url(https://font-public.canva.com/_fb/0/31.ttf) format("truetype")}
@font-face {unicode-range: U+591-5f4,U+200c-200f,U+25cc,U+fb1d-fb4f; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: italic; src: url(https://font-public.canva.com/_fb/0/31.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/31.woff) format("woff"), url(https://font-public.canva.com/_fb/0/31.ttf) format("truetype")}
@font-face {unicode-range: U+591-5f4,U+200c-200f,U+25cc,U+fb1d-fb4f; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: normal; src: url(https://font-public.canva.com/_fb/0/31.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/31.woff) format("woff"), url(https://font-public.canva.com/_fb/0/31.ttf) format("truetype")}
@font-face {unicode-range: U+591-5f4,U+200c-200f,U+25cc,U+fb1d-fb4f; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: italic; src: url(https://font-public.canva.com/_fb/0/31.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/31.woff) format("woff"), url(https://font-public.canva.com/_fb/0/31.ttf) format("truetype")}
@font-face {unicode-range: U+591-5f4,U+200c-200f,U+25cc,U+fb1d-fb4f; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: normal; src: url(https://font-public.canva.com/_fb/0/31.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/31.woff) format("woff"), url(https://font-public.canva.com/_fb/0/31.ttf) format("truetype")}
@font-face {unicode-range: U+591-5f4,U+200c-200f,U+25cc,U+fb1d-fb4f; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: italic; src: url(https://font-public.canva.com/_fb/0/31.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/31.woff) format("woff"), url(https://font-public.canva.com/_fb/0/31.ttf) format("truetype")}
@font-face {unicode-range: U+a01-a75,U+2013-20b9,U+feff; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: normal; src: url(https://font-public.canva.com/_fb/0/2F.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2F.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2F.ttf) format("truetype")}
@font-face {unicode-range: U+a01-a75,U+2013-20b9,U+feff; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: normal; src: url(https://font-public.canva.com/_fb/0/2E.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2E.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2E.ttf) format("truetype")}
@font-face {unicode-range: U+a01-a75,U+2013-20b9,U+feff; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: italic; src: url(https://font-public.canva.com/_fb/0/2F.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2F.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2F.ttf) format("truetype")}
@font-face {unicode-range: U+a01-a75,U+2013-20b9,U+feff; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: italic; src: url(https://font-public.canva.com/_fb/0/2E.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2E.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2E.ttf) format("truetype")}
@font-face {unicode-range: U+a01-a75,U+2013-20b9,U+feff; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: normal; src: url(https://font-public.canva.com/_fb/0/2F.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2F.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2F.ttf) format("truetype")}
@font-face {unicode-range: U+a01-a75,U+2013-20b9,U+feff; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: italic; src: url(https://font-public.canva.com/_fb/0/2F.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2F.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2F.ttf) format("truetype")}
@font-face {unicode-range: U+a01-a75,U+2013-20b9,U+feff; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: normal; src: url(https://font-public.canva.com/_fb/0/2F.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2F.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2F.ttf) format("truetype")}
@font-face {unicode-range: U+a01-a75,U+2013-20b9,U+feff; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: italic; src: url(https://font-public.canva.com/_fb/0/2F.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2F.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2F.ttf) format("truetype")}
@font-face {unicode-range: U+a01-a75,U+2013-20b9,U+feff; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: normal; src: url(https://font-public.canva.com/_fb/0/2F.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2F.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2F.ttf) format("truetype")}
@font-face {unicode-range: U+a01-a75,U+2013-20b9,U+feff; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: italic; src: url(https://font-public.canva.com/_fb/0/2F.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2F.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2F.ttf) format("truetype")}
@font-face {unicode-range: U+a01-a75,U+2013-20b9,U+feff; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: normal; src: url(https://font-public.canva.com/_fb/0/2F.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2F.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2F.ttf) format("truetype")}
@font-face {unicode-range: U+a01-a75,U+2013-20b9,U+feff; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: italic; src: url(https://font-public.canva.com/_fb/0/2F.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2F.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2F.ttf) format("truetype")}
@font-face {unicode-range: U+a01-a75,U+2013-20b9,U+feff; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: normal; src: url(https://font-public.canva.com/_fb/0/2E.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2E.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2E.ttf) format("truetype")}
@font-face {unicode-range: U+a01-a75,U+2013-20b9,U+feff; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: italic; src: url(https://font-public.canva.com/_fb/0/2E.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2E.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2E.ttf) format("truetype")}
@font-face {unicode-range: U+a01-a75,U+2013-20b9,U+feff; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: normal; src: url(https://font-public.canva.com/_fb/0/2E.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2E.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2E.ttf) format("truetype")}
@font-face {unicode-range: U+a01-a75,U+2013-20b9,U+feff; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: italic; src: url(https://font-public.canva.com/_fb/0/2E.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2E.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2E.ttf) format("truetype")}
@font-face {unicode-range: U+a01-a75,U+2013-20b9,U+feff; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: normal; src: url(https://font-public.canva.com/_fb/0/2E.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2E.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2E.ttf) format("truetype")}
@font-face {unicode-range: U+a01-a75,U+2013-20b9,U+feff; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: italic; src: url(https://font-public.canva.com/_fb/0/2E.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/2E.woff) format("woff"), url(https://font-public.canva.com/_fb/0/2E.ttf) format("truetype")}
@font-face {unicode-range: U+1200-1399,U+22ee,U+2d80-2dde,U+ab01-ab2e; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: normal; src: url(https://font-public.canva.com/_fb/0/27.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/27.woff) format("woff"), url(https://font-public.canva.com/_fb/0/27.ttf) format("truetype")}
@font-face {unicode-range: U+1200-1399,U+22ee,U+2d80-2dde,U+ab01-ab2e; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: normal; src: url(https://font-public.canva.com/_fb/0/26.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/26.woff) format("woff"), url(https://font-public.canva.com/_fb/0/26.ttf) format("truetype")}
@font-face {unicode-range: U+1200-1399,U+22ee,U+2d80-2dde,U+ab01-ab2e; font-family: "_fb_"; font-display: swap; font-weight: 400; font-style: italic; src: url(https://font-public.canva.com/_fb/0/27.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/27.woff) format("woff"), url(https://font-public.canva.com/_fb/0/27.ttf) format("truetype")}
@font-face {unicode-range: U+1200-1399,U+22ee,U+2d80-2dde,U+ab01-ab2e; font-family: "_fb_"; font-display: swap; font-weight: 700; font-style: italic; src: url(https://font-public.canva.com/_fb/0/26.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/26.woff) format("woff"), url(https://font-public.canva.com/_fb/0/26.ttf) format("truetype")}
@font-face {unicode-range: U+1200-1399,U+22ee,U+2d80-2dde,U+ab01-ab2e; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: normal; src: url(https://font-public.canva.com/_fb/0/27.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/27.woff) format("woff"), url(https://font-public.canva.com/_fb/0/27.ttf) format("truetype")}
@font-face {unicode-range: U+1200-1399,U+22ee,U+2d80-2dde,U+ab01-ab2e; font-family: "_fb_"; font-display: swap; font-weight: 100; font-style: italic; src: url(https://font-public.canva.com/_fb/0/27.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/27.woff) format("woff"), url(https://font-public.canva.com/_fb/0/27.ttf) format("truetype")}
@font-face {unicode-range: U+1200-1399,U+22ee,U+2d80-2dde,U+ab01-ab2e; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: normal; src: url(https://font-public.canva.com/_fb/0/27.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/27.woff) format("woff"), url(https://font-public.canva.com/_fb/0/27.ttf) format("truetype")}
@font-face {unicode-range: U+1200-1399,U+22ee,U+2d80-2dde,U+ab01-ab2e; font-family: "_fb_"; font-display: swap; font-weight: 200; font-style: italic; src: url(https://font-public.canva.com/_fb/0/27.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/27.woff) format("woff"), url(https://font-public.canva.com/_fb/0/27.ttf) format("truetype")}
@font-face {unicode-range: U+1200-1399,U+22ee,U+2d80-2dde,U+ab01-ab2e; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: normal; src: url(https://font-public.canva.com/_fb/0/27.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/27.woff) format("woff"), url(https://font-public.canva.com/_fb/0/27.ttf) format("truetype")}
@font-face {unicode-range: U+1200-1399,U+22ee,U+2d80-2dde,U+ab01-ab2e; font-family: "_fb_"; font-display: swap; font-weight: 300; font-style: italic; src: url(https://font-public.canva.com/_fb/0/27.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/27.woff) format("woff"), url(https://font-public.canva.com/_fb/0/27.ttf) format("truetype")}
@font-face {unicode-range: U+1200-1399,U+22ee,U+2d80-2dde,U+ab01-ab2e; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: normal; src: url(https://font-public.canva.com/_fb/0/27.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/27.woff) format("woff"), url(https://font-public.canva.com/_fb/0/27.ttf) format("truetype")}
@font-face {unicode-range: U+1200-1399,U+22ee,U+2d80-2dde,U+ab01-ab2e; font-family: "_fb_"; font-display: swap; font-weight: 500; font-style: italic; src: url(https://font-public.canva.com/_fb/0/27.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/27.woff) format("woff"), url(https://font-public.canva.com/_fb/0/27.ttf) format("truetype")}
@font-face {unicode-range: U+1200-1399,U+22ee,U+2d80-2dde,U+ab01-ab2e; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: normal; src: url(https://font-public.canva.com/_fb/0/26.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/26.woff) format("woff"), url(https://font-public.canva.com/_fb/0/26.ttf) format("truetype")}
@font-face {unicode-range: U+1200-1399,U+22ee,U+2d80-2dde,U+ab01-ab2e; font-family: "_fb_"; font-display: swap; font-weight: 600; font-style: italic; src: url(https://font-public.canva.com/_fb/0/26.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/26.woff) format("woff"), url(https://font-public.canva.com/_fb/0/26.ttf) format("truetype")}
@font-face {unicode-range: U+1200-1399,U+22ee,U+2d80-2dde,U+ab01-ab2e; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: normal; src: url(https://font-public.canva.com/_fb/0/26.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/26.woff) format("woff"), url(https://font-public.canva.com/_fb/0/26.ttf) format("truetype")}
@font-face {unicode-range: U+1200-1399,U+22ee,U+2d80-2dde,U+ab01-ab2e; font-family: "_fb_"; font-display: swap; font-weight: 800; font-style: italic; src: url(https://font-public.canva.com/_fb/0/26.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/26.woff) format("woff"), url(https://font-public.canva.com/_fb/0/26.ttf) format("truetype")}
@font-face {unicode-range: U+1200-1399,U+22ee,U+2d80-2dde,U+ab01-ab2e; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: normal; src: url(https://font-public.canva.com/_fb/0/26.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/26.woff) format("woff"), url(https://font-public.canva.com/_fb/0/26.ttf) format("truetype")}
@font-face {unicode-range: U+1200-1399,U+22ee,U+2d80-2dde,U+ab01-ab2e; font-family: "_fb_"; font-display: swap; font-weight: 900; font-style: italic; src: url(https://font-public.canva.com/_fb/0/26.woff2) format("woff2"), url(https://font-public.canva.com/_fb/0/26.woff) format("woff"), url(https://font-public.canva.com/_fb/0/26.ttf) format("truetype")}