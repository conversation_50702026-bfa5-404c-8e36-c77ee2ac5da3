/*! For license information please see b58f37f200ce9f78.1ul071.vendor.js.LICENSE.txt */
(self.webpackChunk_canva_web=self.webpackChunk_canva_web||[]).push([[98449],{13851:(e,t)=>{"use strict";t.default=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return 2===e.length?r(e[0],e[1])||null:e.slice(1).reduce((function(e,t){return r(e,t)}),e[0])||null};var n=new WeakMap;function r(e,t){if(e&&t){var r=n.get(e)||new WeakMap;n.set(e,r);var o=r.get(t)||function(n){i(e,n),i(t,n)};return r.set(t,o),o}return e||t}function i(e,t){"function"==typeof e?e(t):e.current=t}},15924:(e,t,n)=>{"use strict";var r,i;function o(e){return e.type===r.literal}function s(e){return e.type===r.argument}function a(e){return e.type===r.number}function c(e){return e.type===r.date}function l(e){return e.type===r.time}function u(e){return e.type===r.select}function h(e){return e.type===r.plural}function f(e){return e.type===r.pound}function d(e){return e.type===r.tag}function p(e){return!(!e||"object"!=typeof e||e.type!==i.number)}function m(e){return!(!e||"object"!=typeof e||e.type!==i.dateTime)}n.d(t,{SKELETON_TYPE:()=>i,TYPE:()=>r,isArgumentElement:()=>s,isDateElement:()=>c,isDateTimeSkeleton:()=>m,isLiteralElement:()=>o,isNumberElement:()=>a,isNumberSkeleton:()=>p,isPluralElement:()=>h,isPoundElement:()=>f,isSelectElement:()=>u,isTagElement:()=>d,isTimeElement:()=>l}),function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"}(r||(r={})),function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"}(i||(i={}))},22626:(e,t,n)=>{"use strict";n.d(t,{getChildMapping:()=>i,getInitialChildMapping:()=>s,getNextChildMapping:()=>a});var r=n(845212);function i(e,t){var n=Object.create(null);return e&&r.Children.map(e,(function(e){return e})).forEach((function(e){n[e.key]=function(e){return t&&(0,r.isValidElement)(e)?t(e):e}(e)})),n}function o(e,t,n){return null!=n[t]?n[t]:e.props[t]}function s(e,t){return i(e.children,(function(n){return(0,r.cloneElement)(n,{onExited:t.bind(null,n),in:!0,appear:o(n,"appear",e),enter:o(n,"enter",e),exit:o(n,"exit",e)})}))}function a(e,t,n){var s=i(e.children),a=function(e,t){function n(n){return n in t?t[n]:e[n]}e=e||{},t=t||{};var r,i=Object.create(null),o=[];for(var s in e)s in t?o.length&&(i[s]=o,o=[]):o.push(s);var a={};for(var c in t){if(i[c])for(r=0;r<i[c].length;r++){var l=i[c][r];a[i[c][r]]=n(l)}a[c]=n(c)}for(r=0;r<o.length;r++)a[o[r]]=n(o[r]);return a}(t,s);return Object.keys(a).forEach((function(i){var c=a[i];if((0,r.isValidElement)(c)){var l=i in t,u=i in s,h=t[i],f=(0,r.isValidElement)(h)&&!h.props.in;!u||l&&!f?u||!l||f?u&&l&&(0,r.isValidElement)(h)&&(a[i]=(0,r.cloneElement)(c,{onExited:n.bind(null,c),in:h.props.in,exit:o(c,"exit",e),enter:o(c,"enter",e)})):a[i]=(0,r.cloneElement)(c,{in:!1}):a[i]=(0,r.cloneElement)(c,{onExited:n.bind(null,c),in:!0,exit:o(c,"exit",e),enter:o(c,"enter",e)})}})),a}},27950:(e,t,n)=>{"use strict";n.d(t,{default:()=>f});var r=n(897010),i=n(165669),o=n(710821),s=n(434008),a=n(951265),c=n(845212),l=n(833299),u=function(e,t){return e&&t&&t.split(" ").forEach((function(t){return(0,a.default)(e,t)}))},h=function(e){function t(){for(var t,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(t=e.call.apply(e,[this].concat(r))||this).appliedClasses={appear:{},enter:{},exit:{}},t.onEnter=function(e,n){var r=t.resolveArguments(e,n),i=r[0],o=r[1];t.removeClasses(i,"exit"),t.addClass(i,o?"appear":"enter","base"),t.props.onEnter&&t.props.onEnter(e,n)},t.onEntering=function(e,n){var r=t.resolveArguments(e,n),i=r[0],o=r[1]?"appear":"enter";t.addClass(i,o,"active"),t.props.onEntering&&t.props.onEntering(e,n)},t.onEntered=function(e,n){var r=t.resolveArguments(e,n),i=r[0],o=r[1]?"appear":"enter";t.removeClasses(i,o),t.addClass(i,o,"done"),t.props.onEntered&&t.props.onEntered(e,n)},t.onExit=function(e){var n=t.resolveArguments(e)[0];t.removeClasses(n,"appear"),t.removeClasses(n,"enter"),t.addClass(n,"exit","base"),t.props.onExit&&t.props.onExit(e)},t.onExiting=function(e){var n=t.resolveArguments(e)[0];t.addClass(n,"exit","active"),t.props.onExiting&&t.props.onExiting(e)},t.onExited=function(e){var n=t.resolveArguments(e)[0];t.removeClasses(n,"exit"),t.addClass(n,"exit","done"),t.props.onExited&&t.props.onExited(e)},t.resolveArguments=function(e,n){return t.props.nodeRef?[t.props.nodeRef.current,e]:[e,n]},t.getClassNames=function(e){var n=t.props.classNames,r="string"==typeof n,i=r?""+(r&&n?n+"-":"")+e:n[e];return{baseClassName:i,activeClassName:r?i+"-active":n[e+"Active"],doneClassName:r?i+"-done":n[e+"Done"]}},t}(0,o.default)(t,e);var n=t.prototype;return n.addClass=function(e,t,n){var r=this.getClassNames(t)[n+"ClassName"],i=this.getClassNames("enter").doneClassName;"appear"===t&&"done"===n&&i&&(r+=" "+i),"active"===n&&e&&e.scrollTop,r&&(this.appliedClasses[t][n]=r,function(e,t){e&&t&&t.split(" ").forEach((function(t){return(0,s.default)(e,t)}))}(e,r))},n.removeClasses=function(e,t){var n=this.appliedClasses[t],r=n.base,i=n.active,o=n.done;this.appliedClasses[t]={},r&&u(e,r),i&&u(e,i),o&&u(e,o)},n.render=function(){var e=this.props,t=(e.classNames,(0,i.default)(e,["classNames"]));return c.createElement(l.default,(0,r.default)({},t,{onEnter:this.onEnter,onEntered:this.onEntered,onEntering:this.onEntering,onExit:this.onExit,onExiting:this.onExiting,onExited:this.onExited}))},t}(c.Component);h.defaultProps={classNames:""},h.propTypes={};const f=h},76901:(e,t,n)=>{"use strict";var r=n(754838);function i(){}function o(){}o.resetWarningCache=i,e.exports=function(){function e(e,t,n,i,o,s){if(s!==r){var a=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:o,resetWarningCache:i};return n.PropTypes=n,n}},105572:(e,t,n)=>{var r=n(876461),i=n(493668),o=n(334986),s=n(565800),a=n(451118),c=n(999208),l=n(689420);l.alea=r,l.xor128=i,l.xorwow=o,l.xorshift7=s,l.xor4096=a,l.tychei=c,e.exports=l},106853:(e,t,n)=>{"use strict";n.d(t,{WHITE_SPACE_REGEX:()=>r});var r=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i},152060:(e,t,n)=>{"use strict";n.d(t,{default:()=>f});var r=n(165669),i=n(897010),o=n(787475),s=n(710821),a=n(845212),c=n(158385),l=n(22626),u=Object.values||function(e){return Object.keys(e).map((function(t){return e[t]}))},h=function(e){function t(t,n){var r,i=(r=e.call(this,t,n)||this).handleExited.bind((0,o.default)(r));return r.state={contextValue:{isMounting:!0},handleExited:i,firstRender:!0},r}(0,s.default)(t,e);var n=t.prototype;return n.componentDidMount=function(){this.mounted=!0,this.setState({contextValue:{isMounting:!1}})},n.componentWillUnmount=function(){this.mounted=!1},t.getDerivedStateFromProps=function(e,t){var n=t.children,r=t.handleExited;return{children:t.firstRender?(0,l.getInitialChildMapping)(e,r):(0,l.getNextChildMapping)(e,n,r),firstRender:!1}},n.handleExited=function(e,t){var n=(0,l.getChildMapping)(this.props.children);e.key in n||(e.props.onExited&&e.props.onExited(t),this.mounted&&this.setState((function(t){var n=(0,i.default)({},t.children);return delete n[e.key],{children:n}})))},n.render=function(){var e=this.props,t=e.component,n=e.childFactory,i=(0,r.default)(e,["component","childFactory"]),o=this.state.contextValue,s=u(this.state.children).map(n);return delete i.appear,delete i.enter,delete i.exit,null===t?a.createElement(c.default.Provider,{value:o},s):a.createElement(c.default.Provider,{value:o},a.createElement(t,i,s))},t}(a.Component);h.propTypes={},h.defaultProps={component:"div",childFactory:function(e){return e}};const f=h},158385:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});const r=n(845212).createContext(null)},165669:(e,t,n)=>{"use strict";function r(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}n.d(t,{default:()=>r})},167921:(e,t,n)=>{"use strict";n.d(t,{IntlMessageFormat:()=>l});var r=n(944339),i=n(607597),o=n(919010),s=n(853469);function a(e,t){return t?Object.keys(e).reduce((function(n,i){var o,s;return n[i]=(o=e[i],(s=t[i])?(0,r.__assign)((0,r.__assign)((0,r.__assign)({},o||{}),s||{}),Object.keys(o).reduce((function(e,t){return e[t]=(0,r.__assign)((0,r.__assign)({},o[t]),s[t]||{}),e}),{})):o),n}),(0,r.__assign)({},e)):e}function c(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,n){e[t]=n}}}}}var l=function(){function e(t,n,i,l){var u,h=this;if(void 0===n&&(n=e.defaultLocale),this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(e){var t=h.formatToParts(e);if(1===t.length)return t[0].value;var n=t.reduce((function(e,t){return e.length&&t.type===s.PART_TYPE.literal&&"string"==typeof e[e.length-1]?e[e.length-1]+=t.value:e.push(t.value),e}),[]);return n.length<=1?n[0]||"":n},this.formatToParts=function(e){return(0,s.formatToParts)(h.ast,h.locales,h.formatters,h.formats,e,void 0,h.message)},this.resolvedOptions=function(){var e;return{locale:(null===(e=h.resolvedLocale)||void 0===e?void 0:e.toString())||Intl.NumberFormat.supportedLocalesOf(h.locales)[0]}},this.getAst=function(){return h.ast},this.locales=n,this.resolvedLocale=e.resolveLocale(n),"string"==typeof t){if(this.message=t,!e.__parse)throw new TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var f=l||{},d=(f.formatters,(0,r.__rest)(f,["formatters"]));this.ast=e.__parse(t,(0,r.__assign)((0,r.__assign)({},d),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw new TypeError("A message must be provided as a String or AST.");this.formats=a(e.formats,i),this.formatters=l&&l.formatters||(void 0===(u=this.formatterCache)&&(u={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:(0,o.memoize)((function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.NumberFormat).bind.apply(e,(0,r.__spreadArray)([void 0],t,!1)))}),{cache:c(u.number),strategy:o.strategies.variadic}),getDateTimeFormat:(0,o.memoize)((function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.DateTimeFormat).bind.apply(e,(0,r.__spreadArray)([void 0],t,!1)))}),{cache:c(u.dateTime),strategy:o.strategies.variadic}),getPluralRules:(0,o.memoize)((function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.PluralRules).bind.apply(e,(0,r.__spreadArray)([void 0],t,!1)))}),{cache:c(u.pluralRules),strategy:o.strategies.variadic})})}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=(new Intl.NumberFormat).resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(e){if(void 0!==Intl.Locale){var t=Intl.NumberFormat.supportedLocalesOf(e);return t.length>0?new Intl.Locale(t[0]):new Intl.Locale("string"==typeof e?e:e[0])}},e.__parse=i.parse,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}()},174762:(e,t,n)=>{"use strict";n.d(t,{detectScrollType:()=>o,getNormalizedScrollLeft:()=>s,setNormalizedScrollLeft:()=>a});var r,i=!("undefined"==typeof window||!window.document||!window.document.createElement);function o(){if(r)return r;if(!i||!window.document.body)return"indeterminate";var e=window.document.createElement("div");return e.appendChild(document.createTextNode("ABCD")),e.dir="rtl",e.style.fontSize="14px",e.style.width="4px",e.style.height="1px",e.style.position="absolute",e.style.top="-1000px",e.style.overflow="scroll",document.body.appendChild(e),r="reverse",e.scrollLeft>0?r="default":(e.scrollLeft=2,e.scrollLeft<2&&(r="negative")),document.body.removeChild(e),r}function s(e,t){var n=e.scrollLeft;if("rtl"!==t)return n;var r=o();if("indeterminate"===r)return Number.NaN;switch(r){case"negative":return e.scrollWidth-e.clientWidth+n;case"reverse":return e.scrollWidth-e.clientWidth-n}return n}function a(e,t,n){if("rtl"===n){var r=o();if("indeterminate"!==r)switch(r){case"negative":e.scrollLeft=e.clientWidth-e.scrollWidth+t;break;case"reverse":e.scrollLeft=e.scrollWidth-e.clientWidth-t;break;default:e.scrollLeft=t}}else e.scrollLeft=t}},208463:(e,t)=>{var n;!function(){"use strict";var r={}.hasOwnProperty;function i(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=s(e,o(n)))}return e}function o(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return i.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)r.call(e,n)&&e[n]&&(t=s(t,n));return t}function s(e,t){return t?e?e+" "+t:e+t:e}e.exports?(i.default=i,e.exports=i):void 0===(n=function(){return i}.apply(t,[]))||(e.exports=n)}()},269018:(e,t,n)=>{"use strict";function r(e,t,n,i){function o(e,t){return function(n){!function(e,t){if(e.v)throw new Error("attempted to call "+t+" after decoration was finished")}(t,"addInitializer"),a(n,"An initializer"),e.push(n)}}function s(e,t,n,r,i,s,a,c,l){var u;switch(i){case 1:u="accessor";break;case 2:u="method";break;case 3:u="getter";break;case 4:u="setter";break;default:u="field"}var h,f,d={kind:u,name:a?"#"+t:t,static:s,private:a,metadata:c},p={v:!1};d.addInitializer=o(r,p),0===i?a?(h=n.get,f=n.set):(h=function(){return this[t]},f=function(e){this[t]=e}):2===i?h=function(){return n.value}:(1!==i&&3!==i||(h=function(){return n.get.call(this)}),1!==i&&4!==i||(f=function(e){n.set.call(this,e)})),d.access=h&&f?{get:h,set:f}:h?{get:h}:{set:f};try{return e(l,d)}finally{p.v=!0}}function a(e,t){if("function"!=typeof e)throw new TypeError(t+" must be a function")}function c(e,t){var n=typeof t;if(1===e){if("object"!==n||null===t)throw new TypeError("accessor decorators must return an object with get, set, or init properties or void 0");void 0!==t.get&&a(t.get,"accessor.get"),void 0!==t.set&&a(t.set,"accessor.set"),void 0!==t.init&&a(t.init,"accessor.init")}else if("function"!==n){var r;throw r=0===e?"field":10===e?"class":"method",new TypeError(r+" decorators must return a function or void 0")}}function l(e,t,n,r,i,o,a,l,u){var h,f,d,p,m,g,v=n[0];if(a?h=0===i||1===i?{get:n[3],set:n[4]}:3===i?{get:n[3]}:4===i?{set:n[3]}:{value:n[3]}:0!==i&&(h=Object.getOwnPropertyDescriptor(t,r)),1===i?d={get:h.get,set:h.set}:2===i?d=h.value:3===i?d=h.get:4===i&&(d=h.set),"function"==typeof v)void 0!==(p=s(v,r,h,l,i,o,a,u,d))&&(c(i,p),0===i?f=p:1===i?(f=p.init,m=p.get||d.get,g=p.set||d.set,d={get:m,set:g}):d=p);else for(var E=v.length-1;E>=0;E--){var y;if(void 0!==(p=s(v[E],r,h,l,i,o,a,u,d)))c(i,p),0===i?y=p:1===i?(y=p.init,m=p.get||d.get,g=p.set||d.set,d={get:m,set:g}):d=p,void 0!==y&&(void 0===f?f=y:"function"==typeof f?f=[f,y]:f.push(y))}if(0===i||1===i){if(void 0===f)f=function(e,t){return t};else if("function"!=typeof f){var b=f;f=function(e,t){for(var n=t,r=0;r<b.length;r++)n=b[r].call(e,n);return n}}else{var w=f;f=function(e,t){return w.call(e,t)}}e.push(f)}0!==i&&(1===i?(h.get=d.get,h.set=d.set):2===i?h.value=d:3===i?h.get=d:4===i&&(h.set=d),a?1===i?(e.push((function(e,t){return d.get.call(e,t)})),e.push((function(e,t){return d.set.call(e,t)}))):2===i?e.push(d):e.push((function(e,t){return d.call(e,t)})):Object.defineProperty(t,r,h))}function u(e,t){t&&e.push((function(e){for(var n=0;n<t.length;n++)t[n].call(e);return e}))}function h(e,t){return Object.defineProperty(e,Symbol.metadata||Symbol.for("Symbol.metadata"),{configurable:!0,enumerable:!0,value:t})}return r=function(e,t,n,r){if(void 0!==r)var i=r[Symbol.metadata||Symbol.for("Symbol.metadata")];var s=Object.create(void 0===i?null:i),a=function(e,t,n){for(var r,i,o=[],s=new Map,a=new Map,c=0;c<t.length;c++){var h=t[c];if(Array.isArray(h)){var f,d,p=h[1],m=h[2],g=h.length>3,v=p>=5;if(v?(f=e,p-=5,d=i=i||[]):(f=e.prototype,d=r=r||[]),0!==p&&!g){var E=v?a:s,y=E.get(m)||0;if(!0===y||3===y&&4!==p||4===y&&3!==p)throw new Error("Attempted to decorate a public method/accessor that has the same name as a previously decorated public method/accessor. This is not currently supported by the decorators plugin. Property name was: "+m);!y&&p>2?E.set(m,p):E.set(m,!0)}l(o,f,h,m,p,v,g,d,n)}}return u(o,r),u(o,i),o}(e,t,s);return n.length||h(e,s),{e:a,get c(){return function(e,t,n){if(t.length>0){for(var r=[],i=e,s=e.name,a=t.length-1;a>=0;a--){var l={v:!1};try{var u=t[a](i,{kind:"class",name:s,addInitializer:o(r,l),metadata:n})}finally{l.v=!0}void 0!==u&&(c(10,u),i=u)}return[h(i,n),function(){for(var e=0;e<r.length;e++)r[e].call(i)}]}}(e,n,s)}}},r(e,t,n,i)}n.d(t,{_:()=>r})},270713:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});const r=n(167921).IntlMessageFormat},276089:(e,t,n)=>{"use strict";function r(e){return s(e)?(e.nodeName||"").toLowerCase():"#document"}function i(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function o(e){var t;return null==(t=(s(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function s(e){return e instanceof Node||e instanceof i(e).Node}function a(e){return e instanceof Element||e instanceof i(e).Element}function c(e){return e instanceof HTMLElement||e instanceof i(e).HTMLElement}function l(e){return"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof i(e).ShadowRoot)}function u(e){const{overflow:t,overflowX:n,overflowY:r,display:i}=g(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function h(e){return["table","td","th"].includes(r(e))}function f(e){const t=p(),n=g(e);return"none"!==n.transform||"none"!==n.perspective||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","perspective","filter"].some((e=>(n.willChange||"").includes(e)))||["paint","layout","strict","content"].some((e=>(n.contain||"").includes(e)))}function d(e){let t=E(e);for(;c(t)&&!m(t);){if(f(t))return t;t=E(t)}return null}function p(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function m(e){return["html","body","#document"].includes(r(e))}function g(e){return i(e).getComputedStyle(e)}function v(e){return a(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function E(e){if("html"===r(e))return e;const t=e.assignedSlot||e.parentNode||l(e)&&e.host||o(e);return l(t)?t.host:t}function y(e){const t=E(e);return m(t)?e.ownerDocument?e.ownerDocument.body:e.body:c(t)&&u(t)?t:y(t)}function b(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);const o=y(e),s=o===(null==(r=e.ownerDocument)?void 0:r.body),a=i(o);return s?t.concat(a,a.visualViewport||[],u(o)?o:[],a.frameElement&&n?b(a.frameElement):[]):t.concat(o,b(o,[],n))}n.d(t,{getComputedStyle:()=>g,getContainingBlock:()=>d,getDocumentElement:()=>o,getNodeName:()=>r,getNodeScroll:()=>v,getOverflowAncestors:()=>b,getParentNode:()=>E,getWindow:()=>i,isContainingBlock:()=>f,isElement:()=>a,isHTMLElement:()=>c,isLastTraversableNode:()=>m,isOverflowElement:()=>u,isShadowRoot:()=>l,isTableElement:()=>h,isWebKit:()=>p})},277049:(e,t,n)=>{"use strict";function r(e){return e}n.d(t,{_:()=>r})},294462:(e,t,n)=>{"use strict";n.d(t,{default:()=>x});var r=function(){if("undefined"!=typeof Map)return Map;function e(e,t){var n=-1;return e.some((function(e,r){return e[0]===t&&(n=r,!0)})),n}return function(){function t(){this.__entries__=[]}return Object.defineProperty(t.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),t.prototype.get=function(t){var n=e(this.__entries__,t),r=this.__entries__[n];return r&&r[1]},t.prototype.set=function(t,n){var r=e(this.__entries__,t);~r?this.__entries__[r][1]=n:this.__entries__.push([t,n])},t.prototype.delete=function(t){var n=this.__entries__,r=e(n,t);~r&&n.splice(r,1)},t.prototype.has=function(t){return!!~e(this.__entries__,t)},t.prototype.clear=function(){this.__entries__.splice(0)},t.prototype.forEach=function(e,t){void 0===t&&(t=null);for(var n=0,r=this.__entries__;n<r.length;n++){var i=r[n];e.call(t,i[1],i[0])}},t}()}(),i="undefined"!=typeof window&&"undefined"!=typeof document&&window.document===document,o=void 0!==n.g&&n.g.Math===Math?n.g:"undefined"!=typeof self&&self.Math===Math?self:"undefined"!=typeof window&&window.Math===Math?window:Function("return this")(),s="function"==typeof requestAnimationFrame?requestAnimationFrame.bind(o):function(e){return setTimeout((function(){return e(Date.now())}),1e3/60)};var a=["top","right","bottom","left","width","height","size","weight"],c="undefined"!=typeof MutationObserver,l=function(){function e(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=function(e,t){var n=!1,r=!1,i=0;function o(){n&&(n=!1,e()),r&&c()}function a(){s(o)}function c(){var e=Date.now();if(n){if(e-i<2)return;r=!0}else n=!0,r=!1,setTimeout(a,t);i=e}return c}(this.refresh.bind(this),20)}return e.prototype.addObserver=function(e){~this.observers_.indexOf(e)||this.observers_.push(e),this.connected_||this.connect_()},e.prototype.removeObserver=function(e){var t=this.observers_,n=t.indexOf(e);~n&&t.splice(n,1),!t.length&&this.connected_&&this.disconnect_()},e.prototype.refresh=function(){this.updateObservers_()&&this.refresh()},e.prototype.updateObservers_=function(){var e=this.observers_.filter((function(e){return e.gatherActive(),e.hasActive()}));return e.forEach((function(e){return e.broadcastActive()})),e.length>0},e.prototype.connect_=function(){i&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),c?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},e.prototype.disconnect_=function(){i&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},e.prototype.onTransitionEnd_=function(e){var t=e.propertyName,n=void 0===t?"":t;a.some((function(e){return!!~n.indexOf(e)}))&&this.refresh()},e.getInstance=function(){return this.instance_||(this.instance_=new e),this.instance_},e.instance_=null,e}(),u=function(e,t){for(var n=0,r=Object.keys(t);n<r.length;n++){var i=r[n];Object.defineProperty(e,i,{value:t[i],enumerable:!1,writable:!1,configurable:!0})}return e},h=function(e){return e&&e.ownerDocument&&e.ownerDocument.defaultView||o},f=E(0,0,0,0);function d(e){return parseFloat(e)||0}function p(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return t.reduce((function(t,n){return t+d(e["border-"+n+"-width"])}),0)}function m(e){var t=e.clientWidth,n=e.clientHeight;if(!t&&!n)return f;var r=h(e).getComputedStyle(e),i=function(e){for(var t={},n=0,r=["top","right","bottom","left"];n<r.length;n++){var i=r[n],o=e["padding-"+i];t[i]=d(o)}return t}(r),o=i.left+i.right,s=i.top+i.bottom,a=d(r.width),c=d(r.height);if("border-box"===r.boxSizing&&(Math.round(a+o)!==t&&(a-=p(r,"left","right")+o),Math.round(c+s)!==n&&(c-=p(r,"top","bottom")+s)),!function(e){return e===h(e).document.documentElement}(e)){var l=Math.round(a+o)-t,u=Math.round(c+s)-n;1!==Math.abs(l)&&(a-=l),1!==Math.abs(u)&&(c-=u)}return E(i.left,i.top,a,c)}var g="undefined"!=typeof SVGGraphicsElement?function(e){return e instanceof h(e).SVGGraphicsElement}:function(e){return e instanceof h(e).SVGElement&&"function"==typeof e.getBBox};function v(e){return i?g(e)?function(e){var t=e.getBBox();return E(0,0,t.width,t.height)}(e):m(e):f}function E(e,t,n,r){return{x:e,y:t,width:n,height:r}}var y=function(){function e(e){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=E(0,0,0,0),this.target=e}return e.prototype.isActive=function(){var e=v(this.target);return this.contentRect_=e,e.width!==this.broadcastWidth||e.height!==this.broadcastHeight},e.prototype.broadcastRect=function(){var e=this.contentRect_;return this.broadcastWidth=e.width,this.broadcastHeight=e.height,e},e}(),b=function(e,t){var n,r,i,o,s,a,c,l=(r=(n=t).x,i=n.y,o=n.width,s=n.height,a="undefined"!=typeof DOMRectReadOnly?DOMRectReadOnly:Object,c=Object.create(a.prototype),u(c,{x:r,y:i,width:o,height:s,top:i,right:r+o,bottom:s+i,left:r}),c);u(this,{target:e,contentRect:l})},w=function(){function e(e,t,n){if(this.activeObservations_=[],this.observations_=new r,"function"!=typeof e)throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=e,this.controller_=t,this.callbackCtx_=n}return e.prototype.observe=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof h(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)||(t.set(e,new y(e)),this.controller_.addObserver(this),this.controller_.refresh())}},e.prototype.unobserve=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof h(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)&&(t.delete(e),t.size||this.controller_.removeObserver(this))}},e.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},e.prototype.gatherActive=function(){var e=this;this.clearActive(),this.observations_.forEach((function(t){t.isActive()&&e.activeObservations_.push(t)}))},e.prototype.broadcastActive=function(){if(this.hasActive()){var e=this.callbackCtx_,t=this.activeObservations_.map((function(e){return new b(e.target,e.broadcastRect())}));this.callback_.call(e,t,e),this.clearActive()}},e.prototype.clearActive=function(){this.activeObservations_.splice(0)},e.prototype.hasActive=function(){return this.activeObservations_.length>0},e}(),_="undefined"!=typeof WeakMap?new WeakMap:new r,T=function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var n=l.getInstance(),r=new w(t,n,this);_.set(this,r)};["observe","unobserve","disconnect"].forEach((function(e){T.prototype[e]=function(){var t;return(t=_.get(this))[e].apply(t,arguments)}}));const x=void 0!==o.ResizeObserver?o.ResizeObserver:T},316620:(e,t,n)=>{"use strict";n.d(t,{default:()=>m,withContentRect:()=>d});var r=n(897010),i=n(165669),o=n(710821),s=n(845212),a=n(484201),c=n.n(a),l=n(294462),u=["client","offset","scroll","bounds","margin"];function h(e){var t=[];return u.forEach((function(n){e[n]&&t.push(n)})),t}function f(e,t){var n={};if(t.indexOf("client")>-1&&(n.client={top:e.clientTop,left:e.clientLeft,width:e.clientWidth,height:e.clientHeight}),t.indexOf("offset")>-1&&(n.offset={top:e.offsetTop,left:e.offsetLeft,width:e.offsetWidth,height:e.offsetHeight}),t.indexOf("scroll")>-1&&(n.scroll={top:e.scrollTop,left:e.scrollLeft,width:e.scrollWidth,height:e.scrollHeight}),t.indexOf("bounds")>-1){var r=e.getBoundingClientRect();n.bounds={top:r.top,right:r.right,bottom:r.bottom,left:r.left,width:r.width,height:r.height}}if(t.indexOf("margin")>-1){var i=getComputedStyle(e);n.margin={top:i?parseInt(i.marginTop):0,right:i?parseInt(i.marginRight):0,bottom:i?parseInt(i.marginBottom):0,left:i?parseInt(i.marginLeft):0}}return n}function d(e){return function(t){var n,a;return a=n=function(n){function a(){for(var t,r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];return(t=n.call.apply(n,[this].concat(i))||this).state={contentRect:{entry:{},client:{},offset:{},scroll:{},bounds:{},margin:{}}},t._animationFrameID=null,t._resizeObserver=null,t._node=null,t._window=null,t.measure=function(n){var r=f(t._node,e||h(t.props));n&&(r.entry=n[0].contentRect),t._animationFrameID=t._window.requestAnimationFrame((function(){null!==t._resizeObserver&&(t.setState({contentRect:r}),"function"==typeof t.props.onResize&&t.props.onResize(r))}))},t._handleRef=function(e){var n;null!==t._resizeObserver&&null!==t._node&&t._resizeObserver.unobserve(t._node),t._node=e,t._window=(n=t._node)&&n.ownerDocument&&n.ownerDocument.defaultView||window;var r=t.props.innerRef;r&&("function"==typeof r?r(t._node):r.current=t._node),null!==t._resizeObserver&&null!==t._node&&t._resizeObserver.observe(t._node)},t}(0,o.default)(a,n);var c=a.prototype;return c.componentDidMount=function(){this._resizeObserver=null!==this._window&&this._window.ResizeObserver?new this._window.ResizeObserver(this.measure):new l.default(this.measure),null!==this._node&&(this._resizeObserver.observe(this._node),"function"==typeof this.props.onResize&&this.props.onResize(f(this._node,e||h(this.props))))},c.componentWillUnmount=function(){null!==this._window&&this._window.cancelAnimationFrame(this._animationFrameID),null!==this._resizeObserver&&(this._resizeObserver.disconnect(),this._resizeObserver=null)},c.render=function(){var e=this.props,n=(e.innerRef,e.onResize,(0,i.default)(e,["innerRef","onResize"]));return(0,s.createElement)(t,(0,r.default)({},n,{measureRef:this._handleRef,measure:this.measure,contentRect:this.state.contentRect}))},a}(s.Component),n.propTypes={client:c().bool,offset:c().bool,scroll:c().bool,bounds:c().bool,margin:c().bool,innerRef:c().oneOfType([c().object,c().func]),onResize:c().func},a}}var p=d()((function(e){var t=e.measure,n=e.measureRef,r=e.contentRect;return(0,e.children)({measure:t,measureRef:n,contentRect:r})}));p.displayName="Measure",p.propTypes.children=c().func;const m=p},332386:(e,t,n)=>{"use strict";n.d(t,{Parser:()=>O});var r,i=n(944339),o=n(810487),s=n(15924),a=n(688125),c=n(680811),l=n(553824),u=n(358892),h=new RegExp("^".concat(a.SPACE_SEPARATOR_REGEX.source,"*")),f=new RegExp("".concat(a.SPACE_SEPARATOR_REGEX.source,"*$"));function d(e,t){return{start:e,end:t}}var p=!!String.prototype.startsWith&&"_a".startsWith("a",1),m=!!String.fromCodePoint,g=!!Object.fromEntries,v=!!String.prototype.codePointAt,E=!!String.prototype.trimStart,y=!!String.prototype.trimEnd,b=!!Number.isSafeInteger?Number.isSafeInteger:function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e&&Math.abs(e)<=9007199254740991},w=!0;try{w="a"===(null===(r=R("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu").exec("a"))||void 0===r?void 0:r[0])}catch(M){w=!1}var _,T=p?function(e,t,n){return e.startsWith(t,n)}:function(e,t,n){return e.slice(n,n+t.length)===t},x=m?String.fromCodePoint:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n,r="",i=e.length,o=0;i>o;){if((n=e[o++])>1114111)throw RangeError(n+" is not a valid code point");r+=n<65536?String.fromCharCode(n):String.fromCharCode(55296+((n-=65536)>>10),n%1024+56320)}return r},A=g?Object.fromEntries:function(e){for(var t={},n=0,r=e;n<r.length;n++){var i=r[n],o=i[0],s=i[1];t[o]=s}return t},S=v?function(e,t){return e.codePointAt(t)}:function(e,t){var n=e.length;if(!(t<0||t>=n)){var r,i=e.charCodeAt(t);return i<55296||i>56319||t+1===n||(r=e.charCodeAt(t+1))<56320||r>57343?i:r-56320+(i-55296<<10)+65536}},C=E?function(e){return e.trimStart()}:function(e){return e.replace(h,"")},P=y?function(e){return e.trimEnd()}:function(e){return e.replace(f,"")};function R(e,t){return new RegExp(e,t)}if(w){var H=R("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");_=function(e,t){var n;return H.lastIndex=t,null!==(n=H.exec(e)[1])&&void 0!==n?n:""}}else _=function(e,t){for(var n=[];;){var r=S(e,t);if(void 0===r||N(r)||B(r))break;n.push(r),t+=r>=65536?2:1}return x.apply(void 0,n)};var O=function(){function e(e,t){void 0===t&&(t={}),this.message=e,this.position={offset:0,line:1,column:1},this.ignoreTag=!!t.ignoreTag,this.locale=t.locale,this.requiresOtherClause=!!t.requiresOtherClause,this.shouldParseSkeletons=!!t.shouldParseSkeletons}return e.prototype.parse=function(){if(0!==this.offset())throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(e,t,n){for(var r=[];!this.isEOF();){var i=this.char();if(123===i){if((a=this.parseArgument(e,n)).err)return a;r.push(a.val)}else{if(125===i&&e>0)break;if(35!==i||"plural"!==t&&"selectordinal"!==t){if(60===i&&!this.ignoreTag&&47===this.peek()){if(n)break;return this.error(o.ErrorKind.UNMATCHED_CLOSING_TAG,d(this.clonePosition(),this.clonePosition()))}if(60===i&&!this.ignoreTag&&L(this.peek()||0)){if((a=this.parseTag(e,t)).err)return a;r.push(a.val)}else{var a;if((a=this.parseLiteral(e,t)).err)return a;r.push(a.val)}}else{var c=this.clonePosition();this.bump(),r.push({type:s.TYPE.pound,location:d(c,this.clonePosition())})}}}return{val:r,err:null}},e.prototype.parseTag=function(e,t){var n=this.clonePosition();this.bump();var r=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:s.TYPE.literal,value:"<".concat(r,"/>"),location:d(n,this.clonePosition())},err:null};if(this.bumpIf(">")){var i=this.parseMessage(e+1,t,!0);if(i.err)return i;var a=i.val,c=this.clonePosition();if(this.bumpIf("</")){if(this.isEOF()||!L(this.char()))return this.error(o.ErrorKind.INVALID_TAG,d(c,this.clonePosition()));var l=this.clonePosition();return r!==this.parseTagName()?this.error(o.ErrorKind.UNMATCHED_CLOSING_TAG,d(l,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">")?{val:{type:s.TYPE.tag,value:r,children:a,location:d(n,this.clonePosition())},err:null}:this.error(o.ErrorKind.INVALID_TAG,d(c,this.clonePosition())))}return this.error(o.ErrorKind.UNCLOSED_TAG,d(n,this.clonePosition()))}return this.error(o.ErrorKind.INVALID_TAG,d(n,this.clonePosition()))},e.prototype.parseTagName=function(){var e,t=this.offset();for(this.bump();!this.isEOF()&&(45===(e=this.char())||46===e||e>=48&&e<=57||95===e||e>=97&&e<=122||e>=65&&e<=90||183==e||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039);)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(e,t){for(var n=this.clonePosition(),r="";;){var i=this.tryParseQuote(t);if(i)r+=i;else{var o=this.tryParseUnquoted(e,t);if(o)r+=o;else{var a=this.tryParseLeftAngleBracket();if(!a)break;r+=a}}}var c=d(n,this.clonePosition());return{val:{type:s.TYPE.literal,value:r,location:c},err:null}},e.prototype.tryParseLeftAngleBracket=function(){return this.isEOF()||60!==this.char()||!this.ignoreTag&&(L(e=this.peek()||0)||47===e)?null:(this.bump(),"<");var e},e.prototype.tryParseQuote=function(e){if(this.isEOF()||39!==this.char())return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if("plural"===e||"selectordinal"===e)break;return null;default:return null}this.bump();var t=[this.char()];for(this.bump();!this.isEOF();){var n=this.char();if(39===n){if(39!==this.peek()){this.bump();break}t.push(39),this.bump()}else t.push(n);this.bump()}return x.apply(void 0,t)},e.prototype.tryParseUnquoted=function(e,t){if(this.isEOF())return null;var n=this.char();return 60===n||123===n||35===n&&("plural"===t||"selectordinal"===t)||125===n&&e>0?null:(this.bump(),x(n))},e.prototype.parseArgument=function(e,t){var n=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(o.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE,d(n,this.clonePosition()));if(125===this.char())return this.bump(),this.error(o.ErrorKind.EMPTY_ARGUMENT,d(n,this.clonePosition()));var r=this.parseIdentifierIfPossible().value;if(!r)return this.error(o.ErrorKind.MALFORMED_ARGUMENT,d(n,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(o.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE,d(n,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:s.TYPE.argument,value:r,location:d(n,this.clonePosition())},err:null};case 44:return this.bump(),this.bumpSpace(),this.isEOF()?this.error(o.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE,d(n,this.clonePosition())):this.parseArgumentOptions(e,t,r,n);default:return this.error(o.ErrorKind.MALFORMED_ARGUMENT,d(n,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var e=this.clonePosition(),t=this.offset(),n=_(this.message,t),r=t+n.length;return this.bumpTo(r),{value:n,location:d(e,this.clonePosition())}},e.prototype.parseArgumentOptions=function(e,t,n,r){var a,l=this.clonePosition(),h=this.parseIdentifierIfPossible().value,f=this.clonePosition();switch(h){case"":return this.error(o.ErrorKind.EXPECT_ARGUMENT_TYPE,d(l,f));case"number":case"date":case"time":this.bumpSpace();var p=null;if(this.bumpIf(",")){this.bumpSpace();var m=this.clonePosition();if((x=this.parseSimpleArgStyleIfPossible()).err)return x;if(0===(y=P(x.val)).length)return this.error(o.ErrorKind.EXPECT_ARGUMENT_STYLE,d(this.clonePosition(),this.clonePosition()));p={style:y,styleLocation:d(m,this.clonePosition())}}if((S=this.tryParseArgumentClose(r)).err)return S;var g=d(r,this.clonePosition());if(p&&T(null==p?void 0:p.style,"::",0)){var v=C(p.style.slice(2));if("number"===h)return(x=this.parseNumberSkeletonFromString(v,p.styleLocation)).err?x:{val:{type:s.TYPE.number,value:n,location:g,style:x.val},err:null};if(0===v.length)return this.error(o.ErrorKind.EXPECT_DATE_TIME_SKELETON,g);var E=v;this.locale&&(E=(0,u.getBestPattern)(v,this.locale));var y={type:s.SKELETON_TYPE.dateTime,pattern:E,location:p.styleLocation,parsedOptions:this.shouldParseSkeletons?(0,c.parseDateTimeSkeleton)(E):{}};return{val:{type:"date"===h?s.TYPE.date:s.TYPE.time,value:n,location:g,style:y},err:null}}return{val:{type:"number"===h?s.TYPE.number:"date"===h?s.TYPE.date:s.TYPE.time,value:n,location:g,style:null!==(a=null==p?void 0:p.style)&&void 0!==a?a:null},err:null};case"plural":case"selectordinal":case"select":var b=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(o.ErrorKind.EXPECT_SELECT_ARGUMENT_OPTIONS,d(b,(0,i.__assign)({},b)));this.bumpSpace();var w=this.parseIdentifierIfPossible(),_=0;if("select"!==h&&"offset"===w.value){if(!this.bumpIf(":"))return this.error(o.ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,d(this.clonePosition(),this.clonePosition()));var x;if(this.bumpSpace(),(x=this.tryParseDecimalInteger(o.ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,o.ErrorKind.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE)).err)return x;this.bumpSpace(),w=this.parseIdentifierIfPossible(),_=x.val}var S,R=this.tryParsePluralOrSelectOptions(e,h,t,w);if(R.err)return R;if((S=this.tryParseArgumentClose(r)).err)return S;var H=d(r,this.clonePosition());return"select"===h?{val:{type:s.TYPE.select,value:n,options:A(R.val),location:H},err:null}:{val:{type:s.TYPE.plural,value:n,options:A(R.val),offset:_,pluralType:"plural"===h?"cardinal":"ordinal",location:H},err:null};default:return this.error(o.ErrorKind.INVALID_ARGUMENT_TYPE,d(l,f))}},e.prototype.tryParseArgumentClose=function(e){return this.isEOF()||125!==this.char()?this.error(o.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE,d(e,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var e=0,t=this.clonePosition();!this.isEOF();){switch(this.char()){case 39:this.bump();var n=this.clonePosition();if(!this.bumpUntil("'"))return this.error(o.ErrorKind.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,d(n,this.clonePosition()));this.bump();break;case 123:e+=1,this.bump();break;case 125:if(!(e>0))return{val:this.message.slice(t.offset,this.offset()),err:null};e-=1;break;default:this.bump()}}return{val:this.message.slice(t.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(e,t){var n=[];try{n=(0,l.parseNumberSkeletonFromString)(e)}catch(r){return this.error(o.ErrorKind.INVALID_NUMBER_SKELETON,t)}return{val:{type:s.SKELETON_TYPE.number,tokens:n,location:t,parsedOptions:this.shouldParseSkeletons?(0,l.parseNumberSkeleton)(n):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(e,t,n,r){for(var i,s=!1,a=[],c=new Set,l=r.value,u=r.location;;){if(0===l.length){var h=this.clonePosition();if("select"===t||!this.bumpIf("="))break;var f=this.tryParseDecimalInteger(o.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR,o.ErrorKind.INVALID_PLURAL_ARGUMENT_SELECTOR);if(f.err)return f;u=d(h,this.clonePosition()),l=this.message.slice(h.offset,this.offset())}if(c.has(l))return this.error("select"===t?o.ErrorKind.DUPLICATE_SELECT_ARGUMENT_SELECTOR:o.ErrorKind.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,u);"other"===l&&(s=!0),this.bumpSpace();var p=this.clonePosition();if(!this.bumpIf("{"))return this.error("select"===t?o.ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:o.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,d(this.clonePosition(),this.clonePosition()));var m=this.parseMessage(e+1,t,n);if(m.err)return m;var g=this.tryParseArgumentClose(p);if(g.err)return g;a.push([l,{value:m.val,location:d(p,this.clonePosition())}]),c.add(l),this.bumpSpace(),l=(i=this.parseIdentifierIfPossible()).value,u=i.location}return 0===a.length?this.error("select"===t?o.ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR:o.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR,d(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!s?this.error(o.ErrorKind.MISSING_OTHER_CLAUSE,d(this.clonePosition(),this.clonePosition())):{val:a,err:null}},e.prototype.tryParseDecimalInteger=function(e,t){var n=1,r=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(n=-1);for(var i=!1,o=0;!this.isEOF();){var s=this.char();if(!(s>=48&&s<=57))break;i=!0,o=10*o+(s-48),this.bump()}var a=d(r,this.clonePosition());return i?b(o*=n)?{val:o,err:null}:this.error(t,a):this.error(e,a)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var e=this.position.offset;if(e>=this.message.length)throw Error("out of bound");var t=S(this.message,e);if(void 0===t)throw Error("Offset ".concat(e," is at invalid UTF-16 code unit boundary"));return t},e.prototype.error=function(e,t){return{val:null,err:{kind:e,message:this.message,location:t}}},e.prototype.bump=function(){if(!this.isEOF()){var e=this.char();10===e?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=e<65536?1:2)}},e.prototype.bumpIf=function(e){if(T(this.message,e,this.offset())){for(var t=0;t<e.length;t++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(e){var t=this.offset(),n=this.message.indexOf(e,t);return n>=0?(this.bumpTo(n),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(e){if(this.offset()>e)throw Error("targetOffset ".concat(e," must be greater than or equal to the current offset ").concat(this.offset()));for(e=Math.min(e,this.message.length);;){var t=this.offset();if(t===e)break;if(t>e)throw Error("targetOffset ".concat(e," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&N(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var e=this.char(),t=this.offset(),n=this.message.charCodeAt(t+(e>=65536?2:1));return null!=n?n:null},e}();function L(e){return e>=97&&e<=122||e>=65&&e<=90}function N(e){return e>=9&&e<=13||32===e||133===e||e>=8206&&e<=8207||8232===e||8233===e}function B(e){return e>=33&&e<=35||36===e||e>=37&&e<=39||40===e||41===e||42===e||43===e||44===e||45===e||e>=46&&e<=47||e>=58&&e<=59||e>=60&&e<=62||e>=63&&e<=64||91===e||92===e||93===e||94===e||96===e||123===e||124===e||125===e||126===e||161===e||e>=162&&e<=165||166===e||167===e||169===e||171===e||172===e||174===e||176===e||177===e||182===e||187===e||191===e||215===e||247===e||e>=8208&&e<=8213||e>=8214&&e<=8215||8216===e||8217===e||8218===e||e>=8219&&e<=8220||8221===e||8222===e||8223===e||e>=8224&&e<=8231||e>=8240&&e<=8248||8249===e||8250===e||e>=8251&&e<=8254||e>=8257&&e<=8259||8260===e||8261===e||8262===e||e>=8263&&e<=8273||8274===e||8275===e||e>=8277&&e<=8286||e>=8592&&e<=8596||e>=8597&&e<=8601||e>=8602&&e<=8603||e>=8604&&e<=8607||8608===e||e>=8609&&e<=8610||8611===e||e>=8612&&e<=8613||8614===e||e>=8615&&e<=8621||8622===e||e>=8623&&e<=8653||e>=8654&&e<=8655||e>=8656&&e<=8657||8658===e||8659===e||8660===e||e>=8661&&e<=8691||e>=8692&&e<=8959||e>=8960&&e<=8967||8968===e||8969===e||8970===e||8971===e||e>=8972&&e<=8991||e>=8992&&e<=8993||e>=8994&&e<=9e3||9001===e||9002===e||e>=9003&&e<=9083||9084===e||e>=9085&&e<=9114||e>=9115&&e<=9139||e>=9140&&e<=9179||e>=9180&&e<=9185||e>=9186&&e<=9254||e>=9255&&e<=9279||e>=9280&&e<=9290||e>=9291&&e<=9311||e>=9472&&e<=9654||9655===e||e>=9656&&e<=9664||9665===e||e>=9666&&e<=9719||e>=9720&&e<=9727||e>=9728&&e<=9838||9839===e||e>=9840&&e<=10087||10088===e||10089===e||10090===e||10091===e||10092===e||10093===e||10094===e||10095===e||10096===e||10097===e||10098===e||10099===e||10100===e||10101===e||e>=10132&&e<=10175||e>=10176&&e<=10180||10181===e||10182===e||e>=10183&&e<=10213||10214===e||10215===e||10216===e||10217===e||10218===e||10219===e||10220===e||10221===e||10222===e||10223===e||e>=10224&&e<=10239||e>=10240&&e<=10495||e>=10496&&e<=10626||10627===e||10628===e||10629===e||10630===e||10631===e||10632===e||10633===e||10634===e||10635===e||10636===e||10637===e||10638===e||10639===e||10640===e||10641===e||10642===e||10643===e||10644===e||10645===e||10646===e||10647===e||10648===e||e>=10649&&e<=10711||10712===e||10713===e||10714===e||10715===e||e>=10716&&e<=10747||10748===e||10749===e||e>=10750&&e<=11007||e>=11008&&e<=11055||e>=11056&&e<=11076||e>=11077&&e<=11078||e>=11079&&e<=11084||e>=11085&&e<=11123||e>=11124&&e<=11125||e>=11126&&e<=11157||11158===e||e>=11159&&e<=11263||e>=11776&&e<=11777||11778===e||11779===e||11780===e||11781===e||e>=11782&&e<=11784||11785===e||11786===e||11787===e||11788===e||11789===e||e>=11790&&e<=11798||11799===e||e>=11800&&e<=11801||11802===e||11803===e||11804===e||11805===e||e>=11806&&e<=11807||11808===e||11809===e||11810===e||11811===e||11812===e||11813===e||11814===e||11815===e||11816===e||11817===e||e>=11818&&e<=11822||11823===e||e>=11824&&e<=11833||e>=11834&&e<=11835||e>=11836&&e<=11839||11840===e||11841===e||11842===e||e>=11843&&e<=11855||e>=11856&&e<=11857||11858===e||e>=11859&&e<=11903||e>=12289&&e<=12291||12296===e||12297===e||12298===e||12299===e||12300===e||12301===e||12302===e||12303===e||12304===e||12305===e||e>=12306&&e<=12307||12308===e||12309===e||12310===e||12311===e||12312===e||12313===e||12314===e||12315===e||12316===e||12317===e||e>=12318&&e<=12319||12320===e||12336===e||64830===e||64831===e||e>=65093&&e<=65094}},334986:function(e,t,n){var r;!function(e,i,o){function s(e){var t=this,n="";t.next=function(){var e=t.x^t.x>>>2;return t.x=t.y,t.y=t.z,t.z=t.w,t.w=t.v,(t.d=t.d+362437|0)+(t.v=t.v^t.v<<4^e^e<<1)|0},t.x=0,t.y=0,t.z=0,t.w=0,t.v=0,e===(0|e)?t.x=e:n+=e;for(var r=0;r<n.length+64;r++)t.x^=0|n.charCodeAt(r),r==n.length&&(t.d=t.x<<10^t.x>>>4),t.next()}function a(e,t){return t.x=e.x,t.y=e.y,t.z=e.z,t.w=e.w,t.v=e.v,t.d=e.d,t}function c(e,t){var n=new s(e),r=t&&t.state,i=function(){return(n.next()>>>0)/4294967296};return i.double=function(){do{var e=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},i.int32=n.next,i.quick=i,r&&("object"==typeof r&&a(r,n),i.state=function(){return a(n,{})}),i}i&&i.exports?i.exports=c:n.amdD&&n.amdO?void 0===(r=function(){return c}.call(t,n,t,i))||(i.exports=r):this.xorwow=c}(0,e=n.nmd(e),n.amdD)},353514:(e,t,n)=>{"use strict";n.d(t,{timeData:()=>r});var r={"001":["H","h"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["H","h","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["H","hB","h","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["H","h","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["H","h","hB","hb"],CU:["H","h","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["H","hB","h","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["H","h","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["H","h","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["H","h","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["H","h","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["H","hB","h","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["H","h","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["H","h","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["H","h","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"es-BO":["H","h","hB","hb"],"es-BR":["H","h","hB","hb"],"es-EC":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"es-PE":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]}},358892:(e,t,n)=>{"use strict";n.d(t,{getBestPattern:()=>i});var r=n(353514);function i(e,t){for(var n="",r=0;r<e.length;r++){var i=e.charAt(r);if("j"===i){for(var s=0;r+1<e.length&&e.charAt(r+1)===i;)s++,r++;var a=1+(1&s),c=s<2?1:3+(s>>1),l=o(t);for("H"!=l&&"k"!=l||(c=0);c-- >0;)n+="a";for(;a-- >0;)n=l+n}else n+="J"===i?"H":i}return n}function o(e){var t=e.hourCycle;if(void 0===t&&e.hourCycles&&e.hourCycles.length&&(t=e.hourCycles[0]),t)switch(t){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw new Error("Invalid hourCycle")}var n,i=e.language;return"root"!==i&&(n=e.maximize().region),(r.timeData[n||""]||r.timeData[i||""]||r.timeData["".concat(i,"-001")]||r.timeData["001"])[0]}},374560:(e,t,n)=>{"use strict";n.d(t,{arrow:()=>a,autoPlacement:()=>c,computePosition:()=>o,detectOverflow:()=>s,flip:()=>l,hide:()=>f,inline:()=>p,limitShift:()=>v,offset:()=>m,shift:()=>g,size:()=>E});var r=n(612151);function i(e,t,n){let{reference:i,floating:o}=e;const s=(0,r.getSideAxis)(t),a=(0,r.getAlignmentAxis)(t),c=(0,r.getAxisLength)(a),l=(0,r.getSide)(t),u="y"===s,h=i.x+i.width/2-o.width/2,f=i.y+i.height/2-o.height/2,d=i[c]/2-o[c]/2;let p;switch(l){case"top":p={x:h,y:i.y-o.height};break;case"bottom":p={x:h,y:i.y+i.height};break;case"right":p={x:i.x+i.width,y:f};break;case"left":p={x:i.x-o.width,y:f};break;default:p={x:i.x,y:i.y}}switch((0,r.getAlignment)(t)){case"start":p[a]-=d*(n&&u?-1:1);break;case"end":p[a]+=d*(n&&u?-1:1)}return p}const o=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:s=[],platform:a}=n,c=s.filter(Boolean),l=await(null==a.isRTL?void 0:a.isRTL(t));let u=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:h,y:f}=i(u,r,l),d=r,p={},m=0;for(let g=0;g<c.length;g++){const{name:n,fn:s}=c[g],{x:v,y:E,data:y,reset:b}=await s({x:h,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:u,platform:a,elements:{reference:e,floating:t}});h=null!=v?v:h,f=null!=E?E:f,p={...p,[n]:{...p[n],...y}},b&&m<=50&&(m++,"object"==typeof b&&(b.placement&&(d=b.placement),b.rects&&(u=!0===b.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):b.rects),({x:h,y:f}=i(u,d,l))),g=-1)}return{x:h,y:f,placement:d,strategy:o,middlewareData:p}};async function s(e,t){var n;void 0===t&&(t={});const{x:i,y:o,platform:s,rects:a,elements:c,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:h="viewport",elementContext:f="floating",altBoundary:d=!1,padding:p=0}=(0,r.evaluate)(t,e),m=(0,r.getPaddingObject)(p),g=c[d?"floating"===f?"reference":"floating":f],v=(0,r.rectToClientRect)(await s.getClippingRect({element:null==(n=await(null==s.isElement?void 0:s.isElement(g)))||n?g:g.contextElement||await(null==s.getDocumentElement?void 0:s.getDocumentElement(c.floating)),boundary:u,rootBoundary:h,strategy:l})),E="floating"===f?{x:i,y:o,width:a.floating.width,height:a.floating.height}:a.reference,y=await(null==s.getOffsetParent?void 0:s.getOffsetParent(c.floating)),b=await(null==s.isElement?void 0:s.isElement(y))&&await(null==s.getScale?void 0:s.getScale(y))||{x:1,y:1},w=(0,r.rectToClientRect)(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:E,offsetParent:y,strategy:l}):E);return{top:(v.top-w.top+m.top)/b.y,bottom:(w.bottom-v.bottom+m.bottom)/b.y,left:(v.left-w.left+m.left)/b.x,right:(w.right-v.right+m.right)/b.x}}const a=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:i,placement:o,rects:s,platform:a,elements:c,middlewareData:l}=t,{element:u,padding:h=0}=(0,r.evaluate)(e,t)||{};if(null==u)return{};const f=(0,r.getPaddingObject)(h),d={x:n,y:i},p=(0,r.getAlignmentAxis)(o),m=(0,r.getAxisLength)(p),g=await a.getDimensions(u),v="y"===p,E=v?"top":"left",y=v?"bottom":"right",b=v?"clientHeight":"clientWidth",w=s.reference[m]+s.reference[p]-d[p]-s.floating[m],_=d[p]-s.reference[p],T=await(null==a.getOffsetParent?void 0:a.getOffsetParent(u));let x=T?T[b]:0;x&&await(null==a.isElement?void 0:a.isElement(T))||(x=c.floating[b]||s.floating[m]);const A=w/2-_/2,S=x/2-g[m]/2-1,C=(0,r.min)(f[E],S),P=(0,r.min)(f[y],S),R=C,H=x-g[m]-P,O=x/2-g[m]/2+A,L=(0,r.clamp)(R,O,H),N=!l.arrow&&null!=(0,r.getAlignment)(o)&&O!==L&&s.reference[m]/2-(O<R?C:P)-g[m]/2<0,B=N?O<R?O-R:O-H:0;return{[p]:d[p]+B,data:{[p]:L,centerOffset:O-L-B,...N&&{alignmentOffset:B}},reset:N}}});const c=function(e){return void 0===e&&(e={}),{name:"autoPlacement",options:e,async fn(t){var n,i,o;const{rects:a,middlewareData:c,placement:l,platform:u,elements:h}=t,{crossAxis:f=!1,alignment:d,allowedPlacements:p=r.placements,autoAlignment:m=!0,...g}=(0,r.evaluate)(e,t),v=void 0!==d||p===r.placements?function(e,t,n){return(e?[...n.filter((t=>(0,r.getAlignment)(t)===e)),...n.filter((t=>(0,r.getAlignment)(t)!==e))]:n.filter((e=>(0,r.getSide)(e)===e))).filter((n=>!e||(0,r.getAlignment)(n)===e||!!t&&(0,r.getOppositeAlignmentPlacement)(n)!==n))}(d||null,m,p):p,E=await s(t,g),y=(null==(n=c.autoPlacement)?void 0:n.index)||0,b=v[y];if(null==b)return{};const w=(0,r.getAlignmentSides)(b,a,await(null==u.isRTL?void 0:u.isRTL(h.floating)));if(l!==b)return{reset:{placement:v[0]}};const _=[E[(0,r.getSide)(b)],E[w[0]],E[w[1]]],T=[...(null==(i=c.autoPlacement)?void 0:i.overflows)||[],{placement:b,overflows:_}],x=v[y+1];if(x)return{data:{index:y+1,overflows:T},reset:{placement:x}};const A=T.map((e=>{const t=(0,r.getAlignment)(e.placement);return[e.placement,t&&f?e.overflows.slice(0,2).reduce(((e,t)=>e+t),0):e.overflows[0],e.overflows]})).sort(((e,t)=>e[1]-t[1])),S=(null==(o=A.filter((e=>e[2].slice(0,(0,r.getAlignment)(e[0])?2:3).every((e=>e<=0))))[0])?void 0:o[0])||A[0][0];return S!==l?{data:{index:y+1,overflows:T},reset:{placement:S}}:{}}}},l=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,i;const{placement:o,middlewareData:a,rects:c,initialPlacement:l,platform:u,elements:h}=t,{mainAxis:f=!0,crossAxis:d=!0,fallbackPlacements:p,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:v=!0,...E}=(0,r.evaluate)(e,t);if(null!=(n=a.arrow)&&n.alignmentOffset)return{};const y=(0,r.getSide)(o),b=(0,r.getSide)(l)===l,w=await(null==u.isRTL?void 0:u.isRTL(h.floating)),_=p||(b||!v?[(0,r.getOppositePlacement)(l)]:(0,r.getExpandedPlacements)(l));p||"none"===g||_.push(...(0,r.getOppositeAxisPlacements)(l,v,g,w));const T=[l,..._],x=await s(t,E),A=[];let S=(null==(i=a.flip)?void 0:i.overflows)||[];if(f&&A.push(x[y]),d){const e=(0,r.getAlignmentSides)(o,c,w);A.push(x[e[0]],x[e[1]])}if(S=[...S,{placement:o,overflows:A}],!A.every((e=>e<=0))){var C,P;const e=((null==(C=a.flip)?void 0:C.index)||0)+1,t=T[e];if(t)return{data:{index:e,overflows:S},reset:{placement:t}};let n=null==(P=S.filter((e=>e.overflows[0]<=0)).sort(((e,t)=>e.overflows[1]-t.overflows[1]))[0])?void 0:P.placement;if(!n)switch(m){case"bestFit":{var R;const e=null==(R=S.map((e=>[e.placement,e.overflows.filter((e=>e>0)).reduce(((e,t)=>e+t),0)])).sort(((e,t)=>e[1]-t[1]))[0])?void 0:R[0];e&&(n=e);break}case"initialPlacement":n=l}if(o!==n)return{reset:{placement:n}}}return{}}}};function u(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function h(e){return r.sides.some((t=>e[t]>=0))}const f=function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:i="referenceHidden",...o}=(0,r.evaluate)(e,t);switch(i){case"referenceHidden":{const e=u(await s(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:h(e)}}}case"escaped":{const e=u(await s(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:h(e)}}}default:return{}}}}};function d(e){const t=(0,r.min)(...e.map((e=>e.left))),n=(0,r.min)(...e.map((e=>e.top)));return{x:t,y:n,width:(0,r.max)(...e.map((e=>e.right)))-t,height:(0,r.max)(...e.map((e=>e.bottom)))-n}}const p=function(e){return void 0===e&&(e={}),{name:"inline",options:e,async fn(t){const{placement:n,elements:i,rects:o,platform:s,strategy:a}=t,{padding:c=2,x:l,y:u}=(0,r.evaluate)(e,t),h=Array.from(await(null==s.getClientRects?void 0:s.getClientRects(i.reference))||[]),f=function(e){const t=e.slice().sort(((e,t)=>e.y-t.y)),n=[];let i=null;for(let r=0;r<t.length;r++){const e=t[r];!i||e.y-i.y>i.height/2?n.push([e]):n[n.length-1].push(e),i=e}return n.map((e=>(0,r.rectToClientRect)(d(e))))}(h),p=(0,r.rectToClientRect)(d(h)),m=(0,r.getPaddingObject)(c);const g=await s.getElementRects({reference:{getBoundingClientRect:function(){if(2===f.length&&f[0].left>f[1].right&&null!=l&&null!=u)return f.find((e=>l>e.left-m.left&&l<e.right+m.right&&u>e.top-m.top&&u<e.bottom+m.bottom))||p;if(f.length>=2){if("y"===(0,r.getSideAxis)(n)){const e=f[0],t=f[f.length-1],i="top"===(0,r.getSide)(n),o=e.top,s=t.bottom,a=i?e.left:t.left,c=i?e.right:t.right;return{top:o,bottom:s,left:a,right:c,width:c-a,height:s-o,x:a,y:o}}const e="left"===(0,r.getSide)(n),t=(0,r.max)(...f.map((e=>e.right))),i=(0,r.min)(...f.map((e=>e.left))),o=f.filter((n=>e?n.left===i:n.right===t)),s=o[0].top,a=o[o.length-1].bottom;return{top:s,bottom:a,left:i,right:t,width:t-i,height:a-s,x:i,y:s}}return p}},floating:i.floating,strategy:a});return o.reference.x!==g.reference.x||o.reference.y!==g.reference.y||o.reference.width!==g.reference.width||o.reference.height!==g.reference.height?{reset:{rects:g}}:{}}}};const m=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,i;const{x:o,y:s,placement:a,middlewareData:c}=t,l=await async function(e,t){const{placement:n,platform:i,elements:o}=e,s=await(null==i.isRTL?void 0:i.isRTL(o.floating)),a=(0,r.getSide)(n),c=(0,r.getAlignment)(n),l="y"===(0,r.getSideAxis)(n),u=["left","top"].includes(a)?-1:1,h=s&&l?-1:1,f=(0,r.evaluate)(t,e);let{mainAxis:d,crossAxis:p,alignmentAxis:m}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...f};return c&&"number"==typeof m&&(p="end"===c?-1*m:m),l?{x:p*h,y:d*u}:{x:d*u,y:p*h}}(t,e);return a===(null==(n=c.offset)?void 0:n.placement)&&null!=(i=c.arrow)&&i.alignmentOffset?{}:{x:o+l.x,y:s+l.y,data:{...l,placement:a}}}}},g=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:i,placement:o}=t,{mainAxis:a=!0,crossAxis:c=!1,limiter:l={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=(0,r.evaluate)(e,t),h={x:n,y:i},f=await s(t,u),d=(0,r.getSideAxis)((0,r.getSide)(o)),p=(0,r.getOppositeAxis)(d);let m=h[p],g=h[d];if(a){const e="y"===p?"bottom":"right",t=m+f["y"===p?"top":"left"],n=m-f[e];m=(0,r.clamp)(t,m,n)}if(c){const e="y"===d?"bottom":"right",t=g+f["y"===d?"top":"left"],n=g-f[e];g=(0,r.clamp)(t,g,n)}const v=l.fn({...t,[p]:m,[d]:g});return{...v,data:{x:v.x-n,y:v.y-i}}}}},v=function(e){return void 0===e&&(e={}),{options:e,fn(t){const{x:n,y:i,placement:o,rects:s,middlewareData:a}=t,{offset:c=0,mainAxis:l=!0,crossAxis:u=!0}=(0,r.evaluate)(e,t),h={x:n,y:i},f=(0,r.getSideAxis)(o),d=(0,r.getOppositeAxis)(f);let p=h[d],m=h[f];const g=(0,r.evaluate)(c,t),v="number"==typeof g?{mainAxis:g,crossAxis:0}:{mainAxis:0,crossAxis:0,...g};if(l){const e="y"===d?"height":"width",t=s.reference[d]-s.floating[e]+v.mainAxis,n=s.reference[d]+s.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(u){var E,y;const e="y"===d?"width":"height",t=["top","left"].includes((0,r.getSide)(o)),n=s.reference[f]-s.floating[e]+(t&&(null==(E=a.offset)?void 0:E[f])||0)+(t?0:v.crossAxis),i=s.reference[f]+s.reference[e]+(t?0:(null==(y=a.offset)?void 0:y[f])||0)-(t?v.crossAxis:0);m<n?m=n:m>i&&(m=i)}return{[d]:p,[f]:m}}}},E=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){const{placement:n,rects:i,platform:o,elements:a}=t,{apply:c=(()=>{}),...l}=(0,r.evaluate)(e,t),u=await s(t,l),h=(0,r.getSide)(n),f=(0,r.getAlignment)(n),d="y"===(0,r.getSideAxis)(n),{width:p,height:m}=i.floating;let g,v;"top"===h||"bottom"===h?(g=h,v=f===(await(null==o.isRTL?void 0:o.isRTL(a.floating))?"start":"end")?"left":"right"):(v=h,g="end"===f?"top":"bottom");const E=m-u.top-u.bottom,y=p-u.left-u.right,b=(0,r.min)(m-u[g],E),w=(0,r.min)(p-u[v],y),_=!t.middlewareData.shift;let T=b,x=w;if(d?x=f||_?(0,r.min)(w,y):y:T=f||_?(0,r.min)(b,E):E,_&&!f){const e=(0,r.max)(u.left,0),t=(0,r.max)(u.right,0),n=(0,r.max)(u.top,0),i=(0,r.max)(u.bottom,0);d?x=p-2*(0!==e||0!==t?e+t:(0,r.max)(u.left,u.right)):T=m-2*(0!==n||0!==i?n+i:(0,r.max)(u.top,u.bottom))}await c({...t,availableWidth:x,availableHeight:T});const A=await o.getDimensions(a.floating);return p!==A.width||m!==A.height?{reset:{rects:!0}}:{}}}}},434008:(e,t,n)=>{"use strict";n.d(t,{default:()=>i});var r=n(675421);function i(e,t){e.classList?e.classList.add(t):(0,r.default)(e,t)||("string"==typeof e.className?e.className=e.className+" "+t:e.setAttribute("class",(e.className&&e.className.baseVal||"")+" "+t))}},451118:function(e,t,n){var r;!function(e,i,o){function s(e){var t=this;t.next=function(){var e,n,r=t.w,i=t.X,o=t.i;return t.w=r=r+1640531527|0,n=i[o+34&127],e=i[o=o+1&127],n^=n<<13,e^=e<<17,n^=n>>>15,e^=e>>>12,n=i[o]=n^e,t.i=o,n+(r^r>>>16)|0},function(e,t){var n,r,i,o,s,a=[],c=128;for(t===(0|t)?(r=t,t=null):(t+="\0",r=0,c=Math.max(c,t.length)),i=0,o=-32;o<c;++o)t&&(r^=t.charCodeAt((o+32)%t.length)),0===o&&(s=r),r^=r<<10,r^=r>>>15,r^=r<<4,r^=r>>>13,o>=0&&(s=s+1640531527|0,i=0==(n=a[127&o]^=r+s)?i+1:0);for(i>=128&&(a[127&(t&&t.length||0)]=-1),i=127,o=512;o>0;--o)r=a[i+34&127],n=a[i=i+1&127],r^=r<<13,n^=n<<17,r^=r>>>15,n^=n>>>12,a[i]=r^n;e.w=s,e.X=a,e.i=i}(t,e)}function a(e,t){return t.i=e.i,t.w=e.w,t.X=e.X.slice(),t}function c(e,t){null==e&&(e=+new Date);var n=new s(e),r=t&&t.state,i=function(){return(n.next()>>>0)/4294967296};return i.double=function(){do{var e=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},i.int32=n.next,i.quick=i,r&&(r.X&&a(r,n),i.state=function(){return a(n,{})}),i}i&&i.exports?i.exports=c:n.amdD&&n.amdO?void 0===(r=function(){return c}.call(t,n,t,i))||(i.exports=r):this.xor4096=c}(0,e=n.nmd(e),n.amdD)},484201:(e,t,n)=>{e.exports=n(76901)()},493668:function(e,t,n){var r;!function(e,i,o){function s(e){var t=this,n="";t.x=0,t.y=0,t.z=0,t.w=0,t.next=function(){var e=t.x^t.x<<11;return t.x=t.y,t.y=t.z,t.z=t.w,t.w^=t.w>>>19^e^e>>>8},e===(0|e)?t.x=e:n+=e;for(var r=0;r<n.length+64;r++)t.x^=0|n.charCodeAt(r),t.next()}function a(e,t){return t.x=e.x,t.y=e.y,t.z=e.z,t.w=e.w,t}function c(e,t){var n=new s(e),r=t&&t.state,i=function(){return(n.next()>>>0)/4294967296};return i.double=function(){do{var e=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},i.int32=n.next,i.quick=i,r&&("object"==typeof r&&a(r,n),i.state=function(){return a(n,{})}),i}i&&i.exports?i.exports=c:n.amdD&&n.amdO?void 0===(r=function(){return c}.call(t,n,t,i))||(i.exports=r):this.xor128=c}(0,e=n.nmd(e),n.amdD)},499401:()=>{},523546:(e,t,n)=>{"use strict";n.d(t,{isTabbable:()=>T,tabbable:()=>_});var r=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"],i=r.join(","),o="undefined"==typeof Element,s=o?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,a=!o&&Element.prototype.getRootNode?function(e){var t;return null==e||null===(t=e.getRootNode)||void 0===t?void 0:t.call(e)}:function(e){return null==e?void 0:e.ownerDocument},c=function e(t,n){var r;void 0===n&&(n=!0);var i=null==t||null===(r=t.getAttribute)||void 0===r?void 0:r.call(t,"inert");return""===i||"true"===i||n&&t&&e(t.parentNode)},l=function(e,t,n){if(c(e))return[];var r=Array.prototype.slice.apply(e.querySelectorAll(i));return t&&s.call(e,i)&&r.unshift(e),r=r.filter(n)},u=function e(t,n,r){for(var o=[],a=Array.from(t);a.length;){var l=a.shift();if(!c(l,!1))if("SLOT"===l.tagName){var u=l.assignedElements(),h=e(u.length?u:l.children,!0,r);r.flatten?o.push.apply(o,h):o.push({scopeParent:l,candidates:h})}else{s.call(l,i)&&r.filter(l)&&(n||!t.includes(l))&&o.push(l);var f=l.shadowRoot||"function"==typeof r.getShadowRoot&&r.getShadowRoot(l),d=!c(f,!1)&&(!r.shadowRootFilter||r.shadowRootFilter(l));if(f&&d){var p=e(!0===f?l.children:f.children,!0,r);r.flatten?o.push.apply(o,p):o.push({scopeParent:l,candidates:p})}else a.unshift.apply(a,l.children)}}return o},h=function(e){return!isNaN(parseInt(e.getAttribute("tabindex"),10))},f=function(e){if(!e)throw new Error("No node provided");return e.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||function(e){var t,n=null==e||null===(t=e.getAttribute)||void 0===t?void 0:t.call(e,"contenteditable");return""===n||"true"===n}(e))&&!h(e)?0:e.tabIndex},d=function(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex},p=function(e){return"INPUT"===e.tagName},m=function(e){return function(e){return p(e)&&"radio"===e.type}(e)&&!function(e){if(!e.name)return!0;var t,n=e.form||a(e),r=function(e){return n.querySelectorAll('input[type="radio"][name="'+e+'"]')};if("undefined"!=typeof window&&void 0!==window.CSS&&"function"==typeof window.CSS.escape)t=r(window.CSS.escape(e.name));else try{t=r(e.name)}catch(o){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",o.message),!1}var i=function(e,t){for(var n=0;n<e.length;n++)if(e[n].checked&&e[n].form===t)return e[n]}(t,e.form);return!i||i===e}(e)},g=function(e){var t=e.getBoundingClientRect(),n=t.width,r=t.height;return 0===n&&0===r},v=function(e,t){var n=t.displayCheck,r=t.getShadowRoot;if("hidden"===getComputedStyle(e).visibility)return!0;var i=s.call(e,"details>summary:first-of-type")?e.parentElement:e;if(s.call(i,"details:not([open]) *"))return!0;if(n&&"full"!==n&&"legacy-full"!==n){if("non-zero-area"===n)return g(e)}else{if("function"==typeof r){for(var o=e;e;){var c=e.parentElement,l=a(e);if(c&&!c.shadowRoot&&!0===r(c))return g(e);e=e.assignedSlot?e.assignedSlot:c||l===e.ownerDocument?c:l.host}e=o}if(function(e){var t,n,r,i,o=e&&a(e),s=null===(t=o)||void 0===t?void 0:t.host,c=!1;if(o&&o!==e)for(c=!!(null!==(n=s)&&void 0!==n&&null!==(r=n.ownerDocument)&&void 0!==r&&r.contains(s)||null!=e&&null!==(i=e.ownerDocument)&&void 0!==i&&i.contains(e));!c&&s;){var l,u,h;c=!(null===(u=s=null===(l=o=a(s))||void 0===l?void 0:l.host)||void 0===u||null===(h=u.ownerDocument)||void 0===h||!h.contains(s))}return c}(e))return!e.getClientRects().length;if("legacy-full"!==n)return!0}return!1},E=function(e,t){return!(t.disabled||c(t)||function(e){return p(e)&&"hidden"===e.type}(t)||v(t,e)||function(e){return"DETAILS"===e.tagName&&Array.prototype.slice.apply(e.children).some((function(e){return"SUMMARY"===e.tagName}))}(t)||function(e){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(var t=e.parentElement;t;){if("FIELDSET"===t.tagName&&t.disabled){for(var n=0;n<t.children.length;n++){var r=t.children.item(n);if("LEGEND"===r.tagName)return!!s.call(t,"fieldset[disabled] *")||!r.contains(e)}return!0}t=t.parentElement}return!1}(t))},y=function(e,t){return!(m(t)||f(t)<0||!E(e,t))},b=function(e){var t=parseInt(e.getAttribute("tabindex"),10);return!!(isNaN(t)||t>=0)},w=function e(t){var n=[],r=[];return t.forEach((function(t,i){var o=!!t.scopeParent,s=o?t.scopeParent:t,a=function(e,t){var n=f(e);return n<0&&t&&!h(e)?0:n}(s,o),c=o?e(t.candidates):s;0===a?o?n.push.apply(n,c):n.push(s):r.push({documentOrder:i,tabIndex:a,item:t,isScope:o,content:c})})),r.sort(d).reduce((function(e,t){return t.isScope?e.push.apply(e,t.content):e.push(t.content),e}),[]).concat(n)},_=function(e,t){var n;return n=(t=t||{}).getShadowRoot?u([e],t.includeContainer,{filter:y.bind(null,t),flatten:!1,getShadowRoot:t.getShadowRoot,shadowRootFilter:b}):l(e,t.includeContainer,y.bind(null,t)),w(n)},T=function(e,t){if(t=t||{},!e)throw new Error("No node provided");return!1!==s.call(e,i)&&y(t,e)}},553824:(e,t,n)=>{"use strict";n.d(t,{parseNumberSkeleton:()=>p,parseNumberSkeletonFromString:()=>o});var r=n(944339),i=n(106853);function o(e){if(0===e.length)throw new Error("Number skeleton cannot be empty");for(var t=[],n=0,r=e.split(i.WHITE_SPACE_REGEX).filter((function(e){return e.length>0}));n<r.length;n++){var o=r[n].split("/");if(0===o.length)throw new Error("Invalid number skeleton");for(var s=o[0],a=o.slice(1),c=0,l=a;c<l.length;c++){if(0===l[c].length)throw new Error("Invalid number skeleton")}t.push({stem:s,options:a})}return t}var s=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,a=/^(@+)?(\+|#+)?[rs]?$/g,c=/(\*)(0+)|(#+)(0+)|(0+)/g,l=/^(0+)$/;function u(e){var t={};return"r"===e[e.length-1]?t.roundingPriority="morePrecision":"s"===e[e.length-1]&&(t.roundingPriority="lessPrecision"),e.replace(a,(function(e,n,r){return"string"!=typeof r?(t.minimumSignificantDigits=n.length,t.maximumSignificantDigits=n.length):"+"===r?t.minimumSignificantDigits=n.length:"#"===n[0]?t.maximumSignificantDigits=n.length:(t.minimumSignificantDigits=n.length,t.maximumSignificantDigits=n.length+("string"==typeof r?r.length:0)),""})),t}function h(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function f(e){var t;if("E"===e[0]&&"E"===e[1]?(t={notation:"engineering"},e=e.slice(2)):"E"===e[0]&&(t={notation:"scientific"},e=e.slice(1)),t){var n=e.slice(0,2);if("+!"===n?(t.signDisplay="always",e=e.slice(2)):"+?"===n&&(t.signDisplay="exceptZero",e=e.slice(2)),!l.test(e))throw new Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}function d(e){var t=h(e);return t||{}}function p(e){for(var t={},n=0,i=e;n<i.length;n++){var o=i[n];switch(o.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=o.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=o.options[0].replace(/^(.*?)-/,"");continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=(0,r.__assign)((0,r.__assign)((0,r.__assign)({},t),{notation:"scientific"}),o.options.reduce((function(e,t){return(0,r.__assign)((0,r.__assign)({},e),d(t))}),{}));continue;case"engineering":t=(0,r.__assign)((0,r.__assign)((0,r.__assign)({},t),{notation:"engineering"}),o.options.reduce((function(e,t){return(0,r.__assign)((0,r.__assign)({},e),d(t))}),{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(o.options[0]);continue;case"rounding-mode-floor":t.roundingMode="floor";continue;case"rounding-mode-ceiling":t.roundingMode="ceil";continue;case"rounding-mode-down":t.roundingMode="trunc";continue;case"rounding-mode-up":t.roundingMode="expand";continue;case"rounding-mode-half-even":t.roundingMode="halfEven";continue;case"rounding-mode-half-down":t.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":t.roundingMode="halfExpand";continue;case"integer-width":if(o.options.length>1)throw new RangeError("integer-width stems only accept a single optional option");o.options[0].replace(c,(function(e,n,r,i,o,s){if(n)t.minimumIntegerDigits=r.length;else{if(i&&o)throw new Error("We currently do not support maximum integer digits");if(s)throw new Error("We currently do not support exact integer digits")}return""}));continue}if(l.test(o.stem))t.minimumIntegerDigits=o.stem.length;else if(s.test(o.stem)){if(o.options.length>1)throw new RangeError("Fraction-precision stems only accept a single optional option");o.stem.replace(s,(function(e,n,r,i,o,s){return"*"===r?t.minimumFractionDigits=n.length:i&&"#"===i[0]?t.maximumFractionDigits=i.length:o&&s?(t.minimumFractionDigits=o.length,t.maximumFractionDigits=o.length+s.length):(t.minimumFractionDigits=n.length,t.maximumFractionDigits=n.length),""}));var p=o.options[0];"w"===p?t=(0,r.__assign)((0,r.__assign)({},t),{trailingZeroDisplay:"stripIfInteger"}):p&&(t=(0,r.__assign)((0,r.__assign)({},t),u(p)))}else if(a.test(o.stem))t=(0,r.__assign)((0,r.__assign)({},t),u(o.stem));else{var m=h(o.stem);m&&(t=(0,r.__assign)((0,r.__assign)({},t),m));var g=f(o.stem);g&&(t=(0,r.__assign)((0,r.__assign)({},t),g))}}return t}},556020:(e,t,n)=>{"use strict";n.d(t,{arrow:()=>E,flip:()=>g,hide:()=>v,limitShift:()=>m,offset:()=>d,shift:()=>p,useFloating:()=>h});var r=n(821046),i=n(845212),o=n(921885),s="undefined"!=typeof document?i.useLayoutEffect:i.useEffect;function a(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;let n,r,i;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;0!=r--;)if(!a(e[r],t[r]))return!1;return!0}if(i=Object.keys(e),n=i.length,n!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!{}.hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){const n=i[r];if(("_owner"!==n||!e.$$typeof)&&!a(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function c(e){if("undefined"==typeof window)return 1;return(e.ownerDocument.defaultView||window).devicePixelRatio||1}function l(e,t){const n=c(e);return Math.round(t*n)/n}function u(e){const t=i.useRef(e);return s((()=>{t.current=e})),t}function h(e){void 0===e&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:h=[],platform:f,elements:{reference:d,floating:p}={},transform:m=!0,whileElementsMounted:g,open:v}=e,[E,y]=i.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[b,w]=i.useState(h);a(b,h)||w(h);const[_,T]=i.useState(null),[x,A]=i.useState(null),S=i.useCallback((e=>{e!==H.current&&(H.current=e,T(e))}),[]),C=i.useCallback((e=>{e!==O.current&&(O.current=e,A(e))}),[]),P=d||_,R=p||x,H=i.useRef(null),O=i.useRef(null),L=i.useRef(E),N=null!=g,B=u(g),M=u(f),I=i.useCallback((()=>{if(!H.current||!O.current)return;const e={placement:t,strategy:n,middleware:b};M.current&&(e.platform=M.current),(0,r.computePosition)(H.current,O.current,e).then((e=>{const t={...e,isPositioned:!0};D.current&&!a(L.current,t)&&(L.current=t,o.flushSync((()=>{y(t)})))}))}),[b,t,n,M]);s((()=>{!1===v&&L.current.isPositioned&&(L.current.isPositioned=!1,y((e=>({...e,isPositioned:!1}))))}),[v]);const D=i.useRef(!1);s((()=>(D.current=!0,()=>{D.current=!1})),[]),s((()=>{if(P&&(H.current=P),R&&(O.current=R),P&&R){if(B.current)return B.current(P,R,I);I()}}),[P,R,I,B,N]);const k=i.useMemo((()=>({reference:H,floating:O,setReference:S,setFloating:C})),[S,C]),U=i.useMemo((()=>({reference:P,floating:R})),[P,R]),G=i.useMemo((()=>{const e={position:n,left:0,top:0};if(!U.floating)return e;const t=l(U.floating,E.x),r=l(U.floating,E.y);return m?{...e,transform:"translate("+t+"px, "+r+"px)",...c(U.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}}),[n,m,U.floating,E.x,E.y]);return i.useMemo((()=>({...E,update:I,refs:k,elements:U,floatingStyles:G})),[E,I,k,U,G])}const f=e=>({name:"arrow",options:e,fn(t){const{element:n,padding:i}="function"==typeof e?e(t):e;return n&&(o=n,{}.hasOwnProperty.call(o,"current"))?null!=n.current?(0,r.arrow)({element:n.current,padding:i}).fn(t):{}:n?(0,r.arrow)({element:n,padding:i}).fn(t):{};var o}}),d=(e,t)=>({...(0,r.offset)(e),options:[e,t]}),p=(e,t)=>({...(0,r.shift)(e),options:[e,t]}),m=(e,t)=>({...(0,r.limitShift)(e),options:[e,t]}),g=(e,t)=>({...(0,r.flip)(e),options:[e,t]}),v=(e,t)=>({...(0,r.hide)(e),options:[e,t]}),E=(e,t)=>({...f(e),options:[e,t]})},565800:function(e,t,n){var r;!function(e,i,o){function s(e){var t=this;t.next=function(){var e,n,r=t.x,i=t.i;return e=r[i],n=(e^=e>>>7)^e<<24,n^=(e=r[i+1&7])^e>>>10,n^=(e=r[i+3&7])^e>>>3,n^=(e=r[i+4&7])^e<<7,e=r[i+7&7],n^=(e^=e<<13)^e<<9,r[i]=n,t.i=i+1&7,n},function(e,t){var n,r=[];if(t===(0|t))r[0]=t;else for(t=""+t,n=0;n<t.length;++n)r[7&n]=r[7&n]<<15^t.charCodeAt(n)+r[n+1&7]<<13;for(;r.length<8;)r.push(0);for(n=0;n<8&&0===r[n];++n);for(8==n?r[7]=-1:r[n],e.x=r,e.i=0,n=256;n>0;--n)e.next()}(t,e)}function a(e,t){return t.x=e.x.slice(),t.i=e.i,t}function c(e,t){null==e&&(e=+new Date);var n=new s(e),r=t&&t.state,i=function(){return(n.next()>>>0)/4294967296};return i.double=function(){do{var e=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},i.int32=n.next,i.quick=i,r&&(r.x&&a(r,n),i.state=function(){return a(n,{})}),i}i&&i.exports?i.exports=c:n.amdD&&n.amdO?void 0===(r=function(){return c}.call(t,n,t,i))||(i.exports=r):this.xorshift7=c}(0,e=n.nmd(e),n.amdD)},588587:(e,t,n)=>{"use strict";var r,i=n(776266);e=n.hmd(e),r="undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==n.g?n.g:e;(0,i.default)(r)},591365:(e,t,n)=>{"use strict";n.d(t,{onCLS:()=>S,onFCP:()=>A,onFID:()=>N,onINP:()=>K,onLCP:()=>X});var r,i,o,s,a,c=-1,l=function(e){addEventListener("pageshow",(function(t){t.persisted&&(c=t.timeStamp,e(t))}),!0)},u=function(){return window.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]},h=function(){var e=u();return e&&e.activationStart||0},f=function(e,t){var n=u(),r="navigate";return c>=0?r="back-forward-cache":n&&(r=document.prerendering||h()>0?"prerender":document.wasDiscarded?"restore":n.type.replace(/_/g,"-")),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v3-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:r}},d=function(e,t,n){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var r=new PerformanceObserver((function(e){Promise.resolve().then((function(){t(e.getEntries())}))}));return r.observe(Object.assign({type:e,buffered:!0},n||{})),r}}catch(e){}},p=function(e,t,n,r){var i,o;return function(s){t.value>=0&&(s||r)&&((o=t.value-(i||0))||void 0===i)&&(i=t.value,t.delta=o,t.rating=function(e,t){return e>t[1]?"poor":e>t[0]?"needs-improvement":"good"}(t.value,n),e(t))}},m=function(e){requestAnimationFrame((function(){return requestAnimationFrame((function(){return e()}))}))},g=function(e){var t=function(t){"pagehide"!==t.type&&"hidden"!==document.visibilityState||e(t)};addEventListener("visibilitychange",t,!0),addEventListener("pagehide",t,!0)},v=function(e){var t=!1;return function(n){t||(e(n),t=!0)}},E=-1,y=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},b=function(e){"hidden"===document.visibilityState&&E>-1&&(E="visibilitychange"===e.type?e.timeStamp:0,_())},w=function(){addEventListener("visibilitychange",b,!0),addEventListener("prerenderingchange",b,!0)},_=function(){removeEventListener("visibilitychange",b,!0),removeEventListener("prerenderingchange",b,!0)},T=function(){return E<0&&(E=y(),w(),l((function(){setTimeout((function(){E=y(),w()}),0)}))),{get firstHiddenTime(){return E}}},x=function(e){document.prerendering?addEventListener("prerenderingchange",(function(){return e()}),!0):e()},A=function(e,t){t=t||{},x((function(){var n,r=[1800,3e3],i=T(),o=f("FCP"),s=d("paint",(function(e){e.forEach((function(e){"first-contentful-paint"===e.name&&(s.disconnect(),e.startTime<i.firstHiddenTime&&(o.value=Math.max(e.startTime-h(),0),o.entries.push(e),n(!0)))}))}));s&&(n=p(e,o,r,t.reportAllChanges),l((function(i){o=f("FCP"),n=p(e,o,r,t.reportAllChanges),m((function(){o.value=performance.now()-i.timeStamp,n(!0)}))})))}))},S=function(e,t){t=t||{},A(v((function(){var n,r=[.1,.25],i=f("CLS",0),o=0,s=[],a=function(e){e.forEach((function(e){if(!e.hadRecentInput){var t=s[0],n=s[s.length-1];o&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(o+=e.value,s.push(e)):(o=e.value,s=[e])}})),o>i.value&&(i.value=o,i.entries=s,n())},c=d("layout-shift",a);c&&(n=p(e,i,r,t.reportAllChanges),g((function(){a(c.takeRecords()),n(!0)})),l((function(){o=0,i=f("CLS",0),n=p(e,i,r,t.reportAllChanges),m((function(){return n()}))})),setTimeout(n,0))})))},C={passive:!0,capture:!0},P=new Date,R=function(e,t){r||(r=t,i=e,o=new Date,L(removeEventListener),H())},H=function(){if(i>=0&&i<o-P){var e={entryType:"first-input",name:r.type,target:r.target,cancelable:r.cancelable,startTime:r.timeStamp,processingStart:r.timeStamp+i};s.forEach((function(t){t(e)})),s=[]}},O=function(e){if(e.cancelable){var t=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;"pointerdown"==e.type?function(e,t){var n=function(){R(e,t),i()},r=function(){i()},i=function(){removeEventListener("pointerup",n,C),removeEventListener("pointercancel",r,C)};addEventListener("pointerup",n,C),addEventListener("pointercancel",r,C)}(t,e):R(t,e)}},L=function(e){["mousedown","keydown","touchstart","pointerdown"].forEach((function(t){return e(t,O,C)}))},N=function(e,t){t=t||{},x((function(){var n,o=[100,300],a=T(),c=f("FID"),u=function(e){e.startTime<a.firstHiddenTime&&(c.value=e.processingStart-e.startTime,c.entries.push(e),n(!0))},h=function(e){e.forEach(u)},m=d("first-input",h);n=p(e,c,o,t.reportAllChanges),m&&g(v((function(){h(m.takeRecords()),m.disconnect()}))),m&&l((function(){var a;c=f("FID"),n=p(e,c,o,t.reportAllChanges),s=[],i=-1,r=null,L(addEventListener),a=u,s.push(a),H()}))}))},B=0,M=1/0,I=0,D=function(e){e.forEach((function(e){e.interactionId&&(M=Math.min(M,e.interactionId),I=Math.max(I,e.interactionId),B=I?(I-M)/7+1:0)}))},k=function(){return a?B:performance.interactionCount||0},U=function(){"interactionCount"in performance||a||(a=d("event",D,{type:"event",buffered:!0,durationThreshold:0}))},G=0,F=function(){return k()-G},V=[],j={},z=function(e){var t=V[V.length-1],n=j[e.interactionId];if(n||V.length<10||e.duration>t.latency){if(n)n.entries.push(e),n.latency=Math.max(n.latency,e.duration);else{var r={id:e.interactionId,latency:e.duration,entries:[e]};j[r.id]=r,V.push(r)}V.sort((function(e,t){return t.latency-e.latency})),V.splice(10).forEach((function(e){delete j[e.id]}))}},K=function(e,t){t=t||{},x((function(){var n=[200,500];U();var r,i=f("INP"),o=function(e){e.forEach((function(e){e.interactionId&&z(e),"first-input"===e.entryType&&!V.some((function(t){return t.entries.some((function(t){return e.duration===t.duration&&e.startTime===t.startTime}))}))&&z(e)}));var t,n=(t=Math.min(V.length-1,Math.floor(F()/50)),V[t]);n&&n.latency!==i.value&&(i.value=n.latency,i.entries=n.entries,r())},s=d("event",o,{durationThreshold:t.durationThreshold||40});r=p(e,i,n,t.reportAllChanges),s&&(s.observe({type:"first-input",buffered:!0}),g((function(){o(s.takeRecords()),i.value<0&&F()>0&&(i.value=0,i.entries=[]),r(!0)})),l((function(){V=[],G=k(),i=f("INP"),r=p(e,i,n,t.reportAllChanges)})))}))},W={},X=function(e,t){t=t||{},x((function(){var n,r=[2500,4e3],i=T(),o=f("LCP"),s=function(e){var t=e[e.length-1];if(t){var r=Math.max(t.startTime-h(),0);r<i.firstHiddenTime&&(o.value=r,o.entries=[t],n())}},a=d("largest-contentful-paint",s);if(a){n=p(e,o,r,t.reportAllChanges);var c=v((function(){W[o.id]||(s(a.takeRecords()),a.disconnect(),W[o.id]=!0,n(!0))}));["keydown","click"].forEach((function(e){addEventListener(e,c,!0)})),g(c),l((function(i){o=f("LCP"),n=p(e,o,r,t.reportAllChanges),m((function(){o.value=performance.now()-i.timeStamp,W[o.id]=!0,n(!0)}))}))}}))}},607597:(e,t,n)=>{"use strict";n.d(t,{isArgumentElement:()=>s.isArgumentElement,isDateElement:()=>s.isDateElement,isDateTimeSkeleton:()=>s.isDateTimeSkeleton,isLiteralElement:()=>s.isLiteralElement,isNumberElement:()=>s.isNumberElement,isNumberSkeleton:()=>s.isNumberSkeleton,isPluralElement:()=>s.isPluralElement,isPoundElement:()=>s.isPoundElement,isSelectElement:()=>s.isSelectElement,isTagElement:()=>s.isTagElement,isTimeElement:()=>s.isTimeElement,parse:()=>c});var r=n(944339),i=n(810487),o=n(332386),s=n(15924);function a(e){e.forEach((function(e){if(delete e.location,(0,s.isSelectElement)(e)||(0,s.isPluralElement)(e))for(var t in e.options)delete e.options[t].location,a(e.options[t].value);else(0,s.isNumberElement)(e)&&(0,s.isNumberSkeleton)(e.style)||((0,s.isDateElement)(e)||(0,s.isTimeElement)(e))&&(0,s.isDateTimeSkeleton)(e.style)?delete e.style.location:(0,s.isTagElement)(e)&&a(e.children)}))}function c(e,t){void 0===t&&(t={}),t=(0,r.__assign)({shouldParseSkeletons:!0,requiresOtherClause:!0},t);var n=new o.Parser(e,t).parse();if(n.err){var s=SyntaxError(i.ErrorKind[n.err.kind]);throw s.location=n.err.location,s.originalMessage=n.err.message,s}return(null==t?void 0:t.captureLocation)||a(n.val),n.val}o.Parser},612151:(e,t,n)=>{"use strict";n.d(t,{clamp:()=>d,createCoords:()=>u,evaluate:()=>p,floor:()=>l,getAlignment:()=>g,getAlignmentAxis:()=>b,getAlignmentSides:()=>w,getAxisLength:()=>E,getExpandedPlacements:()=>_,getOppositeAlignmentPlacement:()=>T,getOppositeAxis:()=>v,getOppositeAxisPlacements:()=>x,getOppositePlacement:()=>A,getPaddingObject:()=>S,getSide:()=>m,getSideAxis:()=>y,max:()=>a,min:()=>s,placements:()=>o,rectToClientRect:()=>C,round:()=>c,sides:()=>r});const r=["top","right","bottom","left"],i=["start","end"],o=r.reduce(((e,t)=>e.concat(t,t+"-"+i[0],t+"-"+i[1])),[]),s=Math.min,a=Math.max,c=Math.round,l=Math.floor,u=e=>({x:e,y:e}),h={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function d(e,t,n){return a(e,s(t,n))}function p(e,t){return"function"==typeof e?e(t):e}function m(e){return e.split("-")[0]}function g(e){return e.split("-")[1]}function v(e){return"x"===e?"y":"x"}function E(e){return"y"===e?"height":"width"}function y(e){return["top","bottom"].includes(m(e))?"y":"x"}function b(e){return v(y(e))}function w(e,t,n){void 0===n&&(n=!1);const r=g(e),i=b(e),o=E(i);let s="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(s=A(s)),[s,A(s)]}function _(e){const t=A(e);return[T(e),t,T(t)]}function T(e){return e.replace(/start|end/g,(e=>f[e]))}function x(e,t,n,r){const i=g(e);let o=function(e,t,n){const r=["left","right"],i=["right","left"],o=["top","bottom"],s=["bottom","top"];switch(e){case"top":case"bottom":return n?t?i:r:t?r:i;case"left":case"right":return t?o:s;default:return[]}}(m(e),"start"===n,r);return i&&(o=o.map((e=>e+"-"+i)),t&&(o=o.concat(o.map(T)))),o}function A(e){return e.replace(/left|right|bottom|top/g,(e=>h[e]))}function S(e){return"number"!=typeof e?function(e){return{top:0,right:0,bottom:0,left:0,...e}}(e):{top:e,right:e,bottom:e,left:e}}function C(e){const{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}},675421:(e,t,n)=>{"use strict";function r(e,t){return e.classList?!!t&&e.classList.contains(t):-1!==(" "+(e.className.baseVal||e.className)+" ").indexOf(" "+t+" ")}n.d(t,{default:()=>r})},680811:(e,t,n)=>{"use strict";n.d(t,{parseDateTimeSkeleton:()=>i});var r=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;function i(e){var t={};return e.replace(r,(function(e){var n=e.length;switch(e[0]){case"G":t.era=4===n?"long":5===n?"narrow":"short";break;case"y":t.year=2===n?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw new RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw new RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":t.month=["numeric","2-digit","short","long","narrow"][n-1];break;case"w":case"W":throw new RangeError("`w/W` (week) patterns are not supported");case"d":t.day=["numeric","2-digit"][n-1];break;case"D":case"F":case"g":throw new RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":t.weekday=4===n?"long":5===n?"narrow":"short";break;case"e":if(n<4)throw new RangeError("`e..eee` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][n-4];break;case"c":if(n<4)throw new RangeError("`c..ccc` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][n-4];break;case"a":t.hour12=!0;break;case"b":case"B":throw new RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":t.hourCycle="h12",t.hour=["numeric","2-digit"][n-1];break;case"H":t.hourCycle="h23",t.hour=["numeric","2-digit"][n-1];break;case"K":t.hourCycle="h11",t.hour=["numeric","2-digit"][n-1];break;case"k":t.hourCycle="h24",t.hour=["numeric","2-digit"][n-1];break;case"j":case"J":case"C":throw new RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":t.minute=["numeric","2-digit"][n-1];break;case"s":t.second=["numeric","2-digit"][n-1];break;case"S":case"A":throw new RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":t.timeZoneName=n<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw new RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""})),t}},688125:(e,t,n)=>{"use strict";n.d(t,{SPACE_SEPARATOR_REGEX:()=>r});var r=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/},689420:function(e,t,n){var r;!function(i,o,s){var a,c=256,l=s.pow(c,6),u=s.pow(2,52),h=2*u,f=c-1;function d(e,t,n){var r=[],f=v(g((t=1==t?{entropy:!0}:t||{}).entropy?[e,E(o)]:null==e?function(){try{var e;return a&&(e=a.randomBytes)?e=e(c):(e=new Uint8Array(c),(i.crypto||i.msCrypto).getRandomValues(e)),E(e)}catch(r){var t=i.navigator,n=t&&t.plugins;return[+new Date,i,n,i.screen,E(o)]}}():e,3),r),d=new p(r),y=function(){for(var e=d.g(6),t=l,n=0;e<u;)e=(e+n)*c,t*=c,n=d.g(1);for(;e>=h;)e/=2,t/=2,n>>>=1;return(e+n)/t};return y.int32=function(){return 0|d.g(4)},y.quick=function(){return d.g(4)/4294967296},y.double=y,v(E(d.S),o),(t.pass||n||function(e,t,n,r){return r&&(r.S&&m(r,d),e.state=function(){return m(d,{})}),n?(s.random=e,t):e})(y,f,"global"in t?t.global:this==s,t.state)}function p(e){var t,n=e.length,r=this,i=0,o=r.i=r.j=0,s=r.S=[];for(n||(e=[n++]);i<c;)s[i]=i++;for(i=0;i<c;i++)s[i]=s[o=f&o+e[i%n]+(t=s[i])],s[o]=t;(r.g=function(e){for(var t,n=0,i=r.i,o=r.j,s=r.S;e--;)t=s[i=f&i+1],n=n*c+s[f&(s[i]=s[o=f&o+t])+(s[o]=t)];return r.i=i,r.j=o,n})(c)}function m(e,t){return t.i=e.i,t.j=e.j,t.S=e.S.slice(),t}function g(e,t){var n,r=[],i=typeof e;if(t&&"object"==i)for(n in e)try{r.push(g(e[n],t-1))}catch(o){}return r.length?r:"string"==i?e:e+"\0"}function v(e,t){for(var n,r=e+"",i=0;i<r.length;)t[f&i]=f&(n^=19*t[f&i])+r.charCodeAt(i++);return E(t)}function E(e){return String.fromCharCode.apply(0,e)}if(v(s.random(),o),e.exports){e.exports=d;try{a=n(499401)}catch(y){}}else void 0===(r=function(){return d}.call(t,n,t,e))||(e.exports=r)}("undefined"!=typeof self?self:this,[],Math)},710821:(e,t,n)=>{"use strict";n.d(t,{default:()=>i});var r=n(982828);function i(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,(0,r.default)(e,t)}},754838:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},776266:(e,t,n)=>{"use strict";function r(e){var t,n=e.Symbol;if("function"==typeof n)if(n.observable)t=n.observable;else{t="function"==typeof n.for?n.for("https://github.com/benlesh/symbol-observable"):n("https://github.com/benlesh/symbol-observable");try{n.observable=t}catch(r){}}else t="@@observable";return t}n.d(t,{default:()=>r})},787475:(e,t,n)=>{"use strict";function r(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}n.d(t,{default:()=>r})},810487:(e,t,n)=>{"use strict";var r;n.d(t,{ErrorKind:()=>r}),function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"}(r||(r={}))},821046:(e,t,n)=>{"use strict";n.d(t,{arrow:()=>H,autoPlacement:()=>A,autoUpdate:()=>_,computePosition:()=>N,detectOverflow:()=>T,flip:()=>C,hide:()=>R,inline:()=>O,limitShift:()=>L,offset:()=>x,platform:()=>w,shift:()=>S,size:()=>P});var r=n(612151),i=n(374560),o=n(276089);function s(e){const t=(0,o.getComputedStyle)(e);let n=parseFloat(t.width)||0,i=parseFloat(t.height)||0;const s=(0,o.isHTMLElement)(e),a=s?e.offsetWidth:n,c=s?e.offsetHeight:i,l=(0,r.round)(n)!==a||(0,r.round)(i)!==c;return l&&(n=a,i=c),{width:n,height:i,$:l}}function a(e){return(0,o.isElement)(e)?e:e.contextElement}function c(e){const t=a(e);if(!(0,o.isHTMLElement)(t))return(0,r.createCoords)(1);const n=t.getBoundingClientRect(),{width:i,height:c,$:l}=s(t);let u=(l?(0,r.round)(n.width):n.width)/i,h=(l?(0,r.round)(n.height):n.height)/c;return u&&Number.isFinite(u)||(u=1),h&&Number.isFinite(h)||(h=1),{x:u,y:h}}const l=(0,r.createCoords)(0);function u(e){const t=(0,o.getWindow)(e);return(0,o.isWebKit)()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:l}function h(e,t,n,i){void 0===t&&(t=!1),void 0===n&&(n=!1);const s=e.getBoundingClientRect(),l=a(e);let h=(0,r.createCoords)(1);t&&(i?(0,o.isElement)(i)&&(h=c(i)):h=c(e));const f=function(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==(0,o.getWindow)(e))&&t}(l,n,i)?u(l):(0,r.createCoords)(0);let d=(s.left+f.x)/h.x,p=(s.top+f.y)/h.y,m=s.width/h.x,g=s.height/h.y;if(l){const e=(0,o.getWindow)(l),t=i&&(0,o.isElement)(i)?(0,o.getWindow)(i):i;let n=e,r=n.frameElement;for(;r&&i&&t!==n;){const e=c(r),t=r.getBoundingClientRect(),i=(0,o.getComputedStyle)(r),s=t.left+(r.clientLeft+parseFloat(i.paddingLeft))*e.x,a=t.top+(r.clientTop+parseFloat(i.paddingTop))*e.y;d*=e.x,p*=e.y,m*=e.x,g*=e.y,d+=s,p+=a,n=(0,o.getWindow)(r),r=n.frameElement}}return(0,r.rectToClientRect)({width:m,height:g,x:d,y:p})}const f=[":popover-open",":modal"];function d(e){return f.some((t=>{try{return e.matches(t)}catch(n){return!1}}))}function p(e){return h((0,o.getDocumentElement)(e)).left+(0,o.getNodeScroll)(e).scrollLeft}function m(e,t,n){let i;if("viewport"===t)i=function(e,t){const n=(0,o.getWindow)(e),r=(0,o.getDocumentElement)(e),i=n.visualViewport;let s=r.clientWidth,a=r.clientHeight,c=0,l=0;if(i){s=i.width,a=i.height;const e=(0,o.isWebKit)();(!e||e&&"fixed"===t)&&(c=i.offsetLeft,l=i.offsetTop)}return{width:s,height:a,x:c,y:l}}(e,n);else if("document"===t)i=function(e){const t=(0,o.getDocumentElement)(e),n=(0,o.getNodeScroll)(e),i=e.ownerDocument.body,s=(0,r.max)(t.scrollWidth,t.clientWidth,i.scrollWidth,i.clientWidth),a=(0,r.max)(t.scrollHeight,t.clientHeight,i.scrollHeight,i.clientHeight);let c=-n.scrollLeft+p(e);const l=-n.scrollTop;return"rtl"===(0,o.getComputedStyle)(i).direction&&(c+=(0,r.max)(t.clientWidth,i.clientWidth)-s),{width:s,height:a,x:c,y:l}}((0,o.getDocumentElement)(e));else if((0,o.isElement)(t))i=function(e,t){const n=h(e,!0,"fixed"===t),i=n.top+e.clientTop,s=n.left+e.clientLeft,a=(0,o.isHTMLElement)(e)?c(e):(0,r.createCoords)(1);return{width:e.clientWidth*a.x,height:e.clientHeight*a.y,x:s*a.x,y:i*a.y}}(t,n);else{const n=u(e);i={...t,x:t.x-n.x,y:t.y-n.y}}return(0,r.rectToClientRect)(i)}function g(e,t){const n=(0,o.getParentNode)(e);return!(n===t||!(0,o.isElement)(n)||(0,o.isLastTraversableNode)(n))&&("fixed"===(0,o.getComputedStyle)(n).position||g(n,t))}function v(e,t,n){const i=(0,o.isHTMLElement)(t),s=(0,o.getDocumentElement)(t),a="fixed"===n,c=h(e,!0,a,t);let l={scrollLeft:0,scrollTop:0};const u=(0,r.createCoords)(0);if(i||!i&&!a)if(("body"!==(0,o.getNodeName)(t)||(0,o.isOverflowElement)(s))&&(l=(0,o.getNodeScroll)(t)),i){const e=h(t,!0,a,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else s&&(u.x=p(s));return{x:c.left+l.scrollLeft-u.x,y:c.top+l.scrollTop-u.y,width:c.width,height:c.height}}function E(e){return"static"===(0,o.getComputedStyle)(e).position}function y(e,t){return(0,o.isHTMLElement)(e)&&"fixed"!==(0,o.getComputedStyle)(e).position?t?t(e):e.offsetParent:null}function b(e,t){const n=(0,o.getWindow)(e);if(d(e))return n;if(!(0,o.isHTMLElement)(e)){let t=(0,o.getParentNode)(e);for(;t&&!(0,o.isLastTraversableNode)(t);){if((0,o.isElement)(t)&&!E(t))return t;t=(0,o.getParentNode)(t)}return n}let r=y(e,t);for(;r&&(0,o.isTableElement)(r)&&E(r);)r=y(r,t);return r&&(0,o.isLastTraversableNode)(r)&&E(r)&&!(0,o.isContainingBlock)(r)?n:r||(0,o.getContainingBlock)(e)||n}const w={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:i,strategy:s}=e;const a="fixed"===s,l=(0,o.getDocumentElement)(i),u=!!t&&d(t.floating);if(i===l||u&&a)return n;let f={scrollLeft:0,scrollTop:0},p=(0,r.createCoords)(1);const m=(0,r.createCoords)(0),g=(0,o.isHTMLElement)(i);if((g||!g&&!a)&&(("body"!==(0,o.getNodeName)(i)||(0,o.isOverflowElement)(l))&&(f=(0,o.getNodeScroll)(i)),(0,o.isHTMLElement)(i))){const e=h(i);p=c(i),m.x=e.x+i.clientLeft,m.y=e.y+i.clientTop}return{width:n.width*p.x,height:n.height*p.y,x:n.x*p.x-f.scrollLeft*p.x+m.x,y:n.y*p.y-f.scrollTop*p.y+m.y}},getDocumentElement:o.getDocumentElement,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:i,strategy:s}=e;const a=[..."clippingAncestors"===n?d(t)?[]:function(e,t){const n=t.get(e);if(n)return n;let r=(0,o.getOverflowAncestors)(e,[],!1).filter((e=>(0,o.isElement)(e)&&"body"!==(0,o.getNodeName)(e))),i=null;const s="fixed"===(0,o.getComputedStyle)(e).position;let a=s?(0,o.getParentNode)(e):e;for(;(0,o.isElement)(a)&&!(0,o.isLastTraversableNode)(a);){const t=(0,o.getComputedStyle)(a),n=(0,o.isContainingBlock)(a);n||"fixed"!==t.position||(i=null),(s?!n&&!i:!n&&"static"===t.position&&i&&["absolute","fixed"].includes(i.position)||(0,o.isOverflowElement)(a)&&!n&&g(e,a))?r=r.filter((e=>e!==a)):i=t,a=(0,o.getParentNode)(a)}return t.set(e,r),r}(t,this._c):[].concat(n),i],c=a[0],l=a.reduce(((e,n)=>{const i=m(t,n,s);return e.top=(0,r.max)(i.top,e.top),e.right=(0,r.min)(i.right,e.right),e.bottom=(0,r.min)(i.bottom,e.bottom),e.left=(0,r.max)(i.left,e.left),e}),m(t,c,s));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:b,getElementRects:async function(e){const t=this.getOffsetParent||b,n=this.getDimensions,r=await n(e.floating);return{reference:v(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){const{width:t,height:n}=s(e);return{width:t,height:n}},getScale:c,isElement:o.isElement,isRTL:function(e){return"rtl"===(0,o.getComputedStyle)(e).direction}};function _(e,t,n,i){void 0===i&&(i={});const{ancestorScroll:s=!0,ancestorResize:c=!0,elementResize:l="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:f=!1}=i,d=a(e),p=s||c?[...d?(0,o.getOverflowAncestors)(d):[],...(0,o.getOverflowAncestors)(t)]:[];p.forEach((e=>{s&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)}));const m=d&&u?function(e,t){let n,i=null;const s=(0,o.getDocumentElement)(e);function a(){var e;clearTimeout(n),null==(e=i)||e.disconnect(),i=null}return function o(c,l){void 0===c&&(c=!1),void 0===l&&(l=1),a();const{left:u,top:h,width:f,height:d}=e.getBoundingClientRect();if(c||t(),!f||!d)return;const p={rootMargin:-(0,r.floor)(h)+"px "+-(0,r.floor)(s.clientWidth-(u+f))+"px "+-(0,r.floor)(s.clientHeight-(h+d))+"px "+-(0,r.floor)(u)+"px",threshold:(0,r.max)(0,(0,r.min)(1,l))||1};let m=!0;function g(e){const t=e[0].intersectionRatio;if(t!==l){if(!m)return o();t?o(!1,t):n=setTimeout((()=>{o(!1,1e-7)}),1e3)}m=!1}try{i=new IntersectionObserver(g,{...p,root:s.ownerDocument})}catch(v){i=new IntersectionObserver(g,p)}i.observe(e)}(!0),a}(d,n):null;let g,v=-1,E=null;l&&(E=new ResizeObserver((e=>{let[r]=e;r&&r.target===d&&E&&(E.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame((()=>{var e;null==(e=E)||e.observe(t)}))),n()})),d&&!f&&E.observe(d),E.observe(t));let y=f?h(e):null;return f&&function t(){const r=h(e);!y||r.x===y.x&&r.y===y.y&&r.width===y.width&&r.height===y.height||n();y=r,g=requestAnimationFrame(t)}(),n(),()=>{var e;p.forEach((e=>{s&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)})),null==m||m(),null==(e=E)||e.disconnect(),E=null,f&&cancelAnimationFrame(g)}}const T=i.detectOverflow,x=i.offset,A=i.autoPlacement,S=i.shift,C=i.flip,P=i.size,R=i.hide,H=i.arrow,O=i.inline,L=i.limitShift,N=(e,t,n)=>{const r=new Map,o={platform:w,...n},s={...o.platform,_c:r};return(0,i.computePosition)(e,t,{...o,platform:s})}},833299:(e,t,n)=>{"use strict";n.d(t,{ENTERED:()=>f,ENTERING:()=>h,EXITED:()=>u,EXITING:()=>d,UNMOUNTED:()=>l,default:()=>g});var r=n(165669),i=n(710821),o=n(845212),s=n(921885),a=n(964130),c=n(158385),l="unmounted",u="exited",h="entering",f="entered",d="exiting",p=function(e){function t(t,n){var r;r=e.call(this,t,n)||this;var i,o=n&&!n.isMounting?t.enter:t.appear;return r.appearStatus=null,t.in?o?(i=u,r.appearStatus=h):i=f:i=t.unmountOnExit||t.mountOnEnter?l:u,r.state={status:i},r.nextCallback=null,r}(0,i.default)(t,e),t.getDerivedStateFromProps=function(e,t){return e.in&&t.status===l?{status:u}:null};var n=t.prototype;return n.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},n.componentDidUpdate=function(e){var t=null;if(e!==this.props){var n=this.state.status;this.props.in?n!==h&&n!==f&&(t=h):n!==h&&n!==f||(t=d)}this.updateStatus(!1,t)},n.componentWillUnmount=function(){this.cancelNextCallback()},n.getTimeouts=function(){var e,t,n,r=this.props.timeout;return e=t=n=r,null!=r&&"number"!=typeof r&&(e=r.exit,t=r.enter,n=void 0!==r.appear?r.appear:t),{exit:e,enter:t,appear:n}},n.updateStatus=function(e,t){void 0===e&&(e=!1),null!==t?(this.cancelNextCallback(),t===h?this.performEnter(e):this.performExit()):this.props.unmountOnExit&&this.state.status===u&&this.setState({status:l})},n.performEnter=function(e){var t=this,n=this.props.enter,r=this.context?this.context.isMounting:e,i=this.props.nodeRef?[r]:[s.findDOMNode(this),r],o=i[0],c=i[1],l=this.getTimeouts(),u=r?l.appear:l.enter;!e&&!n||a.default.disabled?this.safeSetState({status:f},(function(){t.props.onEntered(o)})):(this.props.onEnter(o,c),this.safeSetState({status:h},(function(){t.props.onEntering(o,c),t.onTransitionEnd(u,(function(){t.safeSetState({status:f},(function(){t.props.onEntered(o,c)}))}))})))},n.performExit=function(){var e=this,t=this.props.exit,n=this.getTimeouts(),r=this.props.nodeRef?void 0:s.findDOMNode(this);t&&!a.default.disabled?(this.props.onExit(r),this.safeSetState({status:d},(function(){e.props.onExiting(r),e.onTransitionEnd(n.exit,(function(){e.safeSetState({status:u},(function(){e.props.onExited(r)}))}))}))):this.safeSetState({status:u},(function(){e.props.onExited(r)}))},n.cancelNextCallback=function(){null!==this.nextCallback&&(this.nextCallback.cancel(),this.nextCallback=null)},n.safeSetState=function(e,t){t=this.setNextCallback(t),this.setState(e,t)},n.setNextCallback=function(e){var t=this,n=!0;return this.nextCallback=function(r){n&&(n=!1,t.nextCallback=null,e(r))},this.nextCallback.cancel=function(){n=!1},this.nextCallback},n.onTransitionEnd=function(e,t){this.setNextCallback(t);var n=this.props.nodeRef?this.props.nodeRef.current:s.findDOMNode(this),r=null==e&&!this.props.addEndListener;if(n&&!r){if(this.props.addEndListener){var i=this.props.nodeRef?[this.nextCallback]:[n,this.nextCallback],o=i[0],a=i[1];this.props.addEndListener(o,a)}null!=e&&setTimeout(this.nextCallback,e)}else setTimeout(this.nextCallback,0)},n.render=function(){var e=this.state.status;if(e===l)return null;var t=this.props,n=t.children,i=(t.in,t.mountOnEnter,t.unmountOnExit,t.appear,t.enter,t.exit,t.timeout,t.addEndListener,t.onEnter,t.onEntering,t.onEntered,t.onExit,t.onExiting,t.onExited,t.nodeRef,(0,r.default)(t,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]));return o.createElement(c.default.Provider,{value:null},"function"==typeof n?n(e,i):o.cloneElement(o.Children.only(n),i))},t}(o.Component);function m(){}p.contextType=c.default,p.propTypes={},p.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:m,onEntering:m,onEntered:m,onExit:m,onExiting:m,onExited:m},p.UNMOUNTED=l,p.EXITED=u,p.ENTERING=h,p.ENTERED=f,p.EXITING=d;const g=p},853469:(e,t,n)=>{"use strict";n.d(t,{PART_TYPE:()=>r,formatToParts:()=>a});var r,i=n(607597),o=n(873066);function s(e){return"function"==typeof e}function a(e,t,n,c,l,u,h){if(1===e.length&&(0,i.isLiteralElement)(e[0]))return[{type:r.literal,value:e[0].value}];for(var f=[],d=0,p=e;d<p.length;d++){var m=p[d];if((0,i.isLiteralElement)(m))f.push({type:r.literal,value:m.value});else if((0,i.isPoundElement)(m))"number"==typeof u&&f.push({type:r.literal,value:n.getNumberFormat(t).format(u)});else{var g=m.value;if(!l||!(g in l))throw new o.MissingValueError(g,h);var v=l[g];if((0,i.isArgumentElement)(m))v&&"string"!=typeof v&&"number"!=typeof v||(v="string"==typeof v||"number"==typeof v?String(v):""),f.push({type:"string"==typeof v?r.literal:r.object,value:v});else if((0,i.isDateElement)(m)){var E="string"==typeof m.style?c.date[m.style]:(0,i.isDateTimeSkeleton)(m.style)?m.style.parsedOptions:void 0;f.push({type:r.literal,value:n.getDateTimeFormat(t,E).format(v)})}else if((0,i.isTimeElement)(m)){E="string"==typeof m.style?c.time[m.style]:(0,i.isDateTimeSkeleton)(m.style)?m.style.parsedOptions:c.time.medium;f.push({type:r.literal,value:n.getDateTimeFormat(t,E).format(v)})}else if((0,i.isNumberElement)(m)){(E="string"==typeof m.style?c.number[m.style]:(0,i.isNumberSkeleton)(m.style)?m.style.parsedOptions:void 0)&&E.scale&&(v*=E.scale||1),f.push({type:r.literal,value:n.getNumberFormat(t,E).format(v)})}else{if((0,i.isTagElement)(m)){var y=m.children,b=m.value,w=l[b];if(!s(w))throw new o.InvalidValueTypeError(b,"function",h);var _=w(a(y,t,n,c,l,u).map((function(e){return e.value})));Array.isArray(_)||(_=[_]),f.push.apply(f,_.map((function(e){return{type:"string"==typeof e?r.literal:r.object,value:e}})))}if((0,i.isSelectElement)(m)){if(!(T=m.options[v]||m.options.other))throw new o.InvalidValueError(m.value,v,Object.keys(m.options),h);f.push.apply(f,a(T.value,t,n,c,l))}else if((0,i.isPluralElement)(m)){var T;if(!(T=m.options["=".concat(v)])){if(!Intl.PluralRules)throw new o.FormatError('Intl.PluralRules is not available in this environment.\nTry polyfilling it using "@formatjs/intl-pluralrules"\n',o.ErrorCode.MISSING_INTL_API,h);var x=n.getPluralRules(t,{type:m.pluralType}).select(v-(m.offset||0));T=m.options[x]||m.options.other}if(!T)throw new o.InvalidValueError(m.value,v,Object.keys(m.options),h);f.push.apply(f,a(T.value,t,n,c,l,v-(m.offset||0)))}else;}}}return function(e){return e.length<2?e:e.reduce((function(e,t){var n=e[e.length-1];return n&&n.type===r.literal&&t.type===r.literal?n.value+=t.value:e.push(t),e}),[])}(f)}!function(e){e[e.literal=0]="literal",e[e.object=1]="object"}(r||(r={}))},873066:(e,t,n)=>{"use strict";n.d(t,{ErrorCode:()=>r,FormatError:()=>o,InvalidValueError:()=>s,InvalidValueTypeError:()=>a,MissingValueError:()=>c});var r,i=n(944339);!function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"}(r||(r={}));var o=function(e){function t(t,n,r){var i=e.call(this,t)||this;return i.code=n,i.originalMessage=r,i}return(0,i.__extends)(t,e),t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),s=function(e){function t(t,n,i,o){return e.call(this,'Invalid values for "'.concat(t,'": "').concat(n,'". Options are "').concat(Object.keys(i).join('", "'),'"'),r.INVALID_VALUE,o)||this}return(0,i.__extends)(t,e),t}(o),a=function(e){function t(t,n,i){return e.call(this,'Value for "'.concat(t,'" must be of type ').concat(n),r.INVALID_VALUE,i)||this}return(0,i.__extends)(t,e),t}(o),c=function(e){function t(t,n){return e.call(this,'The intl string context variable "'.concat(t,'" was not provided to the string "').concat(n,'"'),r.MISSING_VALUE,n)||this}return(0,i.__extends)(t,e),t}(o)},876461:function(e,t,n){var r;!function(e,i,o){function s(e){var t=this,n=function(){var e=4022871197,t=function(t){t=String(t);for(var n=0;n<t.length;n++){var r=.02519603282416938*(e+=t.charCodeAt(n));r-=e=r>>>0,e=(r*=e)>>>0,e+=4294967296*(r-=e)}return 2.3283064365386963e-10*(e>>>0)};return t}();t.next=function(){var e=2091639*t.s0+2.3283064365386963e-10*t.c;return t.s0=t.s1,t.s1=t.s2,t.s2=e-(t.c=0|e)},t.c=1,t.s0=n(" "),t.s1=n(" "),t.s2=n(" "),t.s0-=n(e),t.s0<0&&(t.s0+=1),t.s1-=n(e),t.s1<0&&(t.s1+=1),t.s2-=n(e),t.s2<0&&(t.s2+=1),n=null}function a(e,t){return t.c=e.c,t.s0=e.s0,t.s1=e.s1,t.s2=e.s2,t}function c(e,t){var n=new s(e),r=t&&t.state,i=n.next;return i.int32=function(){return 4294967296*n.next()|0},i.double=function(){return i()+11102230246251565e-32*(2097152*i()|0)},i.quick=i,r&&("object"==typeof r&&a(r,n),i.state=function(){return a(n,{})}),i}i&&i.exports?i.exports=c:n.amdD&&n.amdO?void 0===(r=function(){return c}.call(t,n,t,i))||(i.exports=r):this.alea=c}(0,e=n.nmd(e),n.amdD)},882262:e=>{!function(){function t(e,t){var n,r,i,o,s,a,c,l;for(n=3&e.length,r=e.length-n,i=t,s=3432918353,a=461845907,l=0;l<r;)c=255&e.charCodeAt(l)|(255&e.charCodeAt(++l))<<8|(255&e.charCodeAt(++l))<<16|(255&e.charCodeAt(++l))<<24,++l,i=27492+(65535&(o=5*(65535&(i=(i^=c=(65535&(c=(c=(65535&c)*s+(((c>>>16)*s&65535)<<16)&4294967295)<<15|c>>>17))*a+(((c>>>16)*a&65535)<<16)&4294967295)<<13|i>>>19))+((5*(i>>>16)&65535)<<16)&4294967295))+((58964+(o>>>16)&65535)<<16);switch(c=0,n){case 3:c^=(255&e.charCodeAt(l+2))<<16;case 2:c^=(255&e.charCodeAt(l+1))<<8;case 1:i^=c=(65535&(c=(c=(65535&(c^=255&e.charCodeAt(l)))*s+(((c>>>16)*s&65535)<<16)&4294967295)<<15|c>>>17))*a+(((c>>>16)*a&65535)<<16)&4294967295}return i^=e.length,i=2246822507*(65535&(i^=i>>>16))+((2246822507*(i>>>16)&65535)<<16)&4294967295,i=3266489909*(65535&(i^=i>>>13))+((3266489909*(i>>>16)&65535)<<16)&4294967295,(i^=i>>>16)>>>0}var n=t;n.v2=function(e,t){for(var n,r=e.length,i=t^r,o=0;r>=4;)n=1540483477*(65535&(n=255&e.charCodeAt(o)|(255&e.charCodeAt(++o))<<8|(255&e.charCodeAt(++o))<<16|(255&e.charCodeAt(++o))<<24))+((1540483477*(n>>>16)&65535)<<16),i=1540483477*(65535&i)+((1540483477*(i>>>16)&65535)<<16)^(n=1540483477*(65535&(n^=n>>>24))+((1540483477*(n>>>16)&65535)<<16)),r-=4,++o;switch(r){case 3:i^=(255&e.charCodeAt(o+2))<<16;case 2:i^=(255&e.charCodeAt(o+1))<<8;case 1:i=1540483477*(65535&(i^=255&e.charCodeAt(o)))+((1540483477*(i>>>16)&65535)<<16)}return i=1540483477*(65535&(i^=i>>>13))+((1540483477*(i>>>16)&65535)<<16),(i^=i>>>15)>>>0},n.v3=t,e.exports=n}()},895590:(e,t,n)=>{"use strict";n.d(t,{activeElement:()=>i,contains:()=>o,getDocument:()=>g,getPlatform:()=>s,getTarget:()=>E,getUserAgent:()=>a,isEventTargetWithin:()=>v,isMac:()=>f,isMouseLikePointerType:()=>d,isReactEvent:()=>p,isRootElement:()=>m,isSafari:()=>u,isTypeableCombobox:()=>_,isTypeableElement:()=>b,isVirtualClick:()=>c,isVirtualPointerEvent:()=>l,stopEvent:()=>w});var r=n(276089);function i(e){let t=e.activeElement;for(;null!=(null==(n=t)||null==(n=n.shadowRoot)?void 0:n.activeElement);){var n;t=t.shadowRoot.activeElement}return t}function o(e,t){if(!e||!t)return!1;const n=null==t.getRootNode?void 0:t.getRootNode();if(e.contains(t))return!0;if(n&&(0,r.isShadowRoot)(n)){let n=t;for(;n;){if(e===n)return!0;n=n.parentNode||n.host}}return!1}function s(){const e=navigator.userAgentData;return null!=e&&e.platform?e.platform:navigator.platform}function a(){const e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map((e=>{let{brand:t,version:n}=e;return t+"/"+n})).join(" "):navigator.userAgent}function c(e){return!(0!==e.mozInputSource||!e.isTrusted)||(h()&&e.pointerType?"click"===e.type&&1===e.buttons:0===e.detail&&!e.pointerType)}function l(e){return!a().includes("jsdom/")&&(!h()&&0===e.width&&0===e.height||h()&&1===e.width&&1===e.height&&0===e.pressure&&0===e.detail&&"mouse"===e.pointerType||e.width<1&&e.height<1&&0===e.pressure&&0===e.detail&&"touch"===e.pointerType)}function u(){return/apple/i.test(navigator.vendor)}function h(){const e=/android/i;return e.test(s())||e.test(a())}function f(){return s().toLowerCase().startsWith("mac")&&!navigator.maxTouchPoints}function d(e,t){const n=["mouse","pen"];return t||n.push("",void 0),n.includes(e)}function p(e){return"nativeEvent"in e}function m(e){return e.matches("html,body")}function g(e){return(null==e?void 0:e.ownerDocument)||document}function v(e,t){if(null==t)return!1;if("composedPath"in e)return e.composedPath().includes(t);const n=e;return null!=n.target&&t.contains(n.target)}function E(e){return"composedPath"in e?e.composedPath()[0]:e.target}const y="input:not([type='hidden']):not([disabled]),[contenteditable]:not([contenteditable='false']),textarea:not([disabled])";function b(e){return(0,r.isHTMLElement)(e)&&e.matches(y)}function w(e){e.preventDefault(),e.stopPropagation()}function _(e){return!!e&&("combobox"===e.getAttribute("role")&&b(e))}},897010:(e,t,n)=>{"use strict";function r(){return r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},r.apply(this,arguments)}n.d(t,{default:()=>r})},919010:(e,t,n)=>{"use strict";function r(e,t){var n=t&&t.cache?t.cache:u,r=t&&t.serializer?t.serializer:c;return(t&&t.strategy?t.strategy:a)(e,{cache:n,serializer:r})}function i(e,t,n,r){var i,o=null==(i=r)||"number"==typeof i||"boolean"==typeof i?r:n(r),s=t.get(o);return void 0===s&&(s=e.call(this,r),t.set(o,s)),s}function o(e,t,n){var r=Array.prototype.slice.call(arguments,3),i=n(r),o=t.get(i);return void 0===o&&(o=e.apply(this,r),t.set(i,o)),o}function s(e,t,n,r,i){return n.bind(t,e,r,i)}function a(e,t){return s(e,this,1===e.length?i:o,t.cache.create(),t.serializer)}n.d(t,{memoize:()=>r,strategies:()=>h});var c=function(){return JSON.stringify(arguments)};function l(){this.cache=Object.create(null)}l.prototype.get=function(e){return this.cache[e]},l.prototype.set=function(e,t){this.cache[e]=t};var u={create:function(){return new l}},h={variadic:function(e,t){return s(e,this,o,t.cache.create(),t.serializer)},monadic:function(e,t){return s(e,this,i,t.cache.create(),t.serializer)}}},944339:(e,t,n)=>{"use strict";n.d(t,{__assign:()=>o,__extends:()=>i,__rest:()=>s,__spreadArray:()=>a});var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},r(e,t)};function i(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var o=function(){return o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},o.apply(this,arguments)};function s(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]])}return n}Object.create;function a(e,t,n){if(n||2===arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))}Object.create;"function"==typeof SuppressedError&&SuppressedError},951265:(e,t,n)=>{"use strict";function r(e,t){return e.replace(new RegExp("(^|\\s)"+t+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}function i(e,t){e.classList?e.classList.remove(t):"string"==typeof e.className?e.className=r(e.className,t):e.setAttribute("class",r(e.className&&e.className.baseVal||"",t))}n.d(t,{default:()=>i})},964130:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});const r={disabled:!1}},968975:(e,t,n)=>{"use strict";var r;n.d(t,{useFloating:()=>S});var i=n(845212),o=(n(895590),n(612151),n(821046),n(276089)),s=n(556020);n(523546),n(921885);const a={...r||(r=n.t(i,2))},c=a.useInsertionEffect||(e=>e());function l(e){const t=i.useRef((()=>{0}));return c((()=>{t.current=e})),i.useCallback((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)}),[])}const u="ArrowUp",h="ArrowDown",f="ArrowLeft",d="ArrowRight";var p="undefined"!=typeof document?i.useLayoutEffect:i.useEffect;const m=[f,d],g=[u,h];let v=!1,E=0;const y=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+E++;const b=a.useId||function(){const[e,t]=i.useState((()=>v?y():void 0));return p((()=>{null==e&&t(y())}),[]),i.useEffect((()=>{v=!0}),[]),e};function w(){const e=new Map;return{emit(t,n){var r;null==(r=e.get(t))||r.forEach((e=>e(n)))},on(t,n){e.set(t,[...e.get(t)||[],n])},off(t,n){var r;e.set(t,(null==(r=e.get(t))?void 0:r.filter((e=>e!==n)))||[])}}}const _=i.createContext(null),T=i.createContext(null),x=()=>{var e;return(null==(e=i.useContext(_))?void 0:e.id)||null},A=()=>i.useContext(T);function S(e){void 0===e&&(e={});const{nodeId:t}=e,n=function(e){const{open:t=!1,onOpenChange:n,elements:r}=e,o=b(),s=i.useRef({}),[a]=i.useState((()=>w())),c=null!=x(),[u,h]=i.useState(r.reference),f=l(((e,t,r)=>{s.current.openEvent=e?t:void 0,a.emit("openchange",{open:e,event:t,reason:r,nested:c}),null==n||n(e,t,r)})),d=i.useMemo((()=>({setPositionReference:h})),[]),p=i.useMemo((()=>({reference:u||r.reference||null,floating:r.floating||null,domReference:r.reference})),[u,r.reference,r.floating]);return i.useMemo((()=>({dataRef:s,open:t,onOpenChange:f,elements:p,events:a,floatingId:o,refs:d})),[t,f,p,a,o,d])}({...e,elements:{reference:null,floating:null,...e.elements}}),r=e.rootContext||n,a=r.elements,[c,u]=i.useState(null),[h,f]=i.useState(null),d=(null==a?void 0:a.reference)||c,m=i.useRef(null),g=A();p((()=>{d&&(m.current=d)}),[d]);const v=(0,s.useFloating)({...e,elements:{...a,...h&&{reference:h}}}),E=i.useCallback((e=>{const t=(0,o.isElement)(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),contextElement:e}:e;f(t),v.refs.setReference(t)}),[v.refs]),y=i.useCallback((e=>{((0,o.isElement)(e)||null===e)&&(m.current=e,u(e)),((0,o.isElement)(v.refs.reference.current)||null===v.refs.reference.current||null!==e&&!(0,o.isElement)(e))&&v.refs.setReference(e)}),[v.refs]),_=i.useMemo((()=>({...v.refs,setReference:y,setPositionReference:E,domReference:m})),[v.refs,y,E]),T=i.useMemo((()=>({...v.elements,domReference:d})),[v.elements,d]),S=i.useMemo((()=>({...v,...r,refs:_,elements:T,nodeId:t})),[v,_,T,t,r]);return p((()=>{r.dataRef.current.floatingContext=S;const e=null==g?void 0:g.nodesRef.current.find((e=>e.id===t));e&&(e.context=S)})),i.useMemo((()=>({...v,context:S,refs:_,elements:T})),[v,_,T,S])}},982828:(e,t,n)=>{"use strict";function r(e,t){return r=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},r(e,t)}n.d(t,{default:()=>r})},999208:function(e,t,n){var r;!function(e,i,o){function s(e){var t=this,n="";t.next=function(){var e=t.b,n=t.c,r=t.d,i=t.a;return e=e<<25^e>>>7^n,n=n-r|0,r=r<<24^r>>>8^i,i=i-e|0,t.b=e=e<<20^e>>>12^n,t.c=n=n-r|0,t.d=r<<16^n>>>16^i,t.a=i-e|0},t.a=0,t.b=0,t.c=-1640531527,t.d=1367130551,e===Math.floor(e)?(t.a=e/4294967296|0,t.b=0|e):n+=e;for(var r=0;r<n.length+20;r++)t.b^=0|n.charCodeAt(r),t.next()}function a(e,t){return t.a=e.a,t.b=e.b,t.c=e.c,t.d=e.d,t}function c(e,t){var n=new s(e),r=t&&t.state,i=function(){return(n.next()>>>0)/4294967296};return i.double=function(){do{var e=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},i.int32=n.next,i.quick=i,r&&("object"==typeof r&&a(r,n),i.state=function(){return a(n,{})}),i}i&&i.exports?i.exports=c:n.amdD&&n.amdO?void 0===(r=function(){return c}.call(t,n,t,i))||(i.exports=r):this.tychei=c}(0,e=n.nmd(e),n.amdD)}}]);
//# sourceMappingURL=sourcemaps/b58f37f200ce9f78.1ul071.vendor.js.map