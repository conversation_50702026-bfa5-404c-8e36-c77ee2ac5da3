
--- Page 1 ---
Nutr ient Intak e and plant gr owth
A machine lear ning appr oach for pr ediction syst em and analysis o f nutr ients 
uptak e for bett er cr op gr owth in the Hy droponics syst em. [link]
The study uses the dry weight (biomass) of the plant to calculate the Crop Growth Rate (C GR), which indicates how quickly 
and efficiently the plant is growing. Every 2–3 weeks, plants are removed from the system, and their parts—such as leaves, 
stems, and roots—are separated, dried in a forced-draft oven until a constant weight is reached, and then measured. T o 
understand nutrient uptake, ion concentrations are analyzed using ion chromatography. While Electrical Conductivity (EC) is 
commonly used to monitor nutrient levels, the study emphasizes that EC alone is not sufficient because it does not provide 
information about specific nutrient ions.
Machine lear ning-b ased analysis o f nutr ient and wat er uptak e in 
hydroponically gr own soybeans [link]
In this study, soybean plants were grown in a hydroponic system using a 50% Hoagland nutrient solution. T o understand 
how each nutrient affects plant behavior, the researchers changed the levels of one macronutrient at a time—nitrogen (100, 
175, 250 ppm), potassium (100, 200, 300 ppm), or magnesium (30, 50, 70 ppm). They measured nutrient concentrations on 
Days 1, 4, 8, 18, and 20 using chemical analysis. W ater uptake by the plants was recorded during the same period and used 
as an indirect way to estimate growth. T o predict how much water the plants would take up based on current nutrient levels, 
the study used machine learning models including Random Forest, Support V ector R egression (with RBF kernel), and K-
Nearest Neighbors.
Prediction o f Plant Gr owth Using Mamdani Fuzzy Model 
in Hy droponic S ystems
The Mamdani fuzzy inference system is a rule-based decision-making model that leverages fuzzy logic to handle uncertainty 
and imprecision in complex systems. Unlike classical binary logic, it allows input variables to belong to multiple categories 
(e.g., “Low”, “Medium”, “High”) with varying degrees of membership. In agricultural applications, this enables the modeling of 
non-linear relationships between environmental factors (e.g., nutrient concentration, pH, temperature) and plant responses. 
By using a set of expert-defined IF–THEN rules and fuzzy sets, the Mamdani model can provide interpretable and human-
readable predictions of outcomes such as plant growth, even in the absence of large datasets.
