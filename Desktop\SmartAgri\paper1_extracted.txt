
--- Page 1 ---
Measurement: Sensors 25 (2023) 100665
Available online 10 January **************/© 2023 The Authors. Published by Elsevier Ltd. This is an open access article under the CC BY-NC-ND license ( http://creativecommons.org/licenses/by-
nc-nd/4.0/ ).Machine learning based crop growth management in greenhouse 
environment using hydroponics farming techniques 
<PERSON><PERSON>,b, <PERSON><PERSON><PERSON>,* 
aREVA University, Bengaluru, 560064, India 
bBangalore Institute of Technology, Bengaluru, 560004, India 
cSchool of Computer Science and Engineering, REVA University, Bengaluru, 560064, India   
ARTICLE INFO  
Keywords: 
Hydroponics 
IoT 
Machine learning 
Organic farming 
KNN ABSTRACT  
Today more than one billion out of 6.5 billion people in the world are affected by hunger. The significant growth 
in human population inevitably places considerable pressure on food supplies. In traditional farming, farmers 
require good quality soil, natural minerals and also the production relatively consumes more time. The hydro -
ponics smart greenhouse method has been adopted as a result of technological advancements. Hydroponics is a 
technique of growing plants that does not require the use of soil, and the plants in hydroponics grows in a shorter 
duration and have higher yields. In normal hydroponics system, a portion of the greenhouse is used for har-
vesting and the parameters like temperature, and humidity can have an impact on plant growth, and the plants 
are not properly nourished. In this proposed system, in hydroponics for germination organic coconut coir me-
dium is used rather than rock wool, because rock wool is not bio-degradable and composed of volcanic materials. 
In the proposed research of hydroponics, the system is automated on a wide scale covering the entire green house 
with different crops produced in different climatic conditions. The implementation is done on, stable greenhouse 
environment and plants are grown covering the entire greenhouse, under constant optimal conditions which is 
favorable to have maximum yield. The prediction of absolute crop growth rate of leafy vegetables is predicted 
using KNN algorithm under various conditions. The several forms of hydroponics for commercial crop growth 
has been compared and it has been concluded that NFT along with coconut coir medium is best suitable for 
hydroponics and it produces an accuracy of 93%. Macro average, a performance metric for F1 score produces 
33% and weighted average, which represents the number of instances connected to different class labels, pro-
duces an accuracy of 93%.   
1.Introduction 
Agriculture is the backbone of India ’s economy, which is still in its 
early stages. Agriculture faces a number of challenges, including small 
and fragmented land holdings, manures, pesticides, agricultural chem -
icals, and so on. Consumers are also increasingly demanding a nutritious 
diet that is free of chemicals and pesticides. Agricultural lands are now 
being converted to commercial complexes which will have the impact 
on the economy. So the production capacity is declined due to the 
reduction of agricultural lands, climate change, water scarcity, soil 
pollution, and other factors [1]. 
Urban agriculture or farming is one of the main methods that con-
sumes less space due to the lack of agricultural fields. Urban and indoor 
faming have made way for smart procedures and intelligent models because of technological improvement [2]. 
The above said difficulties and demands can be overruled using IoT 
and hydroponics methods and since it is done in a controlled environ -
ment, it may be carried out anywhere, such as balcony, room, terrace, 
and so on, and a large number of plants can be planted in a small space. If 
monitored and controlled properly, this type of agriculture could pro-
duce a high yield. A method is proposed for controlling the necessary 
conditions for hydroponically growing plants, as well as cultivators ’ 
ability to remotely control agriculture via the Internet of Things [3]. 
The Hanging Gardens of Babylon (Iraq) and the Floating Gardens of 
China are two ancient examples of hydroponics. These tactics have been 
employed by people for thousands of years. Although the fundamental 
principles of hydroponics have not changed, contemporary technology 
has enabled us to produce plants that are more quickly, more 
*Corresponding author. 
E-mail addresses: <EMAIL> (V. Mamatha), <EMAIL> (J.C. Kavitha).  
Contents lists available at ScienceDirect 
Measurement: Sensors 
u{�~zkw! s{yo| kro>! ÐÐÐ1�mt ozmont~om�1 m{y2u{�~zk w2yok��~o yoz�/�oz�{ ~�!
https://doi.org/10.1016/j.measen.2023.100665 
Received 28 September 2022; Received in revised form 15 December 2022; Accepted 7 January 2023   

--- Page 2 ---
Measurement: Sensors 25 (2023) 100665
2powerfully, and more healthily. William Frederick Gerick created hy-
droponics, a kind of agriculture that employs water as an inert medium 
to deliver nutrients to plants. The University of California, Berkeley later 
popularized it as an agricultural method. Initially calling the method 
aquaculture, he eventually realized that the term refers to the culture of 
aquatic organisms. Gerick grew tomato vines that were 25 feet tall using 
fertilizer mineral solutions rather than soil. It is safer to consume, since it 
doesn’t use any pesticide [4,5]. 
Machine learning-based aeroponics techniques are used in conjunc -
tion with multiple input parameters to ensure accurate plant growth 
with minimal water usage and nutrient requirements. Smart farming is 
used to reduce complexity and energy inputs while increasing produc -
tivity for higher quality crops with limited resources [5,6]. According to 
the recent research, plants cultivated in the hydroponic method are less 
affected by global warming, allowing more plants to be harvested under 
ideal condition. Plants grown using this technique is unique as plants are 
grown without using soil. The amount of water used is very less that of 
what is used in the conventional method [7,8]. 
The proposed research focuses, emphasizes of hydroponics plants 
grown without using sunlight and soil, and plants in hydroponic system 
are grown in water where the roots are exposed to rich nutrients like 
nitrogen, phosphate, and potassium. The plant growth and yield shall be 
thoroughly examined and monitored on a regular basis considering the 
various parameters. The essential nutrition contents of the plants can be 
monitored continuously which in turn helps to predict the growth of the 
crops. 
The novelty of the research states that 
In a typical hydroponics system, a portion of the greenhouse is used 
for harvesting; however, the temperature and humidity can affect plant 
growth and result in inadequate nutrient intake by the plants. In order to 
achieve the highest yield, the implementation is carried out in a stable 
greenhouse environment where plants are grown by considering the 
entire structure. 
2.Literature survey 
Khan, Saad et al. [1] discussed a hydroponics technique that prom -
ises to produce wholesome, high-quality, locally-grown vegetables and 
fruits that are fresh and residue-free while overcoming the multiple 
manifestations of climate change, fresh water scarcity, and the urgent 
need of the expanding food demand. The merits and drawbacks of hy-
droponics are explored in this review paper. The emphasis is placed on 
the information needed for hydroponic farming, as well as the current 
trend in it and an overview of the key companies dealing with the same. 
Muhammad Ikhsan Sani et al. [2] proposed the design of wireless 
sensor and actuator network. The control system manages the actuators 
such as mist maker and the fan that delivers water moisture. Data 
packets are sent from the microcontroller to the server to transmit data 
from sensors to the internet via GSM/GPRS module. Every 1 min, data is 
transmitted and can be accessed via the website. By installing cameras, 
future research can be directed toward visual monitoring systems. 
Aris Munandar, Hanif Fakhrurroja et al. [3] presented smart hy-
droponic system design, development, and implementation using IoT 
concept, and also the connection to the cloud platform. The important 
challenge is quantity of data that are collected by the sensors. Using 
Agile method, working prototype was developed to minimize the plan-
ning process and the data is displayed on the web using front end. 
Joshitha C et al. [4] used an IoT system to continuously monitors and 
manages the support of UBI dots cloud database. Farmers can have an 
eye on their cultivation even from a remote location. Controlling of pest 
is the major drawback in traditional farming. 
Swapnil Verma et al. [5] depicts the prediction of absolute crop 
growth rate using machine learning techniques such as Random Forest 
and Deep Neural networks. Complexities are reduced considerably in 
order to achieve optimum growth rate or maximizing crop yield. Various 
input variables like electric conductivity, nutrient solution, ion concentration uptake, dry weight of the fruits are considered as more 
important for the feasible growth of hydroponic tomato crop. 
Mahesh S Gour et al. [6] developed a system of Aeroponics with IoT, 
and also interfaced with machine learning. Various artificial intelligence 
techniques are used to detect faults. Machine learning-based aeroponics 
techniques are used in conjunction with multiple input parameters to 
ensure accurate plant growth with minimal water usage and nutrient 
requirements. A mobile application was developed that enables the 
farmers to remotely control and monitor the entire system. 
Srivani P, Yamuna Devi C et al. [7] proposed an automated system by 
integrating the prediction models using, IoT, Machine Learning, Artifi -
cial Intelligence, different cloud, data analytics methods, wireless sensor 
network and many more. The study focuses on the issues that includes 
pest control, recycling and energy conservation, water conservation and 
recycling, and power optimization. Smart farming uses it to increase 
productivity for higher-quality crops while using less resources and 
reducing energy inputs. Different environmental factors were taken into 
consideration when analysing the plant growth. 
R B Hari Krishna et al. [8] proposed an automated system using IoT 
and which has an inbuilt Wi-Fi Module and connected to cloud which 
uses a microcontroller ESP32. Data from the sensors are transmitted to 
the UBIDOTS cloud and viewed via mobile/web applications. Sensors 
are used to provide an artificial luminance, allowing for efficient 
photosynthesis even in low light/at night. According to the research, the 
amount of water used is only 10% of that used in the conventional 
method. 
Manav Mehra et al. [9] proposed a system that uses neural networks 
and Bayesian networks, and two separate machine learning techniques, 
to manage the growth of hydroponic plants. IoT enables machine-to- 
machine communication, negating the need for human involvement. 
The method makes use of a number of input variables to predict plant 
development with excellent accuracy. The plant’s growth will be least 
affected by global warming. A Arduino, Raspberry Pi 3, and Tensor Flow 
were used to generate a case study on the development of tomato plants. 
A system that integrates controlled environment agriculture, Internet 
of Things, Computer vision, and machine learning techniques was pro-
posed by Srinidhi H K et al. [10]. This approach uses continuous testing, 
development, and integration throughout the whole software develop -
ment lifecycle of the project. It enters the consumer market by lowering 
the cost of small grow boxes, enabling anyone to grow their own pre-
mium vegetables. 
Herman et al. [11] proposed a system that monitors and controls the 
hydroponic system based on IoT and Fuzzy Logic. IoT monitors the 
plants nutrition and water needs, fuzzy logic controls the supply of 
nutrition and water. Hydroponics plant grows better when compared to 
the plants that are grown using traditional method. Visual look of plant, 
leaf length and width of lettuce and bokchoy plants are increased better 
when compared with the plants that are grown using traditional manual 
method. 
R.Vidhya et al. [12] suggests about Nutrient Film Technique (NFT) 
method, which provides the requirement for the healthy growth of the 
plants. IoT and different machine learning algorithms are used in this 
NFT technique, which resulted in higher yield compared to traditional 
framing. Waste water is recycled and irrigated to plants again. Con-
sumption of water is less but the initial setup cost is higher. 
Georgios Georgiadis et al. [13] developed a system where sensors are 
used to measure the parameters and the required information are sent to 
IoT platform, which uses data API’s for communication and exchange of 
data. Machine learning can be used which will provide recommenda -
tions to facilitate the workload of professional agronomists. Reposi -
tioning the nodes in new places and reconfiguration of the sensor 
network, due to corrosion, leads to abnormal moisture values. 
Bakhtar Nikita et al. [14] proposed several types of hydroponic 
methods like drip irrigation, deep water cultivation etc. Hydroponics 
can also be used to grow plants and vegetables in spaces like congested 
or crowded areas. Soil less methods and vertical hydroponics were used V. Mamatha and J.C. Kavitha                                                                                                                                                                                                               

--- Page 3 ---
Measurement: Sensors 25 (2023) 100665
3in places where gardening is not possible. Pesticide and insecticides 
usage will be reduced. 
Anurag Shrivastava et al. [15] proposed a novel concept of hydro -
ponics that increases crop, vegetable, and fruit production without the 
need for soil. Rock wool is utilized in farming procedures with water 
pollutants at regular intervals to provide enormous yields in shorten 
cultivation times. The proposed system is deployed with IoT sensors for 
predicting crop health and condition monitoring. Design and imple -
mentation of automated vertical hydro farming methods with IoT plat-
forms are done using big data analytics. In comparison to traditional 
agricultural techniques, vertical hydro farming produces greater 
outcomes. 
Velazquez-Gonzalez, Roberto S., et al. [16] proposed a system where 
precision agriculture is promoted on a small scale by paradigms like the 
Internet of Things and Industry 4.0, which enable the control of vari-
ables like pH, electrical conductivity, and temperature, among others, 
leading to higher production and resource savings. 
Tatas, Konstantinos et al. [17] developed a web based system that 
remotely monitors the greenhouse by making use of sensors. The length 
of plant irrigation is determined by a cutting-edge fuzzy inference en-
gine. To enable off-grid operation, the system is designed to consume 
little power and also the system can tolerate different transient faults 
without needing assistance. 
Hariono, Tholib, and Mukhamad Cahyono Putra et al. [18] used 
ESP8266 microcontroller based system that is used to carry out data 
acquisition on an IOT-based hydroponic plant automation system. Data 
collection is used to track temperature data from the sensor, periodic 
plant growth monitoring based on an image from the ESP32 Cam, and as 
a backup source of energy for hydroponic plants using sonar panels. 
Each sensor ’s collected data is sent to the Firebase real-time database, 
and every time the data changes the database is updated. The dashboard 
page displays the data acquisition results in real-time so that they can be 
easily read by. 
The Research gaps identified for this method are:  
1. Seedling Problems  
2. System clogging  
3. Nutrition Deficiency  4. Sustainability problem  
5. Assessment of crop growth characteristics 
2.1. Proposed RESEARCH 
Machine learning algorithm makes it an easy and smart approach for 
predicting maximum yields [9,10]. Based on the problems in traditional 
farming, a solution is provided by combining hydroponic farming 
methods, and IoT technology which in turn creates a smart controlling 
system that automatically controls plant nutrition and water needs [11, 
17]. Each sensor device can communicate or send data to a cloud server 
that needs to be processed and monitored in real time by utilizing 
internet of things (IOT) technology [12]. 
Greenhouses are enclosed, secure structures with materials such as 
glass, fiber, polythene, that keep harvests from being directly influenced 
by weather conditions. The sensors are used to detect whether the cli-
matic conditions are suitable for harvesting or not which in turn sends 
the signals to the microcontroller, and the actuators to act as needed [13, 
18]. The below Fig. 1 represents the schematic representation of 
different types of hydroponics system.  
1. Wick System: 
The plants are placed directly within perlite or vermiculite, which is 
an absorbent substance. Nylon wicks are wrapped around the plants 
before they are dropped into the nutrient solution. It is the most basic 
technique when compared to others.  
2. Deep Water Culture (DWC): 
Roots of the plants are suspended into the nutrient solution directly, 
which means, the nutrients oxygen, water and nutrient supply is pro-
vided by suspending the roots into the solution where the plants can 
easily absorb it.  
3. Ebb and Flow (flood and drain): 
Fig. 1.Different types of hydroponics system.  V. Mamatha and J.C. Kavitha                                                                                                                                                                                                               

--- Page 4 ---
Measurement: Sensors 25 (2023) 100665
4Plants that are positioned in a sizable grow bed that is denselyfilled 
with a nutrient-rich material like perlite or rockwool. In order to keep 
the nutrient-rich solution from overflowing, it will be flooded onto the 
grow bed until the water is a few inches below the surface of the growth 
medium.  
4. Drip System: 
In a drip system, nutrients are poured into a tube that reaches the 
plant ’s roots directly and keeps them moist.  
5. N.F.T (Nutrient Film Technology): 
Pumping the nutrition solution into sloped tubes enables the extra 
nutrients to drain back into the reservoir. When nutrients are added to 
the channel, they flow over each plant ’s roots as they descend the slope, 
ensuring that the right quantity of nutrients is provided.  
6. Aeroponics: 
The necessary plants will be suspended in the air to be grown. A few 
mist nozzles are located below the plants. These nozzles will spray the 
nutrient solution onto the roots of each plant, which has shown to be a 
very effective hydroponic technique. This technique is comparable to 
the Nutrient film technique. 
2.2. Proposed model 
The crop yield is limited by crop varieties, planting areas, growing 
climate, and environmental conditions. Crop yield decreases as humid -
ity and temperature rises, and can have an effect on plant growth, 
causing the plants to be under-nourished. For example, if crops are only 
grown in one lane inside the greenhouse, crop yield decreases because 
there are no other lanes carrying the same or different crops; Since the 
planting areas are restricted, there is an increase in temperature and 
humidity and this have an effect on plant growth, causing the plants to 
be undernourished. 
In this proposed research, the entire greenhouse is considered for 
crop plantation. Environmental factors such as water, temperature, air, 
total dissolved solid (TDS), pH, humidity, and light conditions inside the 
greenhouse will have a direct impact on the growth of plants. If only a 
portion of the green house is used, the plants will not grow properly even 
if adequate nutrition is provided. This is due to the variations in external 
factors such as temperature and humidity gradients, this will vary 
depending on where the plants are grown and how much empty space 
there is in the green house. Because of the unplanted area, temperature 
and humidity will have an impact on plant growth and in order to 
overcome that the plants are planted covering the entire region of the 
greenhouse. 
Figs 2(a) and (b) shows the schematic view of plants that are grown 
on only one side of the greenhouse. This clearly shows the production of 
low yield, and it is clear that the plants are not properly nourished. 
It has been shown that the plants that has been grown inside this kind 
of environment are healthy and producing more yield. In hydroponics, 
plants are more densely packed together in comparison to the amount of 
land required to grow the same number of plants. 
In the proposed research work, for the process of germination co-
conut coir is used as the medium, since it is an organic medium capable 
of retaining water and oxygen well and promotes healthy root structure 
and plant yields. It can quickly access the nutrients it requires, which 
simulates plant growth. The pH of coco coir is naturally 5.7–6.5. It is 
always suggested to make changes to the pH of your nutrient solution to 
5.8–6.2 before watering when growing in Cocogro. It is biodegradable 
and leaves no trace after its useful. 
The below Fig. 3 shows the view of fully nourished plants covering 
the entire green house. The below Fig. 4 shows the seeds that are sown in grow trays for 
germination, and it’s the beautiful stage of a plant ’s life; it is the moment 
a seed is born and begins to sprout, gradually unfolding into its full 
potential. Use the proper growing medium, close attention is paid to 
Fig. 2a.Portion of Green House partially Planted. (For interpretation of the 
references to colour in this figure legend, the reader is referred to the Web 
version of this article.) 
Fig. 2b.Another partial lane of Green House Plantation. (For interpretation 
of the references to colour in this figure legend, the reader is referred to the 
Web version of this article.) 
Fig. 3.Healthy and Fully nourished plants that covers the entire region of 
Green House. (For interpretation of the references to colour in this figure 
legend, the reader is referred to the Web version of this article.) V. Mamatha and J.C. Kavitha                                                                                                                                                                                                               

--- Page 5 ---
Measurement: Sensors 25 (2023) 100665
5seed coating, keep the growing area humid, and keep it clean, trans -
ferred the sprouts to net pots and growing NFT channels later are taken 
into account. 
The growth substrates used in order to grow the plants are discussed 
in the comparison Table 1. It gives the details of growth substrates for 
plant growth, and lists the advantages and disadvantages. It has been 
clearly shown that coconut coir is a good medium to grow plants and is 
100% organic and safe for human health also. 
A plant ’s basic needs are support, nutrients, protection from pests 
and insects, proper atmospheric conditions, oxygen, and moisture. A 
hydroponic system, which uses inert growing medium such as coconut 
coir, rock wool, and so on, can provide all of these conditions [15]. Each 
hydroponics method has its own set of advantages and disadvantages. 
Wick, Nutrient Film Technique (NFT), Ebb and Flow, Drip Systems, 
Deep Water Culture (DWC), and Aeroponics, are the six major categories 
of hydroponic systems used for growing plants. The most commonly 
used method for growing leafy green plants is Nutrient Film Technique 
(NFT). Herbs suitable for growing in NFT include cilantro, parsley, basil 
etc. Nutrient film systems save water, are low in cost, and require little 
maintenance. It is simple to examine the roots for signs of disease which 
can reduce fungal risk because of the constant flow. 
The proposed model diagram is shown in the below Fig. 5. 
3.Experimental results and discussion  
1. Data Collection: 
Data set is collected from University of Agriculture Science GKVK, 
Bangalore [20]. 
https://uasbangalore.edu.in/index.php/kannada-uas . The process of data analysis is performed by gathering data from 
various sensors that detect the surroundings, ambient temperature, pH 
level, water temperature, electrical conductivity and air temperature/ 
humidity sensor in a solution [ [14]][ [16,19]]. Because pH is not 
constant and varies from crop to crop, the pH sensor is used to detect 
changes in nutrition and detects water solvent acidity level. A temper -
ature sensor is used to determine the moisture level. Water temperature 
sensors are used to determine the amount of nutrients in the water. 
Electrical conductivity measurements can be used to monitor and con-
trol nutrient solution concentration. 
The data values that are collected from different growth substrate 
shows different accuracies when it is implemented using K-Nearest 
Neighbor (KNN) algorithm. It has been shown that Coconut fiber along 
with NFT techniques outperforms the best when compared to all other 
techniques. The two substrates (Coconut fiber and Rockwool) are 
considered for predicting the accuracy which is shown in Table 3.  
2. Performance Analysis 
Metrics like Accuracy, Precision, Recall, and F1 Score, can be used to 
gauge how accurate machine learning algorithms are. A table with a 
certain layout that enables the visualization of an algorithm ’s perfor -
mance in the field of machine learning, specifically the statistical 
Fig. 4.Seeds sown for germination.  
Table 1 
Growth substrate for plant growth and its advantages and disadvantages in 
hydroponics system.  
Substrates Advantages Disadvantage s 
Coconut Coir 100% organic, High water holding 
capacity, Acceptable PH and EC Too salty 
Vermiculit e and 
Perlite Lightweight 610 lbs/ft3 Vermiculite: 
Waterlogging is rapid 
Perlite: Retained water 
is poor quality 
Gravel Inexpensive, Easy to clean, 
waterlogging is low Heavy, 
Dries quickly 
Rockwool 
(Volcanic 
Materials) Lightweight Expensive 
Saw Dust High water retention, Light weight Tend to clot  Table 2 
Confusion matrix.  
Actual Values 
Predicted values  Positive (1) Negative (0) 
Positive (1) True Positive (TP) False Positive (FP) 
Negative (0) False Negative (FN) True Negative (TN)  
Fig. 5.Proposed model diagram of hydroponics System.  
Table 3 
Growth media and their Accuracies.  
Media Techniques  
Wick 
system 
(%) DWC 
(%) NFT 
(%) EBB and 
Flow 
(%) Drip 
System 
(%) Aero 
ponics 
(%) 
Coconut 
Fiber 60 92.3 93.3 65 52 45 
Rock 
wool 60 72.3 79.3 85 82 85  V. Mamatha and J.C. Kavitha                                                                                                                                                                                                               

--- Page 6 ---
Measurement: Sensors 25 (2023) 100665
6classification issue, is known as an error matrix or confusion matrix. A 
table called a confusion matrix is used to describe how well a classifi -
cation system performs and the effectiveness of a classification algo-
rithm is represented and condensed in a confusion matrix. The structure 
of a confusion matrix is given in the below Table 2.  
≡The true positive rate of a classifier is computed by dividing the total 
number of positives by the proportion of positives that were correctly 
categorized.  
≡By dividing the total number of improperly categorized negatives by 
the total number of negatives, one can get a classifier ’s false positive 
rate.  
≡By accurately calculating the number of positive classes, the recall 
formula is successful.  
≡The number of estimated classes is calculated using the accuracy 
formula, and the answer is manifestly positive. 
Accuracy: 
The total number of positives and negatives accurately classified by 
the total number of samples yields an approximation of a classifier ’s 
overall accuracy. 
AccuracyTPTN
TPTNFPFN
Recall: 
The number of accurate positive predictions made out of all potential 
positive predictions is measured by recall. The model ’s recall gauges 
how well it can identify positive samples. The recall increases as more 
positive samples are found. 
RecallTP
TPFN
F1 score: 
An accurate performance evaluation score is provided by the F1 
score, which is an accuracy and recall harmonic mean. 
f1score2∗Precision∗Recall
PrecisionRecall2TP
2TPFPFN    
3. Experimental Results 
The experimental findings for categorizing the training and testing 
datasets using the proposed strategy is provided in this section. In order 
to conduct the experiment, 70% of the data samples are utilized for 
training and the remaining 30% are used for testing. 
For high yield production, environmental factors like pH and EC 
values can be used to analyze the plant development rate. As a result, it 
is necessary to use well developed statistical and supervised machine 
learning models to research the connection between these characteris -
tics and plant development and health. To predict plant development 
and health dynamics, these models are created and tested. 
The data values are collected for both the growth substrate and it 
shows different accuracy when it is implemented using K-nearest 
neighboring algorithm. It has been shown that Coconut fibre along with 
NFT techniques outperforms the best when compared to all other 
techniques. 
The Fig. 6 shows that hydroponically grown crops produce higher 
yields than conventional agriculture. Crops grown using entire green -
house produces leafy green crop, plants mature faster and are less sus-
ceptible to pests and diseases. The entire process is maintained under 
optimal environmental conditions for maximum performance. 
The procedure uses a task-specific KNN rule in an end-to-end procedure that employs a graph neural network that predicts the label 
using the instance ’s KNN graph. The neural network of the graph con-
tains implicitly embedded weighting and distance functions. Through 
the usage of a KNN search step from the training data to form a KNN 
graph and passing it through the graph neural network, the prediction 
for a query is obtained. The below Fig. 7 represents the exponential 
graph for the dataset. 
4.Conclusion 
A hydroponic system of farming with technological integration al-
lows growers to cultivate crops in a precise environment. The system ’s 
automation and intelligence have provided a number of advantages for 
this sort of farming in terms of increasing crop production while utilizing 
minimal resources. It includes information on how to use a hydroponics 
system to increase yield while using less water. Organic coconut coir is a 
Fig. 6.Output using KNN.  
Fig. 7.Exponential graph.  V. Mamatha and J.C. Kavitha                                                                                                                                                                                                               

--- Page 7 ---
Measurement: Sensors 25 (2023) 100665
7natural medium that provides excellent water and oxygen retention 
while also promoting plant yields and a healthy root structure. The NFT 
technique is the best commercial hydroponics system for growing plants 
and it has been concluded that growth rate is comparatively higher in 
NFT technique. The growth of plant is directly affected by the envi-
ronment conditions, and if the crops are grown in a portion of a 
greenhouse it decreases the crop yield as the other portion is left empty 
and does not carrying the same or different crops. The production de-
creases mainly due to the increase in humidity and temperature and also 
of fluctuating precipitation. The use of KNN algorithm along with NFT 
technique produces an accuracy of 93% in the prediction of crop growth. 
In this proposed system of research, the experiments are carried out only 
on leafy vegetables; and in future it can be done for fruits which are 
hydroponically grown. The major drawback is setting up a hydroponics 
system costs more when compared to conventional method. It needs 
constant monitoring and maintenance because it is more susceptible to 
power outages. Waterborne diseases have a quicker impact on plants. 
CRediT authorship contribution statement 
V. Mamatha: Writing – original draft, Methodology, Writing – re-
view & editing, Software, Validation. J.C. Kavitha: Conceptualization, 
Supervision, Review. 
Declaration of competing interest 
The authors declare that they have no known competing financial 
interests or personal relationships that could have appeared to influence 
the work reported in this paper. 
Data availability 
The dataset and the images are taken from the University of Agri-
cultural Sciences (GKVK), Bengaluru. 
Acknowledgement 
This research was supported by University of Agricultural Sciences, 
GKVK,Bangalore. 
Dr Vishwanath K, HOD, National Seed Project Univerisity of Agri-
cultural Sciences, Bangalore. 
This research was supported by Reva University, Bangalore. 
References 
[1]Saad Khan, Ankit Purohit, Nikita Vadsaria, Hydroponics: current and future state of 
the art in farming, J. Plant Nutr. 44 (10) (2020) 1515, 1538 . [2]Muhammad Ikhsan Sani, Aris Pujud Kurniawan, Web- based monitoring and 
control system for aeroponics growing chamber, International Conference on 
Control, Electronics, Renewable Energy and Communications, 978-1-5090-0744-8/ 
16/2016 (IEEE). 
[3]Aris Munandar, Hanif Fakhrurroja, Irfan F. A. Anto, Design and Development of an 
IoT Based Smart Hydroponic System, (ISRITI), 978-1-5386-7422- 2/18/2018 IEEE. 
[4]Ms Swapnil Verma, DrSushopti D.Gawade, A machine learning approach for 
prediction system and analysis of nutrients uptake for better crop growth in the 
Hydroponics system. International Conference on Artificial Intelligence and Smart 
Systems (ICAIS-2021),IEEE Xplore Part Number: CFP21OAB-ART; ISBN: 978-1- 
7281-9537-7. 
[5]Joshitha C, P Kanakaraja, KovurSarath Kumar, Polavarapu A kanksha, Guduru 
Satish, An Eye on Hydroponics: the IoT Initiative,7th International Conference on 
Electrical Energy Systems, 978-1-7281- 7612-3/20/62021 IEEE DOI: 10.1109/ 
ICEES51510.2021.9383694. 
[6]Mahesh S. Gour, Mr Vittal Reddy, IoT Based Arming Techniques in Indoor 
Environment: A Brief Survey, Fifth International Conference on Communication 
and Electronics Systems (ICCES 2020) IEEE Conference Record # 48766, IEEE 
Xplore, 2020, ISBN 978-1-7281 5371-1 . 
[7]Srivani P, Yamuna Devi C, A controlled environment agriculture with hydroponics: 
variants, parameters, methodologies and challenges for smart farming, Fifteenth 
International Conference on Information Processing, 978-1-7281-3807-7/2019 
IEEE. 
[8]R B Harikrishna, Suraj R, ParamasivaPandi N, Greenhouse Automation Using 
Internet of Things in Hydroponics2021 3rd International Conference on Signal 
Processing and Communication, 978-1-6654-2864-4/21/$31.00 ©2021 IEEE. 
[9]486 Manav Mehra, Sameer Saxena, Sankaranarayanan Suresh, IoT based 
hydroponics system using Deep Neural Networks, 2018, Comput. Electron. Agric. 
155 (9 October 2018) 473 (Elsevier) . 
[10] M Srinidhi H K, Shreenidhi H S, Smart Hydroponics system integrating with IoT 
and Machine learning algorithm, International Conference on Recent Trends on 
Electronics, Information, Communication & Technology, 978-1-7281-9772-2/2020 
IEEE | DOI: 10.1109/RTEICT49044.2020.9315549. 
[11] Nico Surantha Herman, Intelligent monitoring and controlling system for 
hydroponics precision agriculture, in: 7th International Conference on Information 
and Communication Technology, IEEE, 2019, 978-1- 5386-8052-0 . 
[12] R.Vidhya, DrK.Valarmathi, Survey on automatic monitoring of hydroponics farms 
using IoT, International Conference on Communication and Electronics Systems, 
978-1-5386-4765-3/18/2018 IEEE. 
[13] Georgios Georgiadis, Andreas Komninos, Andreas Koskeris, John Garofalakis, 
Implementing an Integrated IoT for Hydroponic Agriculture, Springer Nature 
Switzerland AG, 2021, https://doi.org/10.1007/978-3-030-67197-6_5 . Data 
Science and Internet of Things. 
[14] Nikita Bakhtar, et al., IoT based hydroponic farm, in: International Conference on 
Smart Systems and Inventive Technology (ICSSIT), IEEE, 2018, 2018 . 
[15] Shrivastava Anurag, et al., Automatic robotic system design and development for 
vertical hydroponic farming using IoT and big data analysis, Mater. Today Proc. 
(2021). https://doi.org/10.1016/j.matpr.2021.07.294.2214-7853/ ˘O. 
[16] Gonzalez Velazquez, S. Roberto, et al., A review on hydroponics and the 
technologies associated for medium-and small-scale operations, Agriculture 12 5 
(2022) 646. 
[17] Konstantinos Tatas, et al., Reliable IoT-based monitoring and control of hydroponic 
systems, Technologies 10 1 (2022) 26. 
[18] Tholib Hariono, Mukhamad Cahyono Putra, Data acquisition for monitoring IoT- 
based hydroponic automation system using ESP8266, Newton: Netw. Inform. 
Technol. 1 (1) (2021) 1–7. 
[19] T. Karthick, M. Sangeetha, M. Ramprasath, K. Ananthajothi, Continuous Activity- 
Aware Stress Detection Using Sensors, Wireless Personal Communication, 2021, 
https://doi.org/10.1007/s11277-021-08791-8 . 
[20] https://uasbangalore.edu.in/index.php/kannada-uas . V. Mamatha and J.C. Kavitha                                                                                                                                                                                                               
