import PyPDF2
import sys

def extract_text_from_pdf(pdf_path, output_path):
    try:
        # Open the PDF file
        with open(pdf_path, 'rb') as file:
            # Create a PDF reader object
            pdf_reader = PyPDF2.PdfReader(file)
            
            # Initialize text variable
            extracted_text = ""
            
            # Extract text from each page
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text = page.extract_text()
                extracted_text += f"\n--- Page {page_num + 1} ---\n"
                extracted_text += text
                extracted_text += "\n"
            
            # Write extracted text to output file
            with open(output_path, 'w', encoding='utf-8') as output_file:
                output_file.write(extracted_text)
            
            print(f"Text successfully extracted from {pdf_path}")
            print(f"Output saved to {output_path}")
            print(f"Total pages processed: {len(pdf_reader.pages)}")
            
    except Exception as e:
        print(f"Error extracting text from PDF: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    pdf_file = "NPK_and_plant_growth[1].pdf"
    output_file = "NPK_and_plant_growth_extracted.txt"
    
    extract_text_from_pdf(pdf_file, output_file)
